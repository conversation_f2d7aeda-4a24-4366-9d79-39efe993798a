#!/bin/bash

# Redis问题排查脚本
# 用法: ./redis_debug.sh

echo "=========================================="
echo "Redis 问题排查脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令不存在"
        return 1
    fi
    return 0
}

# 1. 检查Docker环境
echo -e "\n${BLUE}=== 1. 检查Docker环境 ===${NC}"
if ! check_command docker; then
    log_error "Docker未安装或不在PATH中"
    exit 1
fi

log_info "Docker版本:"
docker --version

# 2. 检查Redis容器状态
echo -e "\n${BLUE}=== 2. 检查Redis容器状态 ===${NC}"
log_info "查找Redis容器..."
REDIS_CONTAINER=$(docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep redis)

if [ -z "$REDIS_CONTAINER" ]; then
    log_error "未找到Redis容器"
    log_info "所有容器列表:"
    docker ps -a
    exit 1
else
    log_success "找到Redis容器:"
    echo "$REDIS_CONTAINER"
fi

# 获取Redis容器名称
REDIS_NAME=$(docker ps --format "{{.Names}}" | grep redis | head -1)
if [ -z "$REDIS_NAME" ]; then
    log_error "Redis容器未运行"
    log_info "尝试启动Redis容器..."
    docker start $(docker ps -a --format "{{.Names}}" | grep redis | head -1)
    sleep 3
    REDIS_NAME=$(docker ps --format "{{.Names}}" | grep redis | head -1)
fi

log_info "使用Redis容器: $REDIS_NAME"

# 3. 检查Redis容器日志
echo -e "\n${BLUE}=== 3. 检查Redis容器日志 ===${NC}"
log_info "Redis容器最近日志:"
docker logs $REDIS_NAME --tail=20

# 4. 测试Redis连接
echo -e "\n${BLUE}=== 4. 测试Redis连接 ===${NC}"
log_info "测试Redis PING..."
if docker exec $REDIS_NAME redis-cli ping > /dev/null 2>&1; then
    log_success "Redis PING 成功"
else
    log_error "Redis PING 失败"
    log_info "尝试详细连接测试..."
    docker exec $REDIS_NAME redis-cli ping
fi

# 5. 检查Redis配置
echo -e "\n${BLUE}=== 5. 检查Redis配置 ===${NC}"
log_info "Redis服务器信息:"
docker exec $REDIS_NAME redis-cli info server | head -10

log_info "Redis内存信息:"
docker exec $REDIS_NAME redis-cli info memory | grep -E "(used_memory|maxmemory)"

log_info "Redis客户端连接信息:"
docker exec $REDIS_NAME redis-cli info clients

log_info "Redis超时配置:"
docker exec $REDIS_NAME redis-cli CONFIG GET "*timeout*"

# 6. 检查网络连接
echo -e "\n${BLUE}=== 6. 检查网络连接 ===${NC}"
API_CONTAINER=$(docker ps --format "{{.Names}}" | grep api | head -1)
if [ -n "$API_CONTAINER" ]; then
    log_info "从API容器测试到Redis的连接..."
    log_info "API容器: $API_CONTAINER"
    
    log_info "测试网络连通性:"
    if docker exec $API_CONTAINER ping -c 2 $REDIS_NAME > /dev/null 2>&1; then
        log_success "网络连通性正常"
    else
        log_error "网络连通性异常"
    fi
    
    log_info "测试Redis端口连通性:"
    docker exec $API_CONTAINER timeout 5 bash -c "echo > /dev/tcp/$REDIS_NAME/6379" 2>/dev/null && log_success "Redis端口连通" || log_error "Redis端口不通"
else
    log_warning "未找到API容器，跳过网络测试"
fi

# 7. 检查环境变量
echo -e "\n${BLUE}=== 7. 检查环境变量 ===${NC}"
if [ -n "$API_CONTAINER" ]; then
    log_info "API容器中的Redis相关环境变量:"
    docker exec $API_CONTAINER env | grep -i redis || log_warning "未找到Redis环境变量"
fi

# 8. 检查系统资源
echo -e "\n${BLUE}=== 8. 检查系统资源 ===${NC}"
log_info "容器资源使用情况:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

log_info "主机内存使用:"
free -h

log_info "磁盘使用:"
df -h | head -5

# 9. Redis性能测试
echo -e "\n${BLUE}=== 9. Redis性能测试 ===${NC}"
log_info "执行简单的Redis性能测试..."
docker exec $REDIS_NAME redis-cli --latency-history -i 1 -c 5 2>/dev/null || log_warning "性能测试失败"

# 10. 生成诊断报告
echo -e "\n${BLUE}=== 10. 诊断总结 ===${NC}"
log_info "诊断完成，请检查以上输出中的错误信息"

# 常见问题建议
echo -e "\n${YELLOW}=== 常见问题解决建议 ===${NC}"
echo "1. 如果Redis PING失败："
echo "   - 重启Redis容器: docker restart $REDIS_NAME"
echo "   - 检查Redis配置文件"
echo ""
echo "2. 如果网络连接失败："
echo "   - 检查Docker网络: docker network ls"
echo "   - 重启相关容器"
echo ""
echo "3. 如果内存不足："
echo "   - 清理Redis数据: docker exec $REDIS_NAME redis-cli FLUSHDB"
echo "   - 增加系统内存"
echo ""
echo "4. 如果连接数过多："
echo "   - 检查应用程序连接池配置"
echo "   - 重启API容器"

echo -e "\n${GREEN}排查脚本执行完成！${NC}"
