[{"name": "-11:00 Niue Time - Alofi", "value": "Pacific/Niue"}, {"name": "-11:00 Samoa Time - Midway", "value": "Pacific/Midway"}, {"name": "-11:00 Samoa Time - Pago Pago", "value": "Pacific/Pago_Pago"}, {"name": "-10:00 Cook Islands Time - Avarua", "value": "Pacific/Rarotonga"}, {"name": "-10:00 Hawaii-<PERSON><PERSON><PERSON> Time - Adak", "value": "America/Adak"}, {"name": "-10:00 Hawaii-Aleutian Time - Honolulu, East Honolulu, Pearl City, Hilo", "value": "Pacific/Honolulu"}, {"name": "-10:00 Tahiti Time - Faaa, Papeete, Punaauia", "value": "Pacific/Tahiti"}, {"name": "-09:30 Marquesas Time - Marquesas", "value": "Pacific/Marquesas"}, {"name": "-09:00 Alaska Time - Anchorage, Juneau, Fairbanks, Eagle River", "value": "America/Anchorage"}, {"name": "-09:00 Gambier Time - Gambier", "value": "Pacific/Gambier"}, {"name": "-08:00 Pacific Time - Los Angeles, San Diego, San Jose, San Francisco", "value": "America/Los_Angeles"}, {"name": "-08:00 Pacific Time - Tijuana, Mexicali, Ensenada, Rosarito", "value": "America/Tijuana"}, {"name": "-08:00 Pacific Time - Vancouver, Surrey, Okanagan, Victoria", "value": "America/Vancouver"}, {"name": "-08:00 Pitcairn Time - Adamstown", "value": "Pacific/Pitcairn"}, {"name": "-07:00 Mexican Pacific Time - <PERSON><PERSON><PERSON>, Culiacán, Ciudad Obregón, Mazatlán", "value": "America/Hermosillo"}, {"name": "-07:00 Mountain Time - Calgary, Edmonton, Red Deer, Sherwood Park", "value": "America/Edmonton"}, {"name": "-07:00 Mountain Time - Ciudad Juárez", "value": "America/Ciudad_Juarez"}, {"name": "-07:00 Mountain Time - Denver, El Paso, Albuquerque, Colorado Springs", "value": "America/Denver"}, {"name": "-07:00 Mountain Time - Phoenix, Tucson, Mesa, Chandler", "value": "America/Phoenix"}, {"name": "-07:00 Yukon Time - Whitehorse, Fort St. John, C<PERSON>on, <PERSON>", "value": "America/Whitehorse"}, {"name": "-06:00 Central Time - Belize City, San Ignacio, San Pedro, Orange Walk", "value": "America/Belize"}, {"name": "-06:00 Central Time - Chicago, Houston, San Antonio, Dallas", "value": "America/Chicago"}, {"name": "-06:00 Central Time - Guatemala City, Villa Nueva, Mixco, Cobán", "value": "America/Guatemala"}, {"name": "-06:00 Central Time - Managua, León, Masaya, Chinandega", "value": "America/Managua"}, {"name": "-06:00 Central Time - Mexico City, Iztapalapa, León de los Aldama, Puebla", "value": "America/Mexico_City"}, {"name": "-06:00 Central Time - Reynosa, Heroica <PERSON>, Nuevo Laredo, <PERSON>dras <PERSON>", "value": "America/Matamoros"}, {"name": "-06:00 Central Time - San José, Limón, San Francisco, Alajuela", "value": "America/Costa_Rica"}, {"name": "-06:00 Central Time - San Salvador, Soyapango, San Miguel, Santa Ana", "value": "America/El_Salvador"}, {"name": "-06:00 Central Time - Saskatoon, Regina, <PERSON>, <PERSON>", "value": "America/Regina"}, {"name": "-06:00 Central Time - Tegucigalpa, San Pedro Sula, La Ceiba, Choloma", "value": "America/Tegucigalpa"}, {"name": "-06:00 Central Time - Winnipeg, Brandon, <PERSON>, Kenora", "value": "America/Winnipeg"}, {"name": "-06:00 Easter Island Time - Easter", "value": "Pacific/Easter"}, {"name": "-06:00 Galapagos Time - Galapagos", "value": "Pacific/Galapagos"}, {"name": "-05:00 Acre Time - <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "value": "America/Rio_Branco"}, {"name": "-05:00 Colombia Time - Bogotá, Cali, Medellín, Barranquilla", "value": "America/Bogota"}, {"name": "-05:00 Cuba Time - Havana, Santiago de Cuba, Camagüey, Holguín", "value": "America/Havana"}, {"name": "-05:00 Eastern Time - Atikokan", "value": "America/Atikokan"}, {"name": "-05:00 Eastern Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Playa del Carmen, Cozumel", "value": "America/Cancun"}, {"name": "-05:00 Eastern Time - Cockburn Town", "value": "America/Grand_Turk"}, {"name": "-05:00 Eastern Time - George Town, West Bay", "value": "America/Cayman"}, {"name": "-05:00 Eastern Time - Kingston, New Kingston, Spanish Town, Portmore", "value": "America/Jamaica"}, {"name": "-05:00 Eastern Time - Nassau, Lucaya, Freeport", "value": "America/Nassau"}, {"name": "-05:00 Eastern Time - New York City, Brooklyn, Queens, Philadelphia", "value": "America/New_York"}, {"name": "-05:00 Eastern Time - <PERSON>am<PERSON>, <PERSON>, <PERSON>, <PERSON>", "value": "America/Panama"}, {"name": "-05:00 Eastern Time - Port-au-Prince, <PERSON><PERSON><PERSON>, <PERSON><PERSON> 73, Port-de-Paix", "value": "America/Port-au-Prince"}, {"name": "-05:00 Eastern Time - Toronto, Montréal, Ottawa, Mississauga", "value": "America/Toronto"}, {"name": "-05:00 Ecuador Time - Quito, Guayaquil, Cuenca, Santo Domingo de los Colorados", "value": "America/Guayaquil"}, {"name": "-05:00 Peru Time - Lima, Callao, Arequipa, Trujillo", "value": "America/Lima"}, {"name": "-04:00 Amazon Time - Manaus, Campo Grande, Cuiabá, Porto Velho", "value": "America/Manaus"}, {"name": "-04:00 Atlantic Time - Basseterre", "value": "America/St_Kitts"}, {"name": "-04:00 Atlantic Time - <PERSON>-<PERSON>", "value": "America/Blanc-Sablon"}, {"name": "-04:00 Atlantic Time - <PERSON><PERSON>, Plymouth", "value": "America/Montserrat"}, {"name": "-04:00 Atlantic Time - Bridgetown", "value": "America/Barbados"}, {"name": "-04:00 Atlantic Time - Castries", "value": "America/St_Lucia"}, {"name": "-04:00 Atlantic Time - Chaguanas, Mon Repos, San Fernando, Port of Spain", "value": "America/Port_of_Spain"}, {"name": "-04:00 Atlantic Time - Fort-de-France, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>", "value": "America/Martinique"}, {"name": "-04:00 Atlantic Time - Gustav<PERSON>", "value": "America/St_Barthelemy"}, {"name": "-04:00 Atlantic Time - Halifax, Moncton, Sydney, Dartmouth", "value": "America/Halifax"}, {"name": "-04:00 Atlantic Time - <PERSON>", "value": "Atlantic/Bermuda"}, {"name": "-04:00 Atlantic Time - Kingstown, Kingstown Park", "value": "America/St_Vincent"}, {"name": "-04:00 Atlantic Time - Kralendijk", "value": "America/Kralendijk"}, {"name": "-04:00 Atlantic Time - <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>", "value": "America/Guadeloupe"}, {"name": "-04:00 Atlantic Time - Marigot", "value": "America/Marigot"}, {"name": "-04:00 Atlantic Time - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>", "value": "America/Aruba"}, {"name": "-04:00 Atlantic Time - Philipsburg", "value": "America/Lower_Princes"}, {"name": "-04:00 Atlantic Time - Road Town", "value": "America/Tortola"}, {"name": "-04:00 Atlantic Time - <PERSON><PERSON>", "value": "America/Dominica"}, {"name": "-04:00 Atlantic Time - <PERSON>, <PERSON>", "value": "America/St_Thomas"}, {"name": "-04:00 Atlantic Time - Saint George's", "value": "America/Grenada"}, {"name": "-04:00 Atlantic Time - Saint <PERSON>’s", "value": "America/Antigua"}, {"name": "-04:00 Atlantic Time - San Juan, Bayamón, Carolina, Ponce", "value": "America/Puerto_Rico"}, {"name": "-04:00 Atlantic Time - <PERSON>, Santiago de los Caballeros, <PERSON>, <PERSON>", "value": "America/Santo_Domingo"}, {"name": "-04:00 Atlantic Time - The Valley", "value": "America/Anguilla"}, {"name": "-04:00 Atlantic Time - Thule", "value": "America/Thule"}, {"name": "-04:00 Atlantic Time - <PERSON><PERSON>", "value": "America/Curacao"}, {"name": "-04:00 Bolivia Time - La Paz, Santa Cruz de la Sierra, Cochabamba, Sucre", "value": "America/La_Paz"}, {"name": "-04:00 Chile Time - Santiago, Puente Alto, Antofagasta, Viña del Mar", "value": "America/Santiago"}, {"name": "-04:00 Guyana Time - Georgetown, Linden, New Amsterdam", "value": "America/Guyana"}, {"name": "-04:00 Paraguay Time - Asunción, Ciudad del Este, San Lorenzo, Capiatá", "value": "America/Asuncion"}, {"name": "-04:00 Venezuela Time - Caracas, Maracaibo, Maracay, Valencia", "value": "America/Caracas"}, {"name": "-03:30 Newfoundland Time - St. John's, Mount Pearl, Corner Brook, Conception Bay South", "value": "America/St_Johns"}, {"name": "-03:00 Argentina Time - Buenos Aires, Córdoba, Rosario, Mar del Plata", "value": "America/Argentina/Buenos_Aires"}, {"name": "-03:00 Brasilia Time - São Paulo, Rio de Janeiro, Belo Horizonte, Salvador", "value": "America/Sao_Paulo"}, {"name": "-03:00 Chile Time - <PERSON>, <PERSON><PERSON>", "value": "Antarctica/Palmer"}, {"name": "-03:00 Chile Time - Punta Arenas, Puerto Natales", "value": "America/Punta_Arenas"}, {"name": "-03:00 Falkland Islands Time - <PERSON>", "value": "Atlantic/Stanley"}, {"name": "-03:00 French Guiana Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "America/Cayenne"}, {"name": "-03:00 St. Pierre & Miquelon Time - Saint-Pierre", "value": "America/Miquelon"}, {"name": "-03:00 Suriname Time - Paramaribo, Lelydorp", "value": "America/Paramaribo"}, {"name": "-03:00 Uruguay Time - Montevideo, Salto, Paysandú, Las Piedras", "value": "America/Montevideo"}, {"name": "-03:00 West Greenland Time - Nuuk", "value": "America/Nuuk"}, {"name": "-02:00 <PERSON> - Noronha", "value": "America/Noronha"}, {"name": "-02:00 South Georgia Time - Grytviken", "value": "Atlantic/South_Georgia"}, {"name": "-01:00 Azores Time - Ponta Delgada", "value": "Atlantic/Azores"}, {"name": "-01:00 Cape Verde Time - Praia, Mindelo, Santa Maria, Cova <PERSON>", "value": "Atlantic/Cape_Verde"}, {"name": "-01:00 East Greenland Time - Scoresbysund", "value": "America/Scoresbysund"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON>jan, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Abidjan"}, {"name": "+00:00 Greenwich Mean Time - Accra, Kumasi, Tamale, Takoradi", "value": "Africa/Accra"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Bamako"}, {"name": "+00:00 Greenwich Mean Time - Bissau, Bafatá", "value": "Africa/Bissau"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "value": "Africa/Conakry"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Dakar"}, {"name": "+00:00 Greenwich Mean Time - Danmarkshavn", "value": "America/Danmarkshavn"}, {"name": "+00:00 Greenwich Mean Time - <PERSON>", "value": "Europe/Isle_of_Man"}, {"name": "+00:00 Greenwich Mean Time - Dublin, South Dublin, Cork, Limerick", "value": "Europe/Dublin"}, {"name": "+00:00 Greenwich Mean Time - Freetown, Bo, Kenema, Koidu", "value": "Africa/Freetown"}, {"name": "+00:00 Greenwich Mean Time - Jamestown", "value": "Atlantic/St_Helena"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Lome"}, {"name": "+00:00 Greenwich Mean Time - London, Birmingham, Liverpool, Glasgow", "value": "Europe/London"}, {"name": "+00:00 Greenwich Mean Time - Monrovia, Gbarnga, Kakata, Bensonville", "value": "Africa/Monrovia"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Nouakchott"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Ouagadougou"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Atlantic/Reykjavik"}, {"name": "+00:00 Greenwich Mean Time - Saint Helier", "value": "Europe/Jersey"}, {"name": "+00:00 Greenwich Mean Time - Saint Peter Port", "value": "Europe/Guernsey"}, {"name": "+00:00 Greenwich Mean Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Banju<PERSON>", "value": "Africa/Banjul"}, {"name": "+00:00 Greenwich Mean Time - <PERSON>", "value": "Africa/Sao_Tome"}, {"name": "+00:00 Greenwich Mean Time - Troll", "value": "Antarctica/Troll"}, {"name": "+00:00 Western European Time - Casablanca, Rabat, Fès, Sale", "value": "Africa/Casablanca"}, {"name": "+00:00 Western European Time - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Africa/El_Aaiun"}, {"name": "+00:00 Western European Time - Las Palmas de Gran Canaria, Santa Cruz de Tenerife, La Laguna, Telde", "value": "Atlantic/Canary"}, {"name": "+00:00 Western European Time - Lisbon, Porto, Amadora, Braga", "value": "Europe/Lisbon"}, {"name": "+00:00 Western European Time - Tórshavn", "value": "Atlantic/Faroe"}, {"name": "+01:00 Central Africa Time - Windhoek, Rundu, Walvis Bay, Oshakati", "value": "Africa/Windhoek"}, {"name": "+01:00 Central European Time - Algiers, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Algiers"}, {"name": "+01:00 Central European Time - Amsterdam, Rotterdam, The Hague, Utrecht", "value": "Europe/Amsterdam"}, {"name": "+01:00 Central European Time - Andorra la Vella, les Escaldes", "value": "Europe/Andorra"}, {"name": "+01:00 Central European Time - Belgrade, <PERSON>š, Novi Sad, Zemun", "value": "Europe/Belgrade"}, {"name": "+01:00 Central European Time - Berlin, Hamburg, Munich, Köln", "value": "Europe/Berlin"}, {"name": "+01:00 Central European Time - Bratislava, Košice, Nitra, Prešov", "value": "Europe/Bratislava"}, {"name": "+01:00 Central European Time - Brussels, Antwerpen, Gent, Charleroi", "value": "Europe/Brussels"}, {"name": "+01:00 Central European Time - Budapest, Debrecen, Szeged, Miskolc", "value": "Europe/Budapest"}, {"name": "+01:00 Central European Time - Copenhagen, Århus, Odense, Aalborg", "value": "Europe/Copenhagen"}, {"name": "+01:00 Central European Time - Gibraltar", "value": "Europe/Gibraltar"}, {"name": "+01:00 Central European Time - Ljubljana, Maribor, Kranj, Celje", "value": "Europe/Ljubljana"}, {"name": "+01:00 Central European Time - Longyearbyen", "value": "Arctic/Longyearbyen"}, {"name": "+01:00 Central European Time - Luxembourg, Esch-sur-Alzette, Dudelange", "value": "Europe/Luxembourg"}, {"name": "+01:00 Central European Time - Madrid, Barcelona, Valencia, Sevilla", "value": "Europe/Madrid"}, {"name": "+01:00 Central European Time - Monaco, Monte-Carlo", "value": "Europe/Monaco"}, {"name": "+01:00 Central European Time - Oslo, Bergen, Trondheim, Stavanger", "value": "Europe/Oslo"}, {"name": "+01:00 Central European Time - Paris, Marseille, Lyon, Toulouse", "value": "Europe/Paris"}, {"name": "+01:00 Central European Time - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pljevlja", "value": "Europe/Podgorica"}, {"name": "+01:00 Central European Time - Prague, Brno, Ostrava, Pilsen", "value": "Europe/Prague"}, {"name": "+01:00 Central European Time - Rome, Milan, Naples, Turin", "value": "Europe/Rome"}, {"name": "+01:00 Central European Time - San Marino", "value": "Europe/San_Marino"}, {"name": "+01:00 Central European Time - San Pawl il-Baħar, Birkirkara, Mosta, Sliema", "value": "Europe/Malta"}, {"name": "+01:00 Central European Time - Sarajevo, Banja Luka, Zenica, Tuzla", "value": "Europe/Sarajevo"}, {"name": "+01:00 Central European Time - Skopje, Kumanovo, Prilep, Bitola", "value": "Europe/Skopje"}, {"name": "+01:00 Central European Time - Stockholm, Göteborg, Malmö, Uppsala", "value": "Europe/Stockholm"}, {"name": "+01:00 Central European Time - Tirana, <PERSON><PERSON><PERSON><PERSON>, Elbasan, Vlorë", "value": "Europe/Tirane"}, {"name": "+01:00 Central European Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Tunis"}, {"name": "+01:00 Central European Time - Vaduz", "value": "Europe/Vaduz"}, {"name": "+01:00 Central European Time - Vatican City", "value": "Europe/Vatican"}, {"name": "+01:00 Central European Time - Vienna, Graz, Linz, Favoriten", "value": "Europe/Vienna"}, {"name": "+01:00 Central European Time - Warsaw, Łódź, Kraków, Wrocław", "value": "Europe/Warsaw"}, {"name": "+01:00 Central European Time - Zagreb, Split, Rijeka, Osijek", "value": "Europe/Zagreb"}, {"name": "+01:00 Central European Time - Zürich, Genève, Basel, Lausanne", "value": "Europe/Zurich"}, {"name": "+01:00 West Africa Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Bangui"}, {"name": "+01:00 West Africa Time - Bata, Malabo, Ebebiyin", "value": "Africa/Malabo"}, {"name": "+01:00 West Africa Time - Brazzaville, Pointe-Noire, <PERSON><PERSON><PERSON>, Kayes", "value": "Africa/Brazzaville"}, {"name": "+01:00 West Africa Time - Co<PERSON>ou, Abo<PERSON><PERSON><PERSON><PERSON><PERSON>, Djougou, Porto-Novo", "value": "Africa/Porto-Novo"}, {"name": "+01:00 West Africa Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Douala"}, {"name": "+01:00 West Africa Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Kinshasa"}, {"name": "+01:00 West Africa Time - Lagos, Kano, Ibadan, Port Harcourt", "value": "Africa/Lagos"}, {"name": "+01:00 West Africa Time - Libreville, Port-Gentil, Franceville, Oyem", "value": "Africa/Libreville"}, {"name": "+01:00 West Africa Time - <PERSON><PERSON>, <PERSON><PERSON>, Hu<PERSON>bo, Lobito", "value": "Africa/Luanda"}, {"name": "+01:00 West Africa Time - <PERSON>'<PERSON>, <PERSON>undou, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Ndjamena"}, {"name": "+01:00 West Africa Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Niamey"}, {"name": "+02:00 Central Africa Time - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Bujumbura"}, {"name": "+02:00 Central Africa Time - Gaborone, Francistown, Molepolole, Maun", "value": "Africa/Gaborone"}, {"name": "+02:00 Central Africa Time - <PERSON><PERSON>, Bulawayo, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Harare"}, {"name": "+02:00 Central Africa Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Juba"}, {"name": "+02:00 Central Africa Time - Khartoum, Omdurman, Nyala, Port Sudan", "value": "Africa/Khartoum"}, {"name": "+02:00 Central Africa Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Kigali"}, {"name": "+02:00 Central Africa Time - Lilongwe, Blantyre, Mzuzu, Zomba", "value": "Africa/Blantyre"}, {"name": "+02:00 Central Africa Time - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Lubumbashi"}, {"name": "+02:00 Central Africa Time - Lusaka, Ndola, Kitwe, Chipata", "value": "Africa/Lusaka"}, {"name": "+02:00 Central Africa Time - Maputo, Matola, Nampula, Beira", "value": "Africa/Maputo"}, {"name": "+02:00 Eastern European Time - Athens, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Europe/Athens"}, {"name": "+02:00 Eastern European Time - Beirut, Ra’s <PERSON>rū<PERSON>, Tripoli, Sidon", "value": "Asia/Beirut"}, {"name": "+02:00 Eastern European Time - Bucharest, Sector 3, Iaşi, Sector 6", "value": "Europe/Bucharest"}, {"name": "+02:00 Eastern European Time - Cairo, Alexandria, Giza, Shubrā al Khaymah", "value": "Africa/Cairo"}, {"name": "+02:00 Eastern European Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "value": "Europe/Chisinau"}, {"name": "+02:00 Eastern European Time - East Jerusalem, Gaza, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Asia/Hebron"}, {"name": "+02:00 Eastern European Time - Helsinki, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "value": "Europe/Helsinki"}, {"name": "+02:00 Eastern European Time - Kaliningrad, Chernyakhovsk, Sovetsk, Baltiysk", "value": "Europe/Kaliningrad"}, {"name": "+02:00 Eastern European Time - Kyiv, Kharkiv, Odesa, Dnipro", "value": "Europe/Kyiv"}, {"name": "+02:00 Eastern European Time - Mariehamn", "value": "Europe/Mariehamn"}, {"name": "+02:00 Eastern European Time - Nicosia, Limassol, Larnaca, Stróvolos", "value": "Asia/Nicosia"}, {"name": "+02:00 Eastern European Time - Riga, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Je<PERSON><PERSON>va", "value": "Europe/Riga"}, {"name": "+02:00 Eastern European Time - Sofia, Plovdiv, Varna, Burgas", "value": "Europe/Sofia"}, {"name": "+02:00 Eastern European Time - Tallinn, Tartu, Narva, Pärnu", "value": "Europe/Tallinn"}, {"name": "+02:00 Eastern European Time - Tripoli, Benghazi, Ajdabiya, Mi<PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Tripoli"}, {"name": "+02:00 Eastern European Time - Vilnius, Kaunas, Klaipėda, Šiauliai", "value": "Europe/Vilnius"}, {"name": "+02:00 Israel Time - Jerusalem, Tel Aviv, West Jerusalem, Haifa", "value": "Asia/Jerusalem"}, {"name": "+02:00 South Africa Time - Johannesburg, Cape Town, Durban, Soweto", "value": "Africa/Johannesburg"}, {"name": "+02:00 South Africa Time - <PERSON><PERSON><PERSON>, Mbabane, Lobamba", "value": "Africa/Mbabane"}, {"name": "+02:00 South Africa Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>s <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Maseru"}, {"name": "+03:00 Arabian Time - <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "value": "Asia/Kuwait"}, {"name": "+03:00 Arabian Time - <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>", "value": "Asia/Bahrain"}, {"name": "+03:00 Arabian Time - Baghdad, <PERSON>, <PERSON>, <PERSON><PERSON>", "value": "Asia/Baghdad"}, {"name": "+03:00 Arabian Time - <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>", "value": "Asia/Qatar"}, {"name": "+03:00 Arabian Time - Jeddah, Riyadh, Mecca, Medina", "value": "Asia/Riyadh"}, {"name": "+03:00 Arabian Time - <PERSON><PERSON>, <PERSON>, <PERSON>, Taiz", "value": "Asia/Aden"}, {"name": "+03:00 Asia/Amman - Amman, Zarqa, Irbid, Russeifa", "value": "Asia/Amman"}, {"name": "+03:00 Asia/Damascus - Aleppo, Damascus, Homs, Latakia", "value": "Asia/Damascus"}, {"name": "+03:00 East Africa Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Africa/Addis_Ababa"}, {"name": "+03:00 East Africa Time - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "value": "Indian/Antananarivo"}, {"name": "+03:00 East Africa Time - <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Asmara"}, {"name": "+03:00 East Africa Time - <PERSON>, Mwanza, Zanzibar, <PERSON><PERSON><PERSON>", "value": "Africa/Dar_es_Salaam"}, {"name": "+03:00 East Africa Time - <PERSON><PERSON>bo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Djibouti"}, {"name": "+03:00 East Africa Time - Kampala, Gulu, Lira, Mbarara", "value": "Africa/Kampala"}, {"name": "+03:00 East Africa Time - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "value": "Indian/Mayotte"}, {"name": "+03:00 East Africa Time - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Africa/Mogadishu"}, {"name": "+03:00 East Africa Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Indian/Comoro"}, {"name": "+03:00 East Africa Time - Nairobi, Kakamega, Mombasa, Ruiru", "value": "Africa/Nairobi"}, {"name": "+03:00 Moscow Time - Minsk, Ho<PERSON>el', Hrodna, Mahilyow", "value": "Europe/Minsk"}, {"name": "+03:00 Moscow Time - Moscow, Saint Petersburg, Nizhniy Novgorod, Kazan", "value": "Europe/Moscow"}, {"name": "+03:00 Moscow Time - Sevastopol, Simferopol, <PERSON><PERSON>, Ye<PERSON><PERSON><PERSON><PERSON>", "value": "Europe/Simferopol"}, {"name": "+03:00 Syowa Time - Syowa", "value": "Antarctica/Syowa"}, {"name": "+03:00 Turkey Time - Istanbul, Ankara, Bursa, İzmir", "value": "Europe/Istanbul"}, {"name": "+03:30 Iran Time - Tehran, Mashhad, Isfahan, Karaj", "value": "Asia/Tehran"}, {"name": "+04:00 Armenia Time - Yerevan, Gyumri, Vanadzor, Vagharshapat", "value": "Asia/Yerevan"}, {"name": "+04:00 Azerbaijan Time - Baku, Sumqayıt, Ganja, Lankaran", "value": "Asia/Baku"}, {"name": "+04:00 Georgia Time - Tbilisi, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Asia/Tbilisi"}, {"name": "+04:00 Gulf Time - Dubai, Abu Dhabi, Sharjah, Al Ain City", "value": "Asia/Dubai"}, {"name": "+04:00 Gulf Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ‘Ib<PERSON><PERSON>", "value": "Asia/Muscat"}, {"name": "+04:00 Mauritius Time - <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>-<PERSON>, Curepipe", "value": "Indian/Mauritius"}, {"name": "+04:00 Réunion Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Saint<PERSON><PERSON>", "value": "Indian/Reunion"}, {"name": "+04:00 Samara Time - <PERSON><PERSON>, Saratov, To<PERSON>tti, Izhevsk", "value": "Europe/Samara"}, {"name": "+04:00 Seychelles Time - Victoria", "value": "Indian/Mahe"}, {"name": "+04:30 Afghanistan Time - Kabul, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Asia/Kabul"}, {"name": "+05:00 French Southern & Antarctic Time - Port-aux-Français", "value": "Indian/Kerguelen"}, {"name": "+05:00 Maldives Time - Male", "value": "Indian/Maldives"}, {"name": "+05:00 <PERSON><PERSON> Time - <PERSON><PERSON>", "value": "Antarctica/Mawson"}, {"name": "+05:00 Pakistan Time - Karachi, Lahore, Faisalabad, Rawalpindi", "value": "Asia/Karachi"}, {"name": "+05:00 Tajikistan Time - Dushanbe, Isfara, Istaravshan, Kŭlob", "value": "Asia/Dushanbe"}, {"name": "+05:00 Turkmenistan Time - Ash<PERSON>bat, T<PERSON><PERSON><PERSON><PERSON>t, Daşoguz, <PERSON>", "value": "Asia/Ashgabat"}, {"name": "+05:00 Uzbekistan Time - Tashkent, Namangan, Samarkand, Andijon", "value": "Asia/Tashkent"}, {"name": "+05:00 West Kazakhstan Time - Aktobe, <PERSON><PERSON><PERSON><PERSON><PERSON>, Oral, Atyrau", "value": "Asia/Aqtobe"}, {"name": "+05:00 Yekaterinburg Time - Yekaterinburg, Chelyabinsk, Ufa, Perm", "value": "Asia/Yekaterinburg"}, {"name": "+05:30 India Time - Colombo, Dehiwala-Mount Lavinia, Maharagama, Jaffna", "value": "Asia/Colombo"}, {"name": "+05:30 India Time - Mumbai, Delhi, Bengaluru, Hyderābād", "value": "Asia/Kolkata"}, {"name": "+05:45 Nepal Time - Kathmandu, Bharatpur, Pātan, Birgañj", "value": "Asia/Kathmandu"}, {"name": "+06:00 Bangladesh Time - Dhaka, Chattogram, Khulna, Rangpur", "value": "Asia/Dhaka"}, {"name": "+06:00 Bhutan Time - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Tsirang, Punākha", "value": "Asia/Thimphu"}, {"name": "+06:00 China Time - <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Asia/Urumqi"}, {"name": "+06:00 East Kazakhstan Time - Almaty, Shymkent, Karagandy, Taraz", "value": "Asia/Almaty"}, {"name": "+06:00 Indian Ocean Time - Chagos", "value": "Indian/Chagos"}, {"name": "+06:00 Kyrgyzstan Time - Bishkek, Osh, Jalal-Abad, Karakol", "value": "Asia/Bishkek"}, {"name": "+06:00 Omsk Time - Omsk, Tara, Kalachinsk", "value": "Asia/Omsk"}, {"name": "+06:00 Vostok Time - Vostok", "value": "Antarctica/Vostok"}, {"name": "+06:30 Cocos Islands Time - West Island", "value": "Indian/Cocos"}, {"name": "+06:30 Myanmar Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Asia/Yangon"}, {"name": "+07:00 Christmas Island Time - Flying Fish Cove", "value": "Indian/Christmas"}, {"name": "+07:00 <PERSON> Time - <PERSON>", "value": "Antarctica/Davis"}, {"name": "+07:00 Hovd Time - <PERSON><PERSON><PERSON><PERSON>, Khovd, <PERSON><PERSON><PERSON><PERSON>, Altai", "value": "Asia/Hovd"}, {"name": "+07:00 Indochina Time - Bangkok, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "value": "Asia/Bangkok"}, {"name": "+07:00 Indochina Time - Ho Chi Minh City, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "Asia/Ho_Chi_Minh"}, {"name": "+07:00 Indochina Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Battambang", "value": "Asia/Phnom_Penh"}, {"name": "+07:00 Indochina Time - Vientiane, Savannakhet, Pakse, Thakhèk", "value": "Asia/Vientiane"}, {"name": "+07:00 Novosibirsk Time - Novosibirsk, Krasnoyarsk, Barnaul, Tomsk", "value": "Asia/Novosibirsk"}, {"name": "+07:00 Western Indonesia Time - Jakarta, Surabaya, Bekasi, Bandung", "value": "Asia/Jakarta"}, {"name": "+08:00 Australian Western Time - <PERSON>, Man<PERSON>rah, Bunbury, <PERSON><PERSON><PERSON><PERSON>", "value": "Australia/Perth"}, {"name": "+08:00 Brunei Darussalam Time - Bandar Ser<PERSON>, Kuala Belait, Seria, Tutong", "value": "Asia/Brunei"}, {"name": "+08:00 Central Indonesia Time - Makassar, Samarinda, Denpasar, Balikpapan", "value": "Asia/Makassar"}, {"name": "+08:00 China Time - Macau", "value": "Asia/Macau"}, {"name": "+08:00 China Time - Shanghai, Beijing, Shenzhen, Guangzhou", "value": "Asia/Shanghai"}, {"name": "+08:00 Hong Kong Time - Hong Kong, Kowloon, Victoria, <PERSON><PERSON>", "value": "Asia/Hong_Kong"}, {"name": "+08:00 Irkutsk Time - Irkutsk, Ulan-Ude, Bratsk, Angarsk", "value": "Asia/Irkutsk"}, {"name": "+08:00 Malaysia Time - Kuala Lumpur, Petaling Jaya, Klang, Johor Bahru", "value": "Asia/Kuala_Lumpur"}, {"name": "+08:00 Philippine Time - Quezon City, Davao, Manila, Caloocan City", "value": "Asia/Manila"}, {"name": "+08:00 Singapore Time - Singapore, Woodlands, Geylang, Queenstown Estate", "value": "Asia/Singapore"}, {"name": "+08:00 Taipei Time - Taipei, Kaohsiung, Taichung, Tainan", "value": "Asia/Taipei"}, {"name": "+08:00 Ulaanbaatar Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Asia/Ulaanbaatar"}, {"name": "+08:45 Australian Central Western Time - <PERSON><PERSON><PERSON>", "value": "Australia/Eucla"}, {"name": "+09:00 East Timor Time - Dili, Maliana, Suai, Likisá", "value": "Asia/Dili"}, {"name": "+09:00 Eastern Indonesia Time - Jayapura, Ambon, Sorong, Ternate", "value": "Asia/Jayapura"}, {"name": "+09:00 Japan Time - Tokyo, Yokohama, Osaka, Nagoya", "value": "Asia/Tokyo"}, {"name": "+09:00 Korean Time - <PERSON>yongy<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Asia/Pyongyang"}, {"name": "+09:00 Korean Time - Seoul, Busan, <PERSON><PERSON>on, Daegu", "value": "Asia/Seoul"}, {"name": "+09:00 Palau Time - <PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Pacific/Palau"}, {"name": "+09:00 Yakutsk Time - Chita, Yakutsk, Blagoveshchensk, Belogorsk", "value": "Asia/Chita"}, {"name": "+09:30 Australian Central Time - Adelaide, Adelaide Hills, Mount Gambier, Morphett Vale", "value": "Australia/Adelaide"}, {"name": "+09:30 Australian Central Time - <PERSON>, Alice <PERSON>, <PERSON>ston", "value": "Australia/Darwin"}, {"name": "+10:00 Australian Eastern Time - Brisbane, Gold Coast, Logan City, Townsville", "value": "Australia/Brisbane"}, {"name": "+10:00 Australian Eastern Time - Sydney, Melbourne, Canberra, Newcastle", "value": "Australia/Sydney"}, {"name": "+10:00 Chamorro Time - Dededo Village, Yigo Village, Tamuning-Tumon-Harmon Village, Tamuning", "value": "Pacific/Guam"}, {"name": "+10:00 Chamorro Time - Saipan", "value": "Pacific/Saipan"}, {"name": "+10:00 Chuuk Time - Chuuk", "value": "Pacific/Chuuk"}, {"name": "+10:00 Dumont-d’Urville Time - DumontDUrville", "value": "Antarctica/DumontDUrville"}, {"name": "+10:00 Papua New Guinea Time - Port Moresby, Lae, Mount Hagen, Popondetta", "value": "Pacific/Port_Moresby"}, {"name": "+10:00 Vladivostok Time - Khabarovsk, Vladivostok, Khabarovsk Vtoroy, Komsomolsk-on-Amur", "value": "Asia/Vladivostok"}, {"name": "+10:30 <PERSON> Time - <PERSON>", "value": "Australia/Lord_Howe"}, {"name": "+11:00 Bougainville Time - Arawa", "value": "Pacific/Bougainville"}, {"name": "+11:00 <PERSON> Time - <PERSON>", "value": "Antarctica/Casey"}, {"name": "+11:00 Kosrae Time - Kosrae, Palikir - National Government Center", "value": "Pacific/Kosrae"}, {"name": "+11:00 New Caledonia Time - <PERSON><PERSON><PERSON><PERSON>, Mont-Dore, Dumbéa", "value": "Pacific/Noumea"}, {"name": "+11:00 Norfolk Island Time - Kingston", "value": "Pacific/Norfolk"}, {"name": "+11:00 Sakhalin Time - Yuzhno-Sakhalinsk, Magadan, Korsakov, Kholmsk", "value": "Asia/Sakhalin"}, {"name": "+11:00 Solomon Islands Time - Honiara", "value": "Pacific/Guadalcanal"}, {"name": "+11:00 Vanuatu Time - Port-Vila", "value": "Pacific/Efate"}, {"name": "+12:00 Fiji Time - <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "value": "Pacific/Fiji"}, {"name": "+12:00 Gilbert Islands Time - Tarawa", "value": "Pacific/Tarawa"}, {"name": "+12:00 Marshall Islands Time - <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RMI Capitol", "value": "Pacific/Majuro"}, {"name": "+12:00 Nauru Time - Yaren", "value": "Pacific/Nauru"}, {"name": "+12:00 New Zealand Time - Auckland, Wellington, Christchurch, Manukau City", "value": "Pacific/Auckland"}, {"name": "+12:00 New Zealand Time - Mc<PERSON>ur<PERSON>", "value": "Antarctica/McMurdo"}, {"name": "+12:00 Petropavlovsk-Kamchatski Time - Petropavlovsk-Kamchatsky, Yelizovo, Vilyuchinsk, Anadyr", "value": "Asia/Kamchatka"}, {"name": "+12:00 Tuvalu Time - Funafuti", "value": "Pacific/Funafuti"}, {"name": "+12:00 Wake Island Time - Wake", "value": "Pacific/Wake"}, {"name": "+12:00 Wallis & Futuna Time - Mata-<PERSON>tu", "value": "Pacific/Wallis"}, {"name": "+12:45 Chatham Time - Chatham", "value": "Pacific/Chatham"}, {"name": "+13:00 Apia Time - Apia", "value": "Pacific/Apia"}, {"name": "+13:00 Phoenix Islands Time - <PERSON><PERSON>", "value": "Pacific/Kanton"}, {"name": "+13:00 Tokelau Time - Fakaofo", "value": "Pacific/Fakaofo"}, {"name": "+13:00 Tonga Time - <PERSON><PERSON><PERSON>alofa", "value": "Pacific/Tongatapu"}, {"name": "+14:00 Line Islands Time - Kiritimati", "value": "Pacific/Kiritimati"}]