/* Attention: Generate by code. Don't update by hand!!! */
html[data-theme="light"] {
  --color-components-input-bg-normal: rgb(200 206 218 / 0.25);
  --color-components-input-text-placeholder: #98a2b2;
  --color-components-input-bg-hover: rgb(200 206 218 / 0.14);
  --color-components-input-bg-active: #f9fafb;
  --color-components-input-border-active: #d0d5dc;
  --color-components-input-border-destructive: #fda29b;
  --color-components-input-text-filled: #101828;
  --color-components-input-bg-destructive: #ffffff;
  --color-components-input-bg-disabled: rgb(200 206 218 / 0.14);
  --color-components-input-text-disabled: #d0d5dc;
  --color-components-input-text-filled-disabled: #676f83;
  --color-components-input-border-hover: #d0d5dc;
  --color-components-input-border-active-prompt-1: #0ba5ec;
  --color-components-input-border-active-prompt-2: #155aef;

  --color-components-kbd-bg-gray: rgb(16 24 40 / 0.04);
  --color-components-kbd-bg-white: rgb(255 255 255 / 0.12);

  --color-components-tooltip-bg: rgb(255 255 255 / 0.95);

  --color-components-button-primary-text: #ffffff;
  --color-components-button-primary-bg: #155aef;
  --color-components-button-primary-border: rgb(16 24 40 / 0.04);
  --color-components-button-primary-bg-hover: #004aeb;
  --color-components-button-primary-border-hover: rgb(16 24 40 / 0.08);
  --color-components-button-primary-bg-disabled: rgb(21 90 239 / 0.14);
  --color-components-button-primary-border-disabled: rgb(255 255 255 / 0);
  --color-components-button-primary-text-disabled: rgb(255 255 255 / 0.6);

  --color-components-button-secondary-text: #354052;
  --color-components-button-secondary-text-disabled: rgb(16 24 40 / 0.25);
  --color-components-button-secondary-bg: #ffffff;
  --color-components-button-secondary-bg-hover: #f9fafb;
  --color-components-button-secondary-bg-disabled: #f9fafb;
  --color-components-button-secondary-border: rgb(16 24 40 / 0.14);
  --color-components-button-secondary-border-hover: rgb(16 24 40 / 0.2);
  --color-components-button-secondary-border-disabled: rgb(16 24 40 / 0.04);

  --color-components-button-tertiary-text: #354052;
  --color-components-button-tertiary-text-disabled: rgb(16 24 40 / 0.25);
  --color-components-button-tertiary-bg: #f2f4f7;
  --color-components-button-tertiary-bg-hover: #e9ebf0;
  --color-components-button-tertiary-bg-disabled: #f9fafb;

  --color-components-button-ghost-text: #354052;
  --color-components-button-ghost-text-disabled: rgb(16 24 40 / 0.25);
  --color-components-button-ghost-bg-hover: rgb(200 206 218 / 0.2);

  --color-components-button-destructive-primary-text: #ffffff;
  --color-components-button-destructive-primary-text-disabled: rgb(255 255 255 / 0.6);
  --color-components-button-destructive-primary-bg: #d92d20;
  --color-components-button-destructive-primary-bg-hover: #b42318;
  --color-components-button-destructive-primary-bg-disabled: #fee4e2;
  --color-components-button-destructive-primary-border: rgb(24 24 27 / 0.04);
  --color-components-button-destructive-primary-border-hover: rgb(24 24 27 / 0.08);
  --color-components-button-destructive-primary-border-disabled: rgb(255 255 255 / 0);

  --color-components-button-destructive-secondary-text: #d92d20;
  --color-components-button-destructive-secondary-text-disabled: rgb(240 68 56 / 0.2);
  --color-components-button-destructive-secondary-bg: #ffffff;
  --color-components-button-destructive-secondary-bg-hover: #fef3f2;
  --color-components-button-destructive-secondary-bg-disabled: #fef3f2;
  --color-components-button-destructive-secondary-border: rgb(24 24 27 / 0.08);
  --color-components-button-destructive-secondary-border-hover: rgb(240 68 56 / 0.25);
  --color-components-button-destructive-secondary-border-disabled: rgb(240 68 56 / 0.04);

  --color-components-button-destructive-tertiary-text: #d92d20;
  --color-components-button-destructive-tertiary-text-disabled: rgb(240 68 56 / 0.2);
  --color-components-button-destructive-tertiary-bg: #fee4e2;
  --color-components-button-destructive-tertiary-bg-hover: #fecdca;
  --color-components-button-destructive-tertiary-bg-disabled: rgb(240 68 56 / 0.04);

  --color-components-button-destructive-ghost-text: #d92d20;
  --color-components-button-destructive-ghost-text-disabled: rgb(240 68 56 / 0.2);
  --color-components-button-destructive-ghost-bg-hover: #fee4e2;

  --color-components-button-secondary-accent-text: #155aef;
  --color-components-button-secondary-accent-text-disabled: #b2caff;
  --color-components-button-secondary-accent-bg: #ffffff;
  --color-components-button-secondary-accent-bg-hover: #f2f4f7;
  --color-components-button-secondary-accent-bg-disabled: #f9fafb;
  --color-components-button-secondary-accent-border: rgb(16 24 40 / 0.14);
  --color-components-button-secondary-accent-border-hover: rgb(16 24 40 / 0.14);
  --color-components-button-secondary-accent-border-disabled: rgb(16 24 40 / 0.04);

  --color-components-button-indigo-bg: #444ce7;
  --color-components-button-indigo-bg-hover: #3538cd;
  --color-components-button-indigo-bg-disabled: rgb(97 114 243 / 0.14);

  --color-components-checkbox-icon: #ffffff;
  --color-components-checkbox-icon-disabled: rgb(255 255 255 / 0.5);
  --color-components-checkbox-bg: #155aef;
  --color-components-checkbox-bg-hover: #004aeb;
  --color-components-checkbox-bg-disabled: #f2f4f7;
  --color-components-checkbox-border: #d0d5dc;
  --color-components-checkbox-border-hover: #98a2b2;
  --color-components-checkbox-border-disabled: rgb(24 24 27 / 0.04);
  --color-components-checkbox-bg-unchecked: #ffffff;
  --color-components-checkbox-bg-unchecked-hover: #ffffff;
  --color-components-checkbox-bg-disabled-checked: #b2caff;

  --color-components-radio-border-checked: #155aef;
  --color-components-radio-border-checked-hover: #004aeb;
  --color-components-radio-border-checked-disabled: #b2caff;
  --color-components-radio-bg-disabled: rgb(255 255 255 / 0);
  --color-components-radio-border: #d0d5dc;
  --color-components-radio-border-hover: #98a2b2;
  --color-components-radio-border-disabled: rgb(24 24 27 / 0.04);
  --color-components-radio-bg: rgb(255 255 255 / 0);
  --color-components-radio-bg-hover: rgb(255 255 255 / 0);

  --color-components-toggle-knob: #ffffff;
  --color-components-toggle-knob-disabled: rgb(255 255 255 / 0.95);
  --color-components-toggle-bg: #155aef;
  --color-components-toggle-bg-hover: #004aeb;
  --color-components-toggle-bg-disabled: #d1e0ff;
  --color-components-toggle-bg-unchecked: #e9ebf0;
  --color-components-toggle-bg-unchecked-hover: #d0d5dc;
  --color-components-toggle-bg-unchecked-disabled: #f2f4f7;
  --color-components-toggle-knob-hover: #ffffff;

  --color-components-card-bg: #fcfcfd;
  --color-components-card-border: #ffffff;
  --color-components-card-bg-alt: #ffffff;
  --color-components-card-bg-transparent: rgb(252 252 253 / 0);
  --color-components-card-bg-alt-transparent: rgb(255 255 255 / 0);

  --color-components-menu-item-text: #495464;
  --color-components-menu-item-text-active: #18222f;
  --color-components-menu-item-text-hover: #354052;
  --color-components-menu-item-text-active-accent: #18222f;
  --color-components-menu-item-bg-active: rgb(21 90 239 / 0.08);
  --color-components-menu-item-bg-hover: rgb(200 206 218 / 0.2);

  --color-components-panel-bg: #ffffff;
  --color-components-panel-bg-blur: rgb(255 255 255 / 0.95);
  --color-components-panel-border: rgb(16 24 40 / 0.08);
  --color-components-panel-border-subtle: rgb(16 24 40 / 0.08);
  --color-components-panel-gradient-2: #f9fafb;
  --color-components-panel-gradient-1: #ffffff;
  --color-components-panel-bg-alt: #f9fafb;
  --color-components-panel-on-panel-item-bg: #ffffff;
  --color-components-panel-on-panel-item-bg-hover: #f9fafb;
  --color-components-panel-on-panel-item-bg-alt: #f9fafb;
  --color-components-panel-on-panel-item-bg-transparent: rgb(255 255 255 / 0.95);
  --color-components-panel-on-panel-item-bg-hover-transparent: rgb(249 250 251 / 0);
  --color-components-panel-on-panel-item-bg-destructive-hover-transparent: rgb(254 243 242 / 0);

  --color-components-panel-bg-transparent: rgb(255 255 255 / 0);

  --color-components-main-nav-nav-button-text: #495464;
  --color-components-main-nav-nav-button-text-active: #155aef;
  --color-components-main-nav-nav-button-bg: rgb(255 255 255 / 0);
  --color-components-main-nav-nav-button-bg-active: #fcfcfd;
  --color-components-main-nav-nav-button-border: rgb(255 255 255 / 0.95);
  --color-components-main-nav-nav-button-bg-hover: rgb(16 24 40 / 0.04);

  --color-components-main-nav-nav-user-border: #ffffff;

  --color-components-slider-knob: #ffffff;
  --color-components-slider-knob-hover: #ffffff;
  --color-components-slider-knob-disabled: rgb(255 255 255 / 0.95);
  --color-components-slider-range: #296dff;
  --color-components-slider-track: #e9ebf0;
  --color-components-slider-knob-border-hover: rgb(16 24 40 / 0.2);
  --color-components-slider-knob-border: rgb(16 24 40 / 0.14);

  --color-components-segmented-control-item-active-bg: #ffffff;
  --color-components-segmented-control-item-active-border: #ffffff;
  --color-components-segmented-control-bg-normal: rgb(200 206 218 / 0.2);
  --color-components-segmented-control-item-active-accent-bg: #ffffff;
  --color-components-segmented-control-item-active-accent-border: #ffffff;

  --color-components-option-card-option-bg: #fcfcfd;
  --color-components-option-card-option-selected-bg: #ffffff;
  --color-components-option-card-option-selected-border: #296dff;
  --color-components-option-card-option-border: #e9ebf0;
  --color-components-option-card-option-bg-hover: #ffffff;
  --color-components-option-card-option-border-hover: #d0d5dc;

  --color-components-tab-active: #155aef;

  --color-components-badge-white-to-dark: #ffffff;
  --color-components-badge-status-light-success-bg: #47cd89;
  --color-components-badge-status-light-success-border-inner: #17b26a;
  --color-components-badge-status-light-success-halo: rgb(23 178 106 / 0.25);

  --color-components-badge-status-light-border-outer: #ffffff;
  --color-components-badge-status-light-high-light: rgb(255 255 255 / 0.3);
  --color-components-badge-status-light-warning-bg: #fdb022;
  --color-components-badge-status-light-warning-border-inner: #f79009;
  --color-components-badge-status-light-warning-halo: rgb(247 144 9 / 0.25);

  --color-components-badge-status-light-error-bg: #f97066;
  --color-components-badge-status-light-error-border-inner: #f04438;
  --color-components-badge-status-light-error-halo: rgb(240 68 56 / 0.25);

  --color-components-badge-status-light-normal-bg: #36bffa;
  --color-components-badge-status-light-normal-border-inner: #0ba5ec;
  --color-components-badge-status-light-normal-halo: rgb(11 165 236 / 0.25);

  --color-components-badge-status-light-disabled-bg: #98a2b2;
  --color-components-badge-status-light-disabled-border-inner: #676f83;
  --color-components-badge-status-light-disabled-halo: rgb(16 24 40 / 0.04);

  --color-components-badge-bg-green-soft: rgb(23 178 106 / 0.08);
  --color-components-badge-bg-orange-soft: rgb(247 144 9 / 0.08);
  --color-components-badge-bg-red-soft: rgb(240 68 56 / 0.08);
  --color-components-badge-bg-blue-light-soft: rgb(11 165 236 / 0.08);
  --color-components-badge-bg-gray-soft: rgb(16 24 40 / 0.04);
  --color-components-badge-bg-dimm: rgb(255 255 255 / 0.05);

  --color-components-chart-line: #296dff;
  --color-components-chart-area-1: rgb(21 90 239 / 0.14);
  --color-components-chart-area-2: rgb(21 90 239 / 0.04);
  --color-components-chart-current-1: #155aef;
  --color-components-chart-current-2: #d1e0ff;
  --color-components-chart-bg: #ffffff;

  --color-components-actionbar-bg: rgb(255 255 255 / 0.95);
  --color-components-actionbar-border: rgb(16 24 40 / 0.04);
  --color-components-actionbar-bg-accent: #f5f7ff;
  --color-components-actionbar-border-accent: #b2caff;

  --color-components-dropzone-bg-alt: #f2f4f7;
  --color-components-dropzone-bg: #f9fafb;
  --color-components-dropzone-bg-accent: rgb(21 90 239 / 0.14);
  --color-components-dropzone-border: rgb(16 24 40 / 0.08);
  --color-components-dropzone-border-alt: rgb(16 24 40 / 0.2);
  --color-components-dropzone-border-accent: #84abff;

  --color-components-progress-brand-progress: #296dff;
  --color-components-progress-brand-border: #296dff;
  --color-components-progress-brand-bg: rgb(21 90 239 / 0.04);

  --color-components-progress-white-progress: #ffffff;
  --color-components-progress-white-border: rgb(255 255 255 / 0.95);
  --color-components-progress-white-bg: rgb(255 255 255 / 0.01);

  --color-components-progress-gray-progress: #98a2b2;
  --color-components-progress-gray-border: #98a2b2;
  --color-components-progress-gray-bg: rgb(200 206 218 / 0.02);

  --color-components-progress-warning-progress: #f79009;
  --color-components-progress-warning-border: #f79009;
  --color-components-progress-warning-bg: rgb(247 144 9 / 0.04);

  --color-components-progress-error-progress: #f04438;
  --color-components-progress-error-border: #f04438;
  --color-components-progress-error-bg: rgb(240 68 56 / 0.04);

  --color-components-chat-input-audio-bg: #eff4ff;
  --color-components-chat-input-audio-wave-default: rgb(21 90 239 / 0.2);
  --color-components-chat-input-bg-mask-1: rgb(255 255 255 / 0.01);
  --color-components-chat-input-bg-mask-2: #f2f4f7;
  --color-components-chat-input-border: #ffffff;
  --color-components-chat-input-audio-wave-active: #296dff;
  --color-components-chat-input-audio-bg-alt: #fcfcfd;

  --color-components-avatar-shape-fill-stop-0: #ffffff;
  --color-components-avatar-shape-fill-stop-100: rgb(255 255 255 / 0.9);

  --color-components-avatar-bg-mask-stop-0: rgb(255 255 255 / 0.12);
  --color-components-avatar-bg-mask-stop-100: rgb(255 255 255 / 0.08);

  --color-components-avatar-default-avatar-bg: #d0d5dc;
  --color-components-avatar-mask-darkmode-dimmed: rgb(255 255 255 / 0);

  --color-components-label-gray: #f2f4f7;

  --color-components-premium-badge-blue-bg-stop-0: #5289ff;
  --color-components-premium-badge-blue-bg-stop-100: #155aef;
  --color-components-premium-badge-blue-stroke-stop-0: rgb(255 255 255 / 0.95);
  --color-components-premium-badge-blue-stroke-stop-100: #155aef;
  --color-components-premium-badge-blue-text-stop-0: #f5f7ff;
  --color-components-premium-badge-blue-text-stop-100: #d1e0ff;
  --color-components-premium-badge-blue-glow: #00329e;
  --color-components-premium-badge-blue-bg-stop-0-hover: #296dff;
  --color-components-premium-badge-blue-bg-stop-100-hover: #004aeb;
  --color-components-premium-badge-blue-glow-hover: #84abff;
  --color-components-premium-badge-blue-stroke-stop-0-hover: rgb(255 255 255 / 0.95);
  --color-components-premium-badge-blue-stroke-stop-100-hover: #00329e;

  --color-components-premium-badge-highlight-stop-0: rgb(255 255 255 / 0.12);
  --color-components-premium-badge-highlight-stop-100: rgb(255 255 255 / 0.3);
  --color-components-premium-badge-indigo-bg-stop-0: #8098f9;
  --color-components-premium-badge-indigo-bg-stop-100: #444ce7;
  --color-components-premium-badge-indigo-stroke-stop-0: rgb(255 255 255 / 0.95);
  --color-components-premium-badge-indigo-stroke-stop-100: #6172f3;
  --color-components-premium-badge-indigo-text-stop-0: #f5f8ff;
  --color-components-premium-badge-indigo-text-stop-100: #e0eaff;
  --color-components-premium-badge-indigo-glow: #2d3282;
  --color-components-premium-badge-indigo-glow-hover: #a4bcfd;
  --color-components-premium-badge-indigo-bg-stop-0-hover: #6172f3;
  --color-components-premium-badge-indigo-bg-stop-100-hover: #2d31a6;
  --color-components-premium-badge-indigo-stroke-stop-0-hover: rgb(255 255 255 / 0.95);
  --color-components-premium-badge-indigo-stroke-stop-100-hover: #2d31a6;

  --color-components-premium-badge-grey-bg-stop-0: #98a2b2;
  --color-components-premium-badge-grey-bg-stop-100: #676f83;
  --color-components-premium-badge-grey-stroke-stop-0: rgb(255 255 255 / 0.95);
  --color-components-premium-badge-grey-stroke-stop-100: #676f83;
  --color-components-premium-badge-grey-text-stop-0: #fcfcfd;
  --color-components-premium-badge-grey-text-stop-100: #f2f4f7;
  --color-components-premium-badge-grey-glow: #101828;
  --color-components-premium-badge-grey-glow-hover: #d0d5dc;
  --color-components-premium-badge-grey-bg-stop-0-hover: #676f83;
  --color-components-premium-badge-grey-bg-stop-100-hover: #354052;
  --color-components-premium-badge-grey-stroke-stop-0-hover: rgb(255 255 255 / 0.95);
  --color-components-premium-badge-grey-stroke-stop-100-hover: #354052;

  --color-components-premium-badge-orange-bg-stop-0: #ff692e;
  --color-components-premium-badge-orange-bg-stop-100: #e04f16;
  --color-components-premium-badge-orange-stroke-stop-0: rgb(255 255 255 / 0.95);
  --color-components-premium-badge-orange-stroke-stop-100: #e62e05;
  --color-components-premium-badge-orange-text-stop-0: #fefaf5;
  --color-components-premium-badge-orange-text-stop-100: #fdead7;
  --color-components-premium-badge-orange-glow: #772917;
  --color-components-premium-badge-orange-glow-hover: #f7b27a;
  --color-components-premium-badge-orange-bg-stop-0-hover: #ff4405;
  --color-components-premium-badge-orange-bg-stop-100-hover: #b93815;
  --color-components-premium-badge-orange-stroke-stop-0-hover: rgb(255 255 255 / 0.95);
  --color-components-premium-badge-orange-stroke-stop-100-hover: #bc1b06;

  --color-components-progress-bar-bg: rgb(21 90 239 / 0.04);
  --color-components-progress-bar-progress: rgb(21 90 239 / 0.14);
  --color-components-progress-bar-border: rgb(16 24 40 / 0.04);
  --color-components-progress-bar-progress-solid: #296dff;
  --color-components-progress-bar-progress-highlight: rgb(21 90 239 / 0.2);

  --color-components-icon-bg-red-solid: #d92d20;
  --color-components-icon-bg-rose-solid: #e31b54;
  --color-components-icon-bg-pink-solid: #dd2590;
  --color-components-icon-bg-orange-dark-solid: #ff4405;
  --color-components-icon-bg-yellow-solid: #eaaa08;
  --color-components-icon-bg-green-solid: #4ca30d;
  --color-components-icon-bg-teal-solid: #0e9384;
  --color-components-icon-bg-blue-light-solid: #0ba5ec;
  --color-components-icon-bg-blue-solid: #155aef;
  --color-components-icon-bg-indigo-solid: #444ce7;
  --color-components-icon-bg-violet-solid: #7839ee;
  --color-components-icon-bg-midnight-solid: #828dad;
  --color-components-icon-bg-rose-soft: #fff1f3;
  --color-components-icon-bg-pink-soft: #fdf2fa;
  --color-components-icon-bg-orange-dark-soft: #fff4ed;
  --color-components-icon-bg-yellow-soft: #fefbe8;
  --color-components-icon-bg-green-soft: #f3fee7;
  --color-components-icon-bg-teal-soft: #f0fdf9;
  --color-components-icon-bg-blue-light-soft: #f0f9ff;
  --color-components-icon-bg-blue-soft: #eff4ff;
  --color-components-icon-bg-indigo-soft: #eef4ff;
  --color-components-icon-bg-violet-soft: #f5f3ff;
  --color-components-icon-bg-midnight-soft: #f0f2f5;
  --color-components-icon-bg-red-soft: #fef3f2;
  --color-components-icon-bg-orange-solid: #f79009;
  --color-components-icon-bg-orange-soft: #fffaeb;

  --color-text-primary: #101828;
  --color-text-secondary: #354052;
  --color-text-tertiary: #676f83;
  --color-text-quaternary: rgb(16 24 40 / 0.3);
  --color-text-destructive: #d92d20;
  --color-text-success: #079455;
  --color-text-warning: #dc6803;
  --color-text-destructive-secondary: #f04438;
  --color-text-success-secondary: #17b26a;
  --color-text-warning-secondary: #f79009;
  --color-text-accent: #155aef;
  --color-text-primary-on-surface: #ffffff;
  --color-text-placeholder: #98a2b2;
  --color-text-disabled: #d0d5dc;
  --color-text-accent-secondary: #296dff;
  --color-text-accent-light-mode-only: #155aef;
  --color-text-text-selected: rgb(21 90 239 / 0.14);
  --color-text-secondary-on-surface: rgb(255 255 255 / 0.9);
  --color-text-logo-text: #18222f;
  --color-text-empty-state-icon: #d0d5dc;
  --color-text-inverted: #000000;
  --color-text-inverted-dimmed: rgb(0 0 0 / 0.95);

  --color-background-body: #f2f4f7;
  --color-background-default-subtle: #fcfcfd;
  --color-background-neutral-subtle: #f9fafb;
  --color-background-sidenav-bg: rgb(255 255 255 / 0.8);
  --color-background-default: #ffffff;
  --color-background-soft: #f9fafb;
  --color-background-gradient-bg-fill-chat-bg-1: #f9fafb;
  --color-background-gradient-bg-fill-chat-bg-2: #f2f4f7;
  --color-background-gradient-bg-fill-chat-bubble-bg-1: #ffffff;
  --color-background-gradient-bg-fill-chat-bubble-bg-2: rgb(255 255 255 / 0.6);
  --color-background-gradient-bg-fill-debug-bg-1: rgb(255 255 255 / 0);
  --color-background-gradient-bg-fill-debug-bg-2: rgb(200 206 218 / 0.14);

  --color-background-gradient-mask-gray: rgb(200 206 218 / 0.2);
  --color-background-gradient-mask-transparent: rgb(255 255 255 / 0);
  --color-background-gradient-mask-input-clear-2: rgb(233 235 240 / 0);
  --color-background-gradient-mask-input-clear-1: #e9ebf0;
  --color-background-gradient-mask-transparent-dark: rgb(0 0 0 / 0);
  --color-background-gradient-mask-side-panel-2: rgb(16 24 40 / 0.3);
  --color-background-gradient-mask-side-panel-1: rgb(16 24 40 / 0.02);

  --color-background-default-burn: #e9ebf0;
  --color-background-overlay-fullscreen: rgb(249 250 251 / 0.95);
  --color-background-default-lighter: rgb(255 255 255 / 0.5);
  --color-background-section: #f9fafb;
  --color-background-interaction-from-bg-1: rgb(200 206 218 / 0.2);
  --color-background-interaction-from-bg-2: rgb(200 206 218 / 0.14);
  --color-background-section-burn: #f2f4f7;
  --color-background-default-dodge: #ffffff;
  --color-background-overlay: rgb(16 24 40 / 0.6);
  --color-background-default-dimmed: #e9ebf0;
  --color-background-default-hover: #f9fafb;
  --color-background-overlay-alt: rgb(16 24 40 / 0.4);
  --color-background-surface-white: rgb(255 255 255 / 0.95);
  --color-background-overlay-destructive: rgb(240 68 56 / 0.3);
  --color-background-overlay-backdrop: rgb(242 244 247 / 0.95);
  --color-background-body-transparent: rgb(242 244 247 / 0);

  --color-shadow-shadow-1: rgb(9 9 11 / 0.03);
  --color-shadow-shadow-3: rgb(9 9 11 / 0.05);
  --color-shadow-shadow-4: rgb(9 9 11 / 0.06);
  --color-shadow-shadow-5: rgb(9 9 11 / 0.08);
  --color-shadow-shadow-6: rgb(9 9 11 / 0.1);
  --color-shadow-shadow-7: rgb(9 9 11 / 0.12);
  --color-shadow-shadow-8: rgb(9 9 11 / 0.14);
  --color-shadow-shadow-9: rgb(9 9 11 / 0.18);
  --color-shadow-shadow-2: rgb(9 9 11 / 0.04);
  --color-shadow-shadow-10: rgb(9 9 11 / 0.05);

  --color-workflow-block-border: #ffffff;
  --color-workflow-block-parma-bg: #f2f4f7;
  --color-workflow-block-bg: #fcfcfd;
  --color-workflow-block-bg-transparent: rgb(252 252 253 / 0.9);
  --color-workflow-block-border-highlight: rgb(21 90 239 / 0.14);
  --color-workflow-block-wrapper-bg-1: #e9ebf0;
  --color-workflow-block-wrapper-bg-2: rgb(233 235 240 / 0.2);

  --color-workflow-canvas-workflow-dot-color: rgb(133 133 173 / 0.15);
  --color-workflow-canvas-workflow-bg: #f2f4f7;
  --color-workflow-canvas-workflow-top-bar-1: #f2f4f7;
  --color-workflow-canvas-workflow-top-bar-2: rgb(242 244 247 / 0.24);
  --color-workflow-canvas-canvas-overlay: rgb(242 244 247 / 0.8);

  --color-workflow-link-line-active: #296dff;
  --color-workflow-link-line-normal: #d0d5dc;
  --color-workflow-link-line-handle: #296dff;
  --color-workflow-link-line-normal-transparent: rgb(208 213 220 / 0.2);
  --color-workflow-link-line-failure-active: #f79009;
  --color-workflow-link-line-failure-handle: #f79009;
  --color-workflow-link-line-failure-button-bg: #dc6803;
  --color-workflow-link-line-failure-button-hover: #b54708;

  --color-workflow-link-line-success-active: #17b26a;
  --color-workflow-link-line-success-handle: #17b26a;

  --color-workflow-link-line-error-active: #f04438;
  --color-workflow-link-line-error-handle: #f04438;

  --color-workflow-minimap-bg: #e9ebf0;
  --color-workflow-minimap-block: rgb(200 206 218 / 0.3);

  --color-workflow-display-success-bg: #ecfdf3;
  --color-workflow-display-success-border-1: rgb(23 178 106 / 0.8);
  --color-workflow-display-success-border-2: rgb(23 178 106 / 0.5);
  --color-workflow-display-success-vignette-color: rgb(23 178 106 / 0.2);
  --color-workflow-display-success-bg-line-pattern: rgb(23 178 106 / 0.3);

  --color-workflow-display-glass-1: rgb(255 255 255 / 0.12);
  --color-workflow-display-glass-2: rgb(255 255 255 / 0.5);
  --color-workflow-display-vignette-dark: rgb(0 0 0 / 0.12);
  --color-workflow-display-highlight: rgb(255 255 255 / 0.5);
  --color-workflow-display-outline: rgb(0 0 0 / 0.05);
  --color-workflow-display-error-bg: #fef3f2;
  --color-workflow-display-error-bg-line-pattern: rgb(240 68 56 / 0.3);
  --color-workflow-display-error-border-1: rgb(240 68 56 / 0.8);
  --color-workflow-display-error-border-2: rgb(240 68 56 / 0.5);
  --color-workflow-display-error-vignette-color: rgb(240 68 56 / 0.2);

  --color-workflow-display-warning-bg: #fffaeb;
  --color-workflow-display-warning-bg-line-pattern: rgb(247 144 9 / 0.3);
  --color-workflow-display-warning-border-1: rgb(247 144 9 / 0.8);
  --color-workflow-display-warning-border-2: rgb(247 144 9 / 0.5);
  --color-workflow-display-warning-vignette-color: rgb(247 144 9 / 0.2);

  --color-workflow-display-normal-bg: #f0f9ff;
  --color-workflow-display-normal-bg-line-pattern: rgb(11 165 236 / 0.3);
  --color-workflow-display-normal-border-1: rgb(11 165 236 / 0.8);
  --color-workflow-display-normal-border-2: rgb(11 165 236 / 0.5);
  --color-workflow-display-normal-vignette-color: rgb(11 165 236 / 0.2);

  --color-workflow-display-disabled-bg: #f9fafb;
  --color-workflow-display-disabled-bg-line-pattern: rgb(200 206 218 / 0.3);
  --color-workflow-display-disabled-border-1: rgb(200 206 218 / 0.6);
  --color-workflow-display-disabled-border-2: rgb(200 206 218 / 0.4);
  --color-workflow-display-disabled-vignette-color: rgb(200 206 218 / 0.4);
  --color-workflow-display-disabled-outline: rgb(0 0 0 / 0);

  --color-workflow-workflow-progress-bg-1: rgb(200 206 218 / 0.2);
  --color-workflow-workflow-progress-bg-2: rgb(200 206 218 / 0.04);

  --color-divider-subtle: rgb(16 24 40 / 0.04);
  --color-divider-regular: rgb(16 24 40 / 0.08);
  --color-divider-deep: rgb(16 24 40 / 0.14);
  --color-divider-burn: rgb(16 24 40 / 0.04);
  --color-divider-intense: rgb(16 24 40 / 0.3);
  --color-divider-solid: #d0d5dc;
  --color-divider-solid-alt: #98a2b2;

  --color-state-base-hover: rgb(200 206 218 / 0.2);
  --color-state-base-active: rgb(200 206 218 / 0.4);
  --color-state-base-hover-alt: rgb(200 206 218 / 0.4);
  --color-state-base-handle: rgb(16 24 40 / 0.2);
  --color-state-base-handle-hover: rgb(16 24 40 / 0.3);
  --color-state-base-hover-subtle: rgb(200 206 218 / 0.08);

  --color-state-accent-hover: #eff4ff;
  --color-state-accent-active: rgb(21 90 239 / 0.08);
  --color-state-accent-hover-alt: #d1e0ff;
  --color-state-accent-solid: #296dff;
  --color-state-accent-active-alt: rgb(21 90 239 / 0.14);

  --color-state-destructive-hover: #fef3f2;
  --color-state-destructive-hover-alt: #fee4e2;
  --color-state-destructive-active: #fecdca;
  --color-state-destructive-solid: #f04438;
  --color-state-destructive-border: #fda29b;
  --color-state-destructive-hover-transparent: rgb(254 243 242 / 0);

  --color-state-success-hover: #ecfdf3;
  --color-state-success-hover-alt: #dcfae6;
  --color-state-success-active: #abefc6;
  --color-state-success-solid: #17b26a;

  --color-state-warning-hover: #fffaeb;
  --color-state-warning-hover-alt: #fef0c7;
  --color-state-warning-active: #fedf89;
  --color-state-warning-solid: #f79009;
  --color-state-warning-hover-transparent: rgb(255 250 235 / 0);

  --color-effects-highlight: #ffffff;
  --color-effects-highlight-lightmode-off: rgb(255 255 255 / 0);
  --color-effects-image-frame: #ffffff;
  --color-effects-icon-border: rgb(16 24 40 / 0.08);

  --color-util-colors-orange-dark-orange-dark-50: #fff4ed;
  --color-util-colors-orange-dark-orange-dark-100: #ffe6d5;
  --color-util-colors-orange-dark-orange-dark-200: #ffd6ae;
  --color-util-colors-orange-dark-orange-dark-300: #ff9c66;
  --color-util-colors-orange-dark-orange-dark-400: #ff692e;
  --color-util-colors-orange-dark-orange-dark-500: #ff4405;
  --color-util-colors-orange-dark-orange-dark-600: #e62e05;
  --color-util-colors-orange-dark-orange-dark-700: #bc1b06;

  --color-util-colors-orange-orange-50: #fef6ee;
  --color-util-colors-orange-orange-100: #fdead7;
  --color-util-colors-orange-orange-200: #f9dbaf;
  --color-util-colors-orange-orange-300: #f7b27a;
  --color-util-colors-orange-orange-400: #f38744;
  --color-util-colors-orange-orange-500: #ef6820;
  --color-util-colors-orange-orange-600: #e04f16;
  --color-util-colors-orange-orange-700: #b93815;
  --color-util-colors-orange-orange-100-transparent: rgb(253 234 215 / 0);

  --color-util-colors-pink-pink-50: #fdf2fa;
  --color-util-colors-pink-pink-100: #fce7f6;
  --color-util-colors-pink-pink-200: #fcceee;
  --color-util-colors-pink-pink-300: #faa7e0;
  --color-util-colors-pink-pink-400: #f670c7;
  --color-util-colors-pink-pink-500: #ee46bc;
  --color-util-colors-pink-pink-600: #dd2590;
  --color-util-colors-pink-pink-700: #c11574;

  --color-util-colors-fuchsia-fuchsia-50: #fdf4ff;
  --color-util-colors-fuchsia-fuchsia-100: #fbe8ff;
  --color-util-colors-fuchsia-fuchsia-200: #f6d0fe;
  --color-util-colors-fuchsia-fuchsia-300: #eeaafd;
  --color-util-colors-fuchsia-fuchsia-400: #e478fa;
  --color-util-colors-fuchsia-fuchsia-500: #d444f1;
  --color-util-colors-fuchsia-fuchsia-600: #ba24d5;
  --color-util-colors-fuchsia-fuchsia-700: #9f1ab1;

  --color-util-colors-purple-purple-50: #f4f3ff;
  --color-util-colors-purple-purple-100: #ebe9fe;
  --color-util-colors-purple-purple-200: #d9d6fe;
  --color-util-colors-purple-purple-300: #bdb4fe;
  --color-util-colors-purple-purple-400: #9b8afb;
  --color-util-colors-purple-purple-500: #7a5af8;
  --color-util-colors-purple-purple-600: #6938ef;
  --color-util-colors-purple-purple-700: #5925dc;

  --color-util-colors-indigo-indigo-50: #eef4ff;
  --color-util-colors-indigo-indigo-100: #e0eaff;
  --color-util-colors-indigo-indigo-200: #c7d7fe;
  --color-util-colors-indigo-indigo-300: #a4bcfd;
  --color-util-colors-indigo-indigo-400: #8098f9;
  --color-util-colors-indigo-indigo-500: #6172f3;
  --color-util-colors-indigo-indigo-600: #444ce7;
  --color-util-colors-indigo-indigo-700: #3538cd;

  --color-util-colors-blue-blue-50: #eff8ff;
  --color-util-colors-blue-blue-100: #d1e9ff;
  --color-util-colors-blue-blue-200: #b2ddff;
  --color-util-colors-blue-blue-300: #84caff;
  --color-util-colors-blue-blue-400: #53b1fd;
  --color-util-colors-blue-blue-500: #2e90fa;
  --color-util-colors-blue-blue-600: #1570ef;
  --color-util-colors-blue-blue-700: #175cd3;

  --color-util-colors-blue-light-blue-light-50: #f0f9ff;
  --color-util-colors-blue-light-blue-light-100: #e0f2fe;
  --color-util-colors-blue-light-blue-light-200: #b9e6fe;
  --color-util-colors-blue-light-blue-light-300: #7cd4fd;
  --color-util-colors-blue-light-blue-light-400: #36bffa;
  --color-util-colors-blue-light-blue-light-500: #0ba5ec;
  --color-util-colors-blue-light-blue-light-600: #0086c9;
  --color-util-colors-blue-light-blue-light-700: #026aa2;

  --color-util-colors-gray-blue-gray-blue-50: #f8f9fc;
  --color-util-colors-gray-blue-gray-blue-100: #eaecf5;
  --color-util-colors-gray-blue-gray-blue-200: #d5d9eb;
  --color-util-colors-gray-blue-gray-blue-300: #b3b8db;
  --color-util-colors-gray-blue-gray-blue-400: #717bbc;
  --color-util-colors-gray-blue-gray-blue-500: #4e5ba6;
  --color-util-colors-gray-blue-gray-blue-600: #3e4784;
  --color-util-colors-gray-blue-gray-blue-700: #363f72;

  --color-util-colors-blue-brand-blue-brand-50: #f5f7ff;
  --color-util-colors-blue-brand-blue-brand-100: #d1e0ff;
  --color-util-colors-blue-brand-blue-brand-200: #b2caff;
  --color-util-colors-blue-brand-blue-brand-300: #84abff;
  --color-util-colors-blue-brand-blue-brand-400: #5289ff;
  --color-util-colors-blue-brand-blue-brand-500: #296dff;
  --color-util-colors-blue-brand-blue-brand-600: #155aef;
  --color-util-colors-blue-brand-blue-brand-700: #004aeb;

  --color-util-colors-red-red-50: #fef3f2;
  --color-util-colors-red-red-100: #fee4e2;
  --color-util-colors-red-red-200: #fecdca;
  --color-util-colors-red-red-300: #fda29b;
  --color-util-colors-red-red-400: #f97066;
  --color-util-colors-red-red-500: #f04438;
  --color-util-colors-red-red-600: #d92d20;
  --color-util-colors-red-red-700: #b42318;

  --color-util-colors-green-green-50: #ecfdf3;
  --color-util-colors-green-green-100: #dcfae6;
  --color-util-colors-green-green-200: #abefc6;
  --color-util-colors-green-green-300: #75e0a7;
  --color-util-colors-green-green-400: #47cd89;
  --color-util-colors-green-green-500: #17b26a;
  --color-util-colors-green-green-600: #079455;
  --color-util-colors-green-green-700: #067647;

  --color-util-colors-warning-warning-50: #fffaeb;
  --color-util-colors-warning-warning-100: #fef0c7;
  --color-util-colors-warning-warning-200: #fedf89;
  --color-util-colors-warning-warning-300: #fec84b;
  --color-util-colors-warning-warning-400: #fdb022;
  --color-util-colors-warning-warning-500: #f79009;
  --color-util-colors-warning-warning-600: #dc6803;
  --color-util-colors-warning-warning-700: #b54708;

  --color-util-colors-yellow-yellow-50: #fefbe8;
  --color-util-colors-yellow-yellow-100: #fef7c3;
  --color-util-colors-yellow-yellow-200: #feee95;
  --color-util-colors-yellow-yellow-300: #fde272;
  --color-util-colors-yellow-yellow-400: #fac515;
  --color-util-colors-yellow-yellow-500: #eaaa08;
  --color-util-colors-yellow-yellow-600: #ca8504;
  --color-util-colors-yellow-yellow-700: #a15c07;

  --color-util-colors-teal-teal-50: #f0fdf9;
  --color-util-colors-teal-teal-100: #ccfbef;
  --color-util-colors-teal-teal-200: #99f6e0;
  --color-util-colors-teal-teal-300: #5fe9d0;
  --color-util-colors-teal-teal-400: #2ed3b7;
  --color-util-colors-teal-teal-500: #15b79e;
  --color-util-colors-teal-teal-600: #0e9384;
  --color-util-colors-teal-teal-700: #107569;

  --color-util-colors-cyan-cyan-50: #ecfdff;
  --color-util-colors-cyan-cyan-100: #cff9fe;
  --color-util-colors-cyan-cyan-200: #a5f0fc;
  --color-util-colors-cyan-cyan-300: #67e3f9;
  --color-util-colors-cyan-cyan-400: #22ccee;
  --color-util-colors-cyan-cyan-500: #06aed4;
  --color-util-colors-cyan-cyan-600: #088ab2;
  --color-util-colors-cyan-cyan-700: #0e7090;

  --color-util-colors-violet-violet-50: #f5f3ff;
  --color-util-colors-violet-violet-100: #ece9fe;
  --color-util-colors-violet-violet-200: #ddd6fe;
  --color-util-colors-violet-violet-300: #c3b5fd;
  --color-util-colors-violet-violet-400: #a48afb;
  --color-util-colors-violet-violet-500: #875bf7;
  --color-util-colors-violet-violet-600: #7839ee;
  --color-util-colors-violet-violet-700: #6927da;

  --color-util-colors-gray-gray-50: #f9fafb;
  --color-util-colors-gray-gray-100: #f2f4f7;
  --color-util-colors-gray-gray-200: #e9ebf0;
  --color-util-colors-gray-gray-300: #d0d5dc;
  --color-util-colors-gray-gray-400: #98a2b2;
  --color-util-colors-gray-gray-500: #676f83;
  --color-util-colors-gray-gray-600: #495464;
  --color-util-colors-gray-gray-700: #354052;

  --color-util-colors-green-light-green-light-50: #f3fee7;
  --color-util-colors-green-light-green-light-100: #e3fbcc;
  --color-util-colors-green-light-green-light-200: #d0f8ab;
  --color-util-colors-green-light-green-light-300: #a6ef67;
  --color-util-colors-green-light-green-light-500: #66c61c;
  --color-util-colors-green-light-green-light-400: #85e13a;
  --color-util-colors-green-light-green-light-600: #4ca30d;
  --color-util-colors-green-light-green-light-700: #3b7c0f;

  --color-util-colors-rose-rose-50: #fff1f3;
  --color-util-colors-rose-rose-100: #ffe4e8;
  --color-util-colors-rose-rose-200: #fecdd6;
  --color-util-colors-rose-rose-300: #fea3b4;
  --color-util-colors-rose-rose-400: #fd6f8e;
  --color-util-colors-rose-rose-500: #f63d68;
  --color-util-colors-rose-rose-600: #e31b54;
  --color-util-colors-rose-rose-700: #c01048;

  --color-util-colors-midnight-midnight-50: #fbfbfc;
  --color-util-colors-midnight-midnight-100: #f0f2f5;
  --color-util-colors-midnight-midnight-200: #dfe1ea;
  --color-util-colors-midnight-midnight-300: #c6cbd9;
  --color-util-colors-midnight-midnight-400: #a7aec5;
  --color-util-colors-midnight-midnight-500: #828dad;
  --color-util-colors-midnight-midnight-600: #5d698d;
  --color-util-colors-midnight-midnight-700: #3e465e;

  --color-third-party-LangChain: #1c3c3c;
  --color-third-party-Langfuse: #000000;
  --color-third-party-Github: #1b1f24;
  --color-third-party-Github-tertiary: #1b1f24;
  --color-third-party-Github-secondary: #1b1f24;
  --color-third-party-model-bg-openai: #e3e5e8;
  --color-third-party-model-bg-anthropic: #eeede7;
  --color-third-party-model-bg-default: #f9fafb;

  --color-third-party-aws: #141f2e;
  --color-third-party-aws-alt: #0f1824;

  --color-saas-background: #fcfcfd;
  --color-saas-pricing-grid-bg: rgb(200 206 218 / 0.5);

  --color-dify-logo-dify-logo-blue: #0033ff;
  --color-dify-logo-dify-logo-black: #000000;

}
