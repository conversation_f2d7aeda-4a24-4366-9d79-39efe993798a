/* Attention: Generate by code. Don't update by hand!!! */
const vars = {
  'components-input-bg-normal': 'var(--color-components-input-bg-normal)',
  'components-input-text-placeholder': 'var(--color-components-input-text-placeholder)',
  'components-input-bg-hover': 'var(--color-components-input-bg-hover)',
  'components-input-bg-active': 'var(--color-components-input-bg-active)',
  'components-input-border-active': 'var(--color-components-input-border-active)',
  'components-input-border-destructive': 'var(--color-components-input-border-destructive)',
  'components-input-text-filled': 'var(--color-components-input-text-filled)',
  'components-input-bg-destructive': 'var(--color-components-input-bg-destructive)',
  'components-input-bg-disabled': 'var(--color-components-input-bg-disabled)',
  'components-input-text-disabled': 'var(--color-components-input-text-disabled)',
  'components-input-text-filled-disabled': 'var(--color-components-input-text-filled-disabled)',
  'components-input-border-hover': 'var(--color-components-input-border-hover)',
  'components-input-border-active-prompt-1': 'var(--color-components-input-border-active-prompt-1)',
  'components-input-border-active-prompt-2': 'var(--color-components-input-border-active-prompt-2)',

  'components-kbd-bg-gray': 'var(--color-components-kbd-bg-gray)',
  'components-kbd-bg-white': 'var(--color-components-kbd-bg-white)',

  'components-tooltip-bg': 'var(--color-components-tooltip-bg)',

  'components-button-primary-text': 'var(--color-components-button-primary-text)',
  'components-button-primary-bg': 'var(--color-components-button-primary-bg)',
  'components-button-primary-border': 'var(--color-components-button-primary-border)',
  'components-button-primary-bg-hover': 'var(--color-components-button-primary-bg-hover)',
  'components-button-primary-border-hover': 'var(--color-components-button-primary-border-hover)',
  'components-button-primary-bg-disabled': 'var(--color-components-button-primary-bg-disabled)',
  'components-button-primary-border-disabled': 'var(--color-components-button-primary-border-disabled)',
  'components-button-primary-text-disabled': 'var(--color-components-button-primary-text-disabled)',

  'components-button-secondary-text': 'var(--color-components-button-secondary-text)',
  'components-button-secondary-text-disabled': 'var(--color-components-button-secondary-text-disabled)',
  'components-button-secondary-bg': 'var(--color-components-button-secondary-bg)',
  'components-button-secondary-bg-hover': 'var(--color-components-button-secondary-bg-hover)',
  'components-button-secondary-bg-disabled': 'var(--color-components-button-secondary-bg-disabled)',
  'components-button-secondary-border': 'var(--color-components-button-secondary-border)',
  'components-button-secondary-border-hover': 'var(--color-components-button-secondary-border-hover)',
  'components-button-secondary-border-disabled': 'var(--color-components-button-secondary-border-disabled)',

  'components-button-tertiary-text': 'var(--color-components-button-tertiary-text)',
  'components-button-tertiary-text-disabled': 'var(--color-components-button-tertiary-text-disabled)',
  'components-button-tertiary-bg': 'var(--color-components-button-tertiary-bg)',
  'components-button-tertiary-bg-hover': 'var(--color-components-button-tertiary-bg-hover)',
  'components-button-tertiary-bg-disabled': 'var(--color-components-button-tertiary-bg-disabled)',

  'components-button-ghost-text': 'var(--color-components-button-ghost-text)',
  'components-button-ghost-text-disabled': 'var(--color-components-button-ghost-text-disabled)',
  'components-button-ghost-bg-hover': 'var(--color-components-button-ghost-bg-hover)',

  'components-button-destructive-primary-text': 'var(--color-components-button-destructive-primary-text)',
  'components-button-destructive-primary-text-disabled': 'var(--color-components-button-destructive-primary-text-disabled)',
  'components-button-destructive-primary-bg': 'var(--color-components-button-destructive-primary-bg)',
  'components-button-destructive-primary-bg-hover': 'var(--color-components-button-destructive-primary-bg-hover)',
  'components-button-destructive-primary-bg-disabled': 'var(--color-components-button-destructive-primary-bg-disabled)',
  'components-button-destructive-primary-border': 'var(--color-components-button-destructive-primary-border)',
  'components-button-destructive-primary-border-hover': 'var(--color-components-button-destructive-primary-border-hover)',
  'components-button-destructive-primary-border-disabled': 'var(--color-components-button-destructive-primary-border-disabled)',

  'components-button-destructive-secondary-text': 'var(--color-components-button-destructive-secondary-text)',
  'components-button-destructive-secondary-text-disabled': 'var(--color-components-button-destructive-secondary-text-disabled)',
  'components-button-destructive-secondary-bg': 'var(--color-components-button-destructive-secondary-bg)',
  'components-button-destructive-secondary-bg-hover': 'var(--color-components-button-destructive-secondary-bg-hover)',
  'components-button-destructive-secondary-bg-disabled': 'var(--color-components-button-destructive-secondary-bg-disabled)',
  'components-button-destructive-secondary-border': 'var(--color-components-button-destructive-secondary-border)',
  'components-button-destructive-secondary-border-hover': 'var(--color-components-button-destructive-secondary-border-hover)',
  'components-button-destructive-secondary-border-disabled': 'var(--color-components-button-destructive-secondary-border-disabled)',

  'components-button-destructive-tertiary-text': 'var(--color-components-button-destructive-tertiary-text)',
  'components-button-destructive-tertiary-text-disabled': 'var(--color-components-button-destructive-tertiary-text-disabled)',
  'components-button-destructive-tertiary-bg': 'var(--color-components-button-destructive-tertiary-bg)',
  'components-button-destructive-tertiary-bg-hover': 'var(--color-components-button-destructive-tertiary-bg-hover)',
  'components-button-destructive-tertiary-bg-disabled': 'var(--color-components-button-destructive-tertiary-bg-disabled)',

  'components-button-destructive-ghost-text': 'var(--color-components-button-destructive-ghost-text)',
  'components-button-destructive-ghost-text-disabled': 'var(--color-components-button-destructive-ghost-text-disabled)',
  'components-button-destructive-ghost-bg-hover': 'var(--color-components-button-destructive-ghost-bg-hover)',

  'components-button-secondary-accent-text': 'var(--color-components-button-secondary-accent-text)',
  'components-button-secondary-accent-text-disabled': 'var(--color-components-button-secondary-accent-text-disabled)',
  'components-button-secondary-accent-bg': 'var(--color-components-button-secondary-accent-bg)',
  'components-button-secondary-accent-bg-hover': 'var(--color-components-button-secondary-accent-bg-hover)',
  'components-button-secondary-accent-bg-disabled': 'var(--color-components-button-secondary-accent-bg-disabled)',
  'components-button-secondary-accent-border': 'var(--color-components-button-secondary-accent-border)',
  'components-button-secondary-accent-border-hover': 'var(--color-components-button-secondary-accent-border-hover)',
  'components-button-secondary-accent-border-disabled': 'var(--color-components-button-secondary-accent-border-disabled)',

  'components-button-indigo-bg': 'var(--color-components-button-indigo-bg)',
  'components-button-indigo-bg-hover': 'var(--color-components-button-indigo-bg-hover)',
  'components-button-indigo-bg-disabled': 'var(--color-components-button-indigo-bg-disabled)',

  'components-checkbox-icon': 'var(--color-components-checkbox-icon)',
  'components-checkbox-icon-disabled': 'var(--color-components-checkbox-icon-disabled)',
  'components-checkbox-bg': 'var(--color-components-checkbox-bg)',
  'components-checkbox-bg-hover': 'var(--color-components-checkbox-bg-hover)',
  'components-checkbox-bg-disabled': 'var(--color-components-checkbox-bg-disabled)',
  'components-checkbox-border': 'var(--color-components-checkbox-border)',
  'components-checkbox-border-hover': 'var(--color-components-checkbox-border-hover)',
  'components-checkbox-border-disabled': 'var(--color-components-checkbox-border-disabled)',
  'components-checkbox-bg-unchecked': 'var(--color-components-checkbox-bg-unchecked)',
  'components-checkbox-bg-unchecked-hover': 'var(--color-components-checkbox-bg-unchecked-hover)',
  'components-checkbox-bg-disabled-checked': 'var(--color-components-checkbox-bg-disabled-checked)',

  'components-radio-border-checked': 'var(--color-components-radio-border-checked)',
  'components-radio-border-checked-hover': 'var(--color-components-radio-border-checked-hover)',
  'components-radio-border-checked-disabled': 'var(--color-components-radio-border-checked-disabled)',
  'components-radio-bg-disabled': 'var(--color-components-radio-bg-disabled)',
  'components-radio-border': 'var(--color-components-radio-border)',
  'components-radio-border-hover': 'var(--color-components-radio-border-hover)',
  'components-radio-border-disabled': 'var(--color-components-radio-border-disabled)',
  'components-radio-bg': 'var(--color-components-radio-bg)',
  'components-radio-bg-hover': 'var(--color-components-radio-bg-hover)',

  'components-toggle-knob': 'var(--color-components-toggle-knob)',
  'components-toggle-knob-disabled': 'var(--color-components-toggle-knob-disabled)',
  'components-toggle-bg': 'var(--color-components-toggle-bg)',
  'components-toggle-bg-hover': 'var(--color-components-toggle-bg-hover)',
  'components-toggle-bg-disabled': 'var(--color-components-toggle-bg-disabled)',
  'components-toggle-bg-unchecked': 'var(--color-components-toggle-bg-unchecked)',
  'components-toggle-bg-unchecked-hover': 'var(--color-components-toggle-bg-unchecked-hover)',
  'components-toggle-bg-unchecked-disabled': 'var(--color-components-toggle-bg-unchecked-disabled)',
  'components-toggle-knob-hover': 'var(--color-components-toggle-knob-hover)',

  'components-card-bg': 'var(--color-components-card-bg)',
  'components-card-border': 'var(--color-components-card-border)',
  'components-card-bg-alt': 'var(--color-components-card-bg-alt)',
  'components-card-bg-transparent': 'var(--color-components-card-bg-transparent)',
  'components-card-bg-alt-transparent': 'var(--color-components-card-bg-alt-transparent)',

  'components-menu-item-text': 'var(--color-components-menu-item-text)',
  'components-menu-item-text-active': 'var(--color-components-menu-item-text-active)',
  'components-menu-item-text-hover': 'var(--color-components-menu-item-text-hover)',
  'components-menu-item-text-active-accent': 'var(--color-components-menu-item-text-active-accent)',
  'components-menu-item-bg-active': 'var(--color-components-menu-item-bg-active)',
  'components-menu-item-bg-hover': 'var(--color-components-menu-item-bg-hover)',

  'components-panel-bg': 'var(--color-components-panel-bg)',
  'components-panel-bg-blur': 'var(--color-components-panel-bg-blur)',
  'components-panel-border': 'var(--color-components-panel-border)',
  'components-panel-border-subtle': 'var(--color-components-panel-border-subtle)',
  'components-panel-gradient-2': 'var(--color-components-panel-gradient-2)',
  'components-panel-gradient-1': 'var(--color-components-panel-gradient-1)',
  'components-panel-bg-alt': 'var(--color-components-panel-bg-alt)',
  'components-panel-on-panel-item-bg': 'var(--color-components-panel-on-panel-item-bg)',
  'components-panel-on-panel-item-bg-hover': 'var(--color-components-panel-on-panel-item-bg-hover)',
  'components-panel-on-panel-item-bg-alt': 'var(--color-components-panel-on-panel-item-bg-alt)',
  'components-panel-on-panel-item-bg-transparent': 'var(--color-components-panel-on-panel-item-bg-transparent)',
  'components-panel-on-panel-item-bg-hover-transparent': 'var(--color-components-panel-on-panel-item-bg-hover-transparent)',
  'components-panel-on-panel-item-bg-destructive-hover-transparent': 'var(--color-components-panel-on-panel-item-bg-destructive-hover-transparent)',

  'components-panel-bg-transparent': 'var(--color-components-panel-bg-transparent)',

  'components-main-nav-nav-button-text': 'var(--color-components-main-nav-nav-button-text)',
  'components-main-nav-nav-button-text-active': 'var(--color-components-main-nav-nav-button-text-active)',
  'components-main-nav-nav-button-bg': 'var(--color-components-main-nav-nav-button-bg)',
  'components-main-nav-nav-button-bg-active': 'var(--color-components-main-nav-nav-button-bg-active)',
  'components-main-nav-nav-button-border': 'var(--color-components-main-nav-nav-button-border)',
  'components-main-nav-nav-button-bg-hover': 'var(--color-components-main-nav-nav-button-bg-hover)',

  'components-main-nav-nav-user-border': 'var(--color-components-main-nav-nav-user-border)',

  'components-slider-knob': 'var(--color-components-slider-knob)',
  'components-slider-knob-hover': 'var(--color-components-slider-knob-hover)',
  'components-slider-knob-disabled': 'var(--color-components-slider-knob-disabled)',
  'components-slider-range': 'var(--color-components-slider-range)',
  'components-slider-track': 'var(--color-components-slider-track)',
  'components-slider-knob-border-hover': 'var(--color-components-slider-knob-border-hover)',
  'components-slider-knob-border': 'var(--color-components-slider-knob-border)',

  'components-segmented-control-item-active-bg': 'var(--color-components-segmented-control-item-active-bg)',
  'components-segmented-control-item-active-border': 'var(--color-components-segmented-control-item-active-border)',
  'components-segmented-control-bg-normal': 'var(--color-components-segmented-control-bg-normal)',
  'components-segmented-control-item-active-accent-bg': 'var(--color-components-segmented-control-item-active-accent-bg)',
  'components-segmented-control-item-active-accent-border': 'var(--color-components-segmented-control-item-active-accent-border)',

  'components-option-card-option-bg': 'var(--color-components-option-card-option-bg)',
  'components-option-card-option-selected-bg': 'var(--color-components-option-card-option-selected-bg)',
  'components-option-card-option-selected-border': 'var(--color-components-option-card-option-selected-border)',
  'components-option-card-option-border': 'var(--color-components-option-card-option-border)',
  'components-option-card-option-bg-hover': 'var(--color-components-option-card-option-bg-hover)',
  'components-option-card-option-border-hover': 'var(--color-components-option-card-option-border-hover)',

  'components-tab-active': 'var(--color-components-tab-active)',

  'components-badge-white-to-dark': 'var(--color-components-badge-white-to-dark)',
  'components-badge-status-light-success-bg': 'var(--color-components-badge-status-light-success-bg)',
  'components-badge-status-light-success-border-inner': 'var(--color-components-badge-status-light-success-border-inner)',
  'components-badge-status-light-success-halo': 'var(--color-components-badge-status-light-success-halo)',

  'components-badge-status-light-border-outer': 'var(--color-components-badge-status-light-border-outer)',
  'components-badge-status-light-high-light': 'var(--color-components-badge-status-light-high-light)',
  'components-badge-status-light-warning-bg': 'var(--color-components-badge-status-light-warning-bg)',
  'components-badge-status-light-warning-border-inner': 'var(--color-components-badge-status-light-warning-border-inner)',
  'components-badge-status-light-warning-halo': 'var(--color-components-badge-status-light-warning-halo)',

  'components-badge-status-light-error-bg': 'var(--color-components-badge-status-light-error-bg)',
  'components-badge-status-light-error-border-inner': 'var(--color-components-badge-status-light-error-border-inner)',
  'components-badge-status-light-error-halo': 'var(--color-components-badge-status-light-error-halo)',

  'components-badge-status-light-normal-bg': 'var(--color-components-badge-status-light-normal-bg)',
  'components-badge-status-light-normal-border-inner': 'var(--color-components-badge-status-light-normal-border-inner)',
  'components-badge-status-light-normal-halo': 'var(--color-components-badge-status-light-normal-halo)',

  'components-badge-status-light-disabled-bg': 'var(--color-components-badge-status-light-disabled-bg)',
  'components-badge-status-light-disabled-border-inner': 'var(--color-components-badge-status-light-disabled-border-inner)',
  'components-badge-status-light-disabled-halo': 'var(--color-components-badge-status-light-disabled-halo)',

  'components-badge-bg-green-soft': 'var(--color-components-badge-bg-green-soft)',
  'components-badge-bg-orange-soft': 'var(--color-components-badge-bg-orange-soft)',
  'components-badge-bg-red-soft': 'var(--color-components-badge-bg-red-soft)',
  'components-badge-bg-blue-light-soft': 'var(--color-components-badge-bg-blue-light-soft)',
  'components-badge-bg-gray-soft': 'var(--color-components-badge-bg-gray-soft)',
  'components-badge-bg-dimm': 'var(--color-components-badge-bg-dimm)',

  'components-chart-line': 'var(--color-components-chart-line)',
  'components-chart-area-1': 'var(--color-components-chart-area-1)',
  'components-chart-area-2': 'var(--color-components-chart-area-2)',
  'components-chart-current-1': 'var(--color-components-chart-current-1)',
  'components-chart-current-2': 'var(--color-components-chart-current-2)',
  'components-chart-bg': 'var(--color-components-chart-bg)',

  'components-actionbar-bg': 'var(--color-components-actionbar-bg)',
  'components-actionbar-border': 'var(--color-components-actionbar-border)',
  'components-actionbar-bg-accent': 'var(--color-components-actionbar-bg-accent)',
  'components-actionbar-border-accent': 'var(--color-components-actionbar-border-accent)',

  'components-dropzone-bg-alt': 'var(--color-components-dropzone-bg-alt)',
  'components-dropzone-bg': 'var(--color-components-dropzone-bg)',
  'components-dropzone-bg-accent': 'var(--color-components-dropzone-bg-accent)',
  'components-dropzone-border': 'var(--color-components-dropzone-border)',
  'components-dropzone-border-alt': 'var(--color-components-dropzone-border-alt)',
  'components-dropzone-border-accent': 'var(--color-components-dropzone-border-accent)',

  'components-progress-brand-progress': 'var(--color-components-progress-brand-progress)',
  'components-progress-brand-border': 'var(--color-components-progress-brand-border)',
  'components-progress-brand-bg': 'var(--color-components-progress-brand-bg)',

  'components-progress-white-progress': 'var(--color-components-progress-white-progress)',
  'components-progress-white-border': 'var(--color-components-progress-white-border)',
  'components-progress-white-bg': 'var(--color-components-progress-white-bg)',

  'components-progress-gray-progress': 'var(--color-components-progress-gray-progress)',
  'components-progress-gray-border': 'var(--color-components-progress-gray-border)',
  'components-progress-gray-bg': 'var(--color-components-progress-gray-bg)',

  'components-progress-warning-progress': 'var(--color-components-progress-warning-progress)',
  'components-progress-warning-border': 'var(--color-components-progress-warning-border)',
  'components-progress-warning-bg': 'var(--color-components-progress-warning-bg)',

  'components-progress-error-progress': 'var(--color-components-progress-error-progress)',
  'components-progress-error-border': 'var(--color-components-progress-error-border)',
  'components-progress-error-bg': 'var(--color-components-progress-error-bg)',

  'components-chat-input-audio-bg': 'var(--color-components-chat-input-audio-bg)',
  'components-chat-input-audio-wave-default': 'var(--color-components-chat-input-audio-wave-default)',
  'components-chat-input-bg-mask-1': 'var(--color-components-chat-input-bg-mask-1)',
  'components-chat-input-bg-mask-2': 'var(--color-components-chat-input-bg-mask-2)',
  'components-chat-input-border': 'var(--color-components-chat-input-border)',
  'components-chat-input-audio-wave-active': 'var(--color-components-chat-input-audio-wave-active)',
  'components-chat-input-audio-bg-alt': 'var(--color-components-chat-input-audio-bg-alt)',

  'components-avatar-shape-fill-stop-0': 'var(--color-components-avatar-shape-fill-stop-0)',
  'components-avatar-shape-fill-stop-100': 'var(--color-components-avatar-shape-fill-stop-100)',

  'components-avatar-bg-mask-stop-0': 'var(--color-components-avatar-bg-mask-stop-0)',
  'components-avatar-bg-mask-stop-100': 'var(--color-components-avatar-bg-mask-stop-100)',

  'components-avatar-default-avatar-bg': 'var(--color-components-avatar-default-avatar-bg)',
  'components-avatar-mask-darkmode-dimmed': 'var(--color-components-avatar-mask-darkmode-dimmed)',

  'components-label-gray': 'var(--color-components-label-gray)',

  'components-premium-badge-blue-bg-stop-0': 'var(--color-components-premium-badge-blue-bg-stop-0)',
  'components-premium-badge-blue-bg-stop-100': 'var(--color-components-premium-badge-blue-bg-stop-100)',
  'components-premium-badge-blue-stroke-stop-0': 'var(--color-components-premium-badge-blue-stroke-stop-0)',
  'components-premium-badge-blue-stroke-stop-100': 'var(--color-components-premium-badge-blue-stroke-stop-100)',
  'components-premium-badge-blue-text-stop-0': 'var(--color-components-premium-badge-blue-text-stop-0)',
  'components-premium-badge-blue-text-stop-100': 'var(--color-components-premium-badge-blue-text-stop-100)',
  'components-premium-badge-blue-glow': 'var(--color-components-premium-badge-blue-glow)',
  'components-premium-badge-blue-bg-stop-0-hover': 'var(--color-components-premium-badge-blue-bg-stop-0-hover)',
  'components-premium-badge-blue-bg-stop-100-hover': 'var(--color-components-premium-badge-blue-bg-stop-100-hover)',
  'components-premium-badge-blue-glow-hover': 'var(--color-components-premium-badge-blue-glow-hover)',
  'components-premium-badge-blue-stroke-stop-0-hover': 'var(--color-components-premium-badge-blue-stroke-stop-0-hover)',
  'components-premium-badge-blue-stroke-stop-100-hover': 'var(--color-components-premium-badge-blue-stroke-stop-100-hover)',

  'components-premium-badge-highlight-stop-0': 'var(--color-components-premium-badge-highlight-stop-0)',
  'components-premium-badge-highlight-stop-100': 'var(--color-components-premium-badge-highlight-stop-100)',
  'components-premium-badge-indigo-bg-stop-0': 'var(--color-components-premium-badge-indigo-bg-stop-0)',
  'components-premium-badge-indigo-bg-stop-100': 'var(--color-components-premium-badge-indigo-bg-stop-100)',
  'components-premium-badge-indigo-stroke-stop-0': 'var(--color-components-premium-badge-indigo-stroke-stop-0)',
  'components-premium-badge-indigo-stroke-stop-100': 'var(--color-components-premium-badge-indigo-stroke-stop-100)',
  'components-premium-badge-indigo-text-stop-0': 'var(--color-components-premium-badge-indigo-text-stop-0)',
  'components-premium-badge-indigo-text-stop-100': 'var(--color-components-premium-badge-indigo-text-stop-100)',
  'components-premium-badge-indigo-glow': 'var(--color-components-premium-badge-indigo-glow)',
  'components-premium-badge-indigo-glow-hover': 'var(--color-components-premium-badge-indigo-glow-hover)',
  'components-premium-badge-indigo-bg-stop-0-hover': 'var(--color-components-premium-badge-indigo-bg-stop-0-hover)',
  'components-premium-badge-indigo-bg-stop-100-hover': 'var(--color-components-premium-badge-indigo-bg-stop-100-hover)',
  'components-premium-badge-indigo-stroke-stop-0-hover': 'var(--color-components-premium-badge-indigo-stroke-stop-0-hover)',
  'components-premium-badge-indigo-stroke-stop-100-hover': 'var(--color-components-premium-badge-indigo-stroke-stop-100-hover)',

  'components-premium-badge-grey-bg-stop-0': 'var(--color-components-premium-badge-grey-bg-stop-0)',
  'components-premium-badge-grey-bg-stop-100': 'var(--color-components-premium-badge-grey-bg-stop-100)',
  'components-premium-badge-grey-stroke-stop-0': 'var(--color-components-premium-badge-grey-stroke-stop-0)',
  'components-premium-badge-grey-stroke-stop-100': 'var(--color-components-premium-badge-grey-stroke-stop-100)',
  'components-premium-badge-grey-text-stop-0': 'var(--color-components-premium-badge-grey-text-stop-0)',
  'components-premium-badge-grey-text-stop-100': 'var(--color-components-premium-badge-grey-text-stop-100)',
  'components-premium-badge-grey-glow': 'var(--color-components-premium-badge-grey-glow)',
  'components-premium-badge-grey-glow-hover': 'var(--color-components-premium-badge-grey-glow-hover)',
  'components-premium-badge-grey-bg-stop-0-hover': 'var(--color-components-premium-badge-grey-bg-stop-0-hover)',
  'components-premium-badge-grey-bg-stop-100-hover': 'var(--color-components-premium-badge-grey-bg-stop-100-hover)',
  'components-premium-badge-grey-stroke-stop-0-hover': 'var(--color-components-premium-badge-grey-stroke-stop-0-hover)',
  'components-premium-badge-grey-stroke-stop-100-hover': 'var(--color-components-premium-badge-grey-stroke-stop-100-hover)',

  'components-premium-badge-orange-bg-stop-0': 'var(--color-components-premium-badge-orange-bg-stop-0)',
  'components-premium-badge-orange-bg-stop-100': 'var(--color-components-premium-badge-orange-bg-stop-100)',
  'components-premium-badge-orange-stroke-stop-0': 'var(--color-components-premium-badge-orange-stroke-stop-0)',
  'components-premium-badge-orange-stroke-stop-100': 'var(--color-components-premium-badge-orange-stroke-stop-100)',
  'components-premium-badge-orange-text-stop-0': 'var(--color-components-premium-badge-orange-text-stop-0)',
  'components-premium-badge-orange-text-stop-100': 'var(--color-components-premium-badge-orange-text-stop-100)',
  'components-premium-badge-orange-glow': 'var(--color-components-premium-badge-orange-glow)',
  'components-premium-badge-orange-glow-hover': 'var(--color-components-premium-badge-orange-glow-hover)',
  'components-premium-badge-orange-bg-stop-0-hover': 'var(--color-components-premium-badge-orange-bg-stop-0-hover)',
  'components-premium-badge-orange-bg-stop-100-hover': 'var(--color-components-premium-badge-orange-bg-stop-100-hover)',
  'components-premium-badge-orange-stroke-stop-0-hover': 'var(--color-components-premium-badge-orange-stroke-stop-0-hover)',
  'components-premium-badge-orange-stroke-stop-100-hover': 'var(--color-components-premium-badge-orange-stroke-stop-100-hover)',

  'components-progress-bar-bg': 'var(--color-components-progress-bar-bg)',
  'components-progress-bar-progress': 'var(--color-components-progress-bar-progress)',
  'components-progress-bar-border': 'var(--color-components-progress-bar-border)',
  'components-progress-bar-progress-solid': 'var(--color-components-progress-bar-progress-solid)',
  'components-progress-bar-progress-highlight': 'var(--color-components-progress-bar-progress-highlight)',

  'components-icon-bg-red-solid': 'var(--color-components-icon-bg-red-solid)',
  'components-icon-bg-rose-solid': 'var(--color-components-icon-bg-rose-solid)',
  'components-icon-bg-pink-solid': 'var(--color-components-icon-bg-pink-solid)',
  'components-icon-bg-orange-dark-solid': 'var(--color-components-icon-bg-orange-dark-solid)',
  'components-icon-bg-yellow-solid': 'var(--color-components-icon-bg-yellow-solid)',
  'components-icon-bg-green-solid': 'var(--color-components-icon-bg-green-solid)',
  'components-icon-bg-teal-solid': 'var(--color-components-icon-bg-teal-solid)',
  'components-icon-bg-blue-light-solid': 'var(--color-components-icon-bg-blue-light-solid)',
  'components-icon-bg-blue-solid': 'var(--color-components-icon-bg-blue-solid)',
  'components-icon-bg-indigo-solid': 'var(--color-components-icon-bg-indigo-solid)',
  'components-icon-bg-violet-solid': 'var(--color-components-icon-bg-violet-solid)',
  'components-icon-bg-midnight-solid': 'var(--color-components-icon-bg-midnight-solid)',
  'components-icon-bg-rose-soft': 'var(--color-components-icon-bg-rose-soft)',
  'components-icon-bg-pink-soft': 'var(--color-components-icon-bg-pink-soft)',
  'components-icon-bg-orange-dark-soft': 'var(--color-components-icon-bg-orange-dark-soft)',
  'components-icon-bg-yellow-soft': 'var(--color-components-icon-bg-yellow-soft)',
  'components-icon-bg-green-soft': 'var(--color-components-icon-bg-green-soft)',
  'components-icon-bg-teal-soft': 'var(--color-components-icon-bg-teal-soft)',
  'components-icon-bg-blue-light-soft': 'var(--color-components-icon-bg-blue-light-soft)',
  'components-icon-bg-blue-soft': 'var(--color-components-icon-bg-blue-soft)',
  'components-icon-bg-indigo-soft': 'var(--color-components-icon-bg-indigo-soft)',
  'components-icon-bg-violet-soft': 'var(--color-components-icon-bg-violet-soft)',
  'components-icon-bg-midnight-soft': 'var(--color-components-icon-bg-midnight-soft)',
  'components-icon-bg-red-soft': 'var(--color-components-icon-bg-red-soft)',
  'components-icon-bg-orange-solid': 'var(--color-components-icon-bg-orange-solid)',
  'components-icon-bg-orange-soft': 'var(--color-components-icon-bg-orange-soft)',

  'text-primary': 'var(--color-text-primary)',
  'text-secondary': 'var(--color-text-secondary)',
  'text-tertiary': 'var(--color-text-tertiary)',
  'text-quaternary': 'var(--color-text-quaternary)',
  'text-destructive': 'var(--color-text-destructive)',
  'text-success': 'var(--color-text-success)',
  'text-warning': 'var(--color-text-warning)',
  'text-destructive-secondary': 'var(--color-text-destructive-secondary)',
  'text-success-secondary': 'var(--color-text-success-secondary)',
  'text-warning-secondary': 'var(--color-text-warning-secondary)',
  'text-accent': 'var(--color-text-accent)',
  'text-primary-on-surface': 'var(--color-text-primary-on-surface)',
  'text-placeholder': 'var(--color-text-placeholder)',
  'text-disabled': 'var(--color-text-disabled)',
  'text-accent-secondary': 'var(--color-text-accent-secondary)',
  'text-accent-light-mode-only': 'var(--color-text-accent-light-mode-only)',
  'text-text-selected': 'var(--color-text-text-selected)',
  'text-secondary-on-surface': 'var(--color-text-secondary-on-surface)',
  'text-logo-text': 'var(--color-text-logo-text)',
  'text-empty-state-icon': 'var(--color-text-empty-state-icon)',
  'text-inverted': 'var(--color-text-inverted)',
  'text-inverted-dimmed': 'var(--color-text-inverted-dimmed)',

  'background-body': 'var(--color-background-body)',
  'background-default-subtle': 'var(--color-background-default-subtle)',
  'background-neutral-subtle': 'var(--color-background-neutral-subtle)',
  'background-sidenav-bg': 'var(--color-background-sidenav-bg)',
  'background-default': 'var(--color-background-default)',
  'background-soft': 'var(--color-background-soft)',
  'background-gradient-bg-fill-chat-bg-1': 'var(--color-background-gradient-bg-fill-chat-bg-1)',
  'background-gradient-bg-fill-chat-bg-2': 'var(--color-background-gradient-bg-fill-chat-bg-2)',
  'background-gradient-bg-fill-chat-bubble-bg-1': 'var(--color-background-gradient-bg-fill-chat-bubble-bg-1)',
  'background-gradient-bg-fill-chat-bubble-bg-2': 'var(--color-background-gradient-bg-fill-chat-bubble-bg-2)',
  'background-gradient-bg-fill-debug-bg-1': 'var(--color-background-gradient-bg-fill-debug-bg-1)',
  'background-gradient-bg-fill-debug-bg-2': 'var(--color-background-gradient-bg-fill-debug-bg-2)',

  'background-gradient-mask-gray': 'var(--color-background-gradient-mask-gray)',
  'background-gradient-mask-transparent': 'var(--color-background-gradient-mask-transparent)',
  'background-gradient-mask-input-clear-2': 'var(--color-background-gradient-mask-input-clear-2)',
  'background-gradient-mask-input-clear-1': 'var(--color-background-gradient-mask-input-clear-1)',
  'background-gradient-mask-transparent-dark': 'var(--color-background-gradient-mask-transparent-dark)',
  'background-gradient-mask-side-panel-2': 'var(--color-background-gradient-mask-side-panel-2)',
  'background-gradient-mask-side-panel-1': 'var(--color-background-gradient-mask-side-panel-1)',

  'background-default-burn': 'var(--color-background-default-burn)',
  'background-overlay-fullscreen': 'var(--color-background-overlay-fullscreen)',
  'background-default-lighter': 'var(--color-background-default-lighter)',
  'background-section': 'var(--color-background-section)',
  'background-interaction-from-bg-1': 'var(--color-background-interaction-from-bg-1)',
  'background-interaction-from-bg-2': 'var(--color-background-interaction-from-bg-2)',
  'background-section-burn': 'var(--color-background-section-burn)',
  'background-default-dodge': 'var(--color-background-default-dodge)',
  'background-overlay': 'var(--color-background-overlay)',
  'background-default-dimmed': 'var(--color-background-default-dimmed)',
  'background-default-hover': 'var(--color-background-default-hover)',
  'background-overlay-alt': 'var(--color-background-overlay-alt)',
  'background-surface-white': 'var(--color-background-surface-white)',
  'background-overlay-destructive': 'var(--color-background-overlay-destructive)',
  'background-overlay-backdrop': 'var(--color-background-overlay-backdrop)',
  'background-body-transparent': 'var(--color-background-body-transparent)',

  'shadow-shadow-1': 'var(--color-shadow-shadow-1)',
  'shadow-shadow-3': 'var(--color-shadow-shadow-3)',
  'shadow-shadow-4': 'var(--color-shadow-shadow-4)',
  'shadow-shadow-5': 'var(--color-shadow-shadow-5)',
  'shadow-shadow-6': 'var(--color-shadow-shadow-6)',
  'shadow-shadow-7': 'var(--color-shadow-shadow-7)',
  'shadow-shadow-8': 'var(--color-shadow-shadow-8)',
  'shadow-shadow-9': 'var(--color-shadow-shadow-9)',
  'shadow-shadow-2': 'var(--color-shadow-shadow-2)',
  'shadow-shadow-10': 'var(--color-shadow-shadow-10)',

  'workflow-block-border': 'var(--color-workflow-block-border)',
  'workflow-block-parma-bg': 'var(--color-workflow-block-parma-bg)',
  'workflow-block-bg': 'var(--color-workflow-block-bg)',
  'workflow-block-bg-transparent': 'var(--color-workflow-block-bg-transparent)',
  'workflow-block-border-highlight': 'var(--color-workflow-block-border-highlight)',
  'workflow-block-wrapper-bg-1': 'var(--color-workflow-block-wrapper-bg-1)',
  'workflow-block-wrapper-bg-2': 'var(--color-workflow-block-wrapper-bg-2)',

  'workflow-canvas-workflow-dot-color': 'var(--color-workflow-canvas-workflow-dot-color)',
  'workflow-canvas-workflow-bg': 'var(--color-workflow-canvas-workflow-bg)',
  'workflow-canvas-workflow-top-bar-1': 'var(--color-workflow-canvas-workflow-top-bar-1)',
  'workflow-canvas-workflow-top-bar-2': 'var(--color-workflow-canvas-workflow-top-bar-2)',
  'workflow-canvas-canvas-overlay': 'var(--color-workflow-canvas-canvas-overlay)',

  'workflow-link-line-active': 'var(--color-workflow-link-line-active)',
  'workflow-link-line-normal': 'var(--color-workflow-link-line-normal)',
  'workflow-link-line-handle': 'var(--color-workflow-link-line-handle)',
  'workflow-link-line-normal-transparent': 'var(--color-workflow-link-line-normal-transparent)',
  'workflow-link-line-failure-active': 'var(--color-workflow-link-line-failure-active)',
  'workflow-link-line-failure-handle': 'var(--color-workflow-link-line-failure-handle)',
  'workflow-link-line-failure-button-bg': 'var(--color-workflow-link-line-failure-button-bg)',
  'workflow-link-line-failure-button-hover': 'var(--color-workflow-link-line-failure-button-hover)',

  'workflow-link-line-success-active': 'var(--color-workflow-link-line-success-active)',
  'workflow-link-line-success-handle': 'var(--color-workflow-link-line-success-handle)',

  'workflow-link-line-error-active': 'var(--color-workflow-link-line-error-active)',
  'workflow-link-line-error-handle': 'var(--color-workflow-link-line-error-handle)',

  'workflow-minimap-bg': 'var(--color-workflow-minimap-bg)',
  'workflow-minimap-block': 'var(--color-workflow-minimap-block)',

  'workflow-display-success-bg': 'var(--color-workflow-display-success-bg)',
  'workflow-display-success-border-1': 'var(--color-workflow-display-success-border-1)',
  'workflow-display-success-border-2': 'var(--color-workflow-display-success-border-2)',
  'workflow-display-success-vignette-color': 'var(--color-workflow-display-success-vignette-color)',
  'workflow-display-success-bg-line-pattern': 'var(--color-workflow-display-success-bg-line-pattern)',

  'workflow-display-glass-1': 'var(--color-workflow-display-glass-1)',
  'workflow-display-glass-2': 'var(--color-workflow-display-glass-2)',
  'workflow-display-vignette-dark': 'var(--color-workflow-display-vignette-dark)',
  'workflow-display-highlight': 'var(--color-workflow-display-highlight)',
  'workflow-display-outline': 'var(--color-workflow-display-outline)',
  'workflow-display-error-bg': 'var(--color-workflow-display-error-bg)',
  'workflow-display-error-bg-line-pattern': 'var(--color-workflow-display-error-bg-line-pattern)',
  'workflow-display-error-border-1': 'var(--color-workflow-display-error-border-1)',
  'workflow-display-error-border-2': 'var(--color-workflow-display-error-border-2)',
  'workflow-display-error-vignette-color': 'var(--color-workflow-display-error-vignette-color)',

  'workflow-display-warning-bg': 'var(--color-workflow-display-warning-bg)',
  'workflow-display-warning-bg-line-pattern': 'var(--color-workflow-display-warning-bg-line-pattern)',
  'workflow-display-warning-border-1': 'var(--color-workflow-display-warning-border-1)',
  'workflow-display-warning-border-2': 'var(--color-workflow-display-warning-border-2)',
  'workflow-display-warning-vignette-color': 'var(--color-workflow-display-warning-vignette-color)',

  'workflow-display-normal-bg': 'var(--color-workflow-display-normal-bg)',
  'workflow-display-normal-bg-line-pattern': 'var(--color-workflow-display-normal-bg-line-pattern)',
  'workflow-display-normal-border-1': 'var(--color-workflow-display-normal-border-1)',
  'workflow-display-normal-border-2': 'var(--color-workflow-display-normal-border-2)',
  'workflow-display-normal-vignette-color': 'var(--color-workflow-display-normal-vignette-color)',

  'workflow-display-disabled-bg': 'var(--color-workflow-display-disabled-bg)',
  'workflow-display-disabled-bg-line-pattern': 'var(--color-workflow-display-disabled-bg-line-pattern)',
  'workflow-display-disabled-border-1': 'var(--color-workflow-display-disabled-border-1)',
  'workflow-display-disabled-border-2': 'var(--color-workflow-display-disabled-border-2)',
  'workflow-display-disabled-vignette-color': 'var(--color-workflow-display-disabled-vignette-color)',
  'workflow-display-disabled-outline': 'var(--color-workflow-display-disabled-outline)',

  'workflow-workflow-progress-bg-1': 'var(--color-workflow-workflow-progress-bg-1)',
  'workflow-workflow-progress-bg-2': 'var(--color-workflow-workflow-progress-bg-2)',

  'divider-subtle': 'var(--color-divider-subtle)',
  'divider-regular': 'var(--color-divider-regular)',
  'divider-deep': 'var(--color-divider-deep)',
  'divider-burn': 'var(--color-divider-burn)',
  'divider-intense': 'var(--color-divider-intense)',
  'divider-solid': 'var(--color-divider-solid)',
  'divider-solid-alt': 'var(--color-divider-solid-alt)',

  'state-base-hover': 'var(--color-state-base-hover)',
  'state-base-active': 'var(--color-state-base-active)',
  'state-base-hover-alt': 'var(--color-state-base-hover-alt)',
  'state-base-handle': 'var(--color-state-base-handle)',
  'state-base-handle-hover': 'var(--color-state-base-handle-hover)',
  'state-base-hover-subtle': 'var(--color-state-base-hover-subtle)',

  'state-accent-hover': 'var(--color-state-accent-hover)',
  'state-accent-active': 'var(--color-state-accent-active)',
  'state-accent-hover-alt': 'var(--color-state-accent-hover-alt)',
  'state-accent-solid': 'var(--color-state-accent-solid)',
  'state-accent-active-alt': 'var(--color-state-accent-active-alt)',

  'state-destructive-hover': 'var(--color-state-destructive-hover)',
  'state-destructive-hover-alt': 'var(--color-state-destructive-hover-alt)',
  'state-destructive-active': 'var(--color-state-destructive-active)',
  'state-destructive-solid': 'var(--color-state-destructive-solid)',
  'state-destructive-border': 'var(--color-state-destructive-border)',
  'state-destructive-hover-transparent': 'var(--color-state-destructive-hover-transparent)',

  'state-success-hover': 'var(--color-state-success-hover)',
  'state-success-hover-alt': 'var(--color-state-success-hover-alt)',
  'state-success-active': 'var(--color-state-success-active)',
  'state-success-solid': 'var(--color-state-success-solid)',

  'state-warning-hover': 'var(--color-state-warning-hover)',
  'state-warning-hover-alt': 'var(--color-state-warning-hover-alt)',
  'state-warning-active': 'var(--color-state-warning-active)',
  'state-warning-solid': 'var(--color-state-warning-solid)',
  'state-warning-hover-transparent': 'var(--color-state-warning-hover-transparent)',

  'effects-highlight': 'var(--color-effects-highlight)',
  'effects-highlight-lightmode-off': 'var(--color-effects-highlight-lightmode-off)',
  'effects-image-frame': 'var(--color-effects-image-frame)',
  'effects-icon-border': 'var(--color-effects-icon-border)',

  'util-colors-orange-dark-orange-dark-50': 'var(--color-util-colors-orange-dark-orange-dark-50)',
  'util-colors-orange-dark-orange-dark-100': 'var(--color-util-colors-orange-dark-orange-dark-100)',
  'util-colors-orange-dark-orange-dark-200': 'var(--color-util-colors-orange-dark-orange-dark-200)',
  'util-colors-orange-dark-orange-dark-300': 'var(--color-util-colors-orange-dark-orange-dark-300)',
  'util-colors-orange-dark-orange-dark-400': 'var(--color-util-colors-orange-dark-orange-dark-400)',
  'util-colors-orange-dark-orange-dark-500': 'var(--color-util-colors-orange-dark-orange-dark-500)',
  'util-colors-orange-dark-orange-dark-600': 'var(--color-util-colors-orange-dark-orange-dark-600)',
  'util-colors-orange-dark-orange-dark-700': 'var(--color-util-colors-orange-dark-orange-dark-700)',

  'util-colors-orange-orange-50': 'var(--color-util-colors-orange-orange-50)',
  'util-colors-orange-orange-100': 'var(--color-util-colors-orange-orange-100)',
  'util-colors-orange-orange-200': 'var(--color-util-colors-orange-orange-200)',
  'util-colors-orange-orange-300': 'var(--color-util-colors-orange-orange-300)',
  'util-colors-orange-orange-400': 'var(--color-util-colors-orange-orange-400)',
  'util-colors-orange-orange-500': 'var(--color-util-colors-orange-orange-500)',
  'util-colors-orange-orange-600': 'var(--color-util-colors-orange-orange-600)',
  'util-colors-orange-orange-700': 'var(--color-util-colors-orange-orange-700)',
  'util-colors-orange-orange-100-transparent': 'var(--color-util-colors-orange-orange-100-transparent)',

  'util-colors-pink-pink-50': 'var(--color-util-colors-pink-pink-50)',
  'util-colors-pink-pink-100': 'var(--color-util-colors-pink-pink-100)',
  'util-colors-pink-pink-200': 'var(--color-util-colors-pink-pink-200)',
  'util-colors-pink-pink-300': 'var(--color-util-colors-pink-pink-300)',
  'util-colors-pink-pink-400': 'var(--color-util-colors-pink-pink-400)',
  'util-colors-pink-pink-500': 'var(--color-util-colors-pink-pink-500)',
  'util-colors-pink-pink-600': 'var(--color-util-colors-pink-pink-600)',
  'util-colors-pink-pink-700': 'var(--color-util-colors-pink-pink-700)',

  'util-colors-fuchsia-fuchsia-50': 'var(--color-util-colors-fuchsia-fuchsia-50)',
  'util-colors-fuchsia-fuchsia-100': 'var(--color-util-colors-fuchsia-fuchsia-100)',
  'util-colors-fuchsia-fuchsia-200': 'var(--color-util-colors-fuchsia-fuchsia-200)',
  'util-colors-fuchsia-fuchsia-300': 'var(--color-util-colors-fuchsia-fuchsia-300)',
  'util-colors-fuchsia-fuchsia-400': 'var(--color-util-colors-fuchsia-fuchsia-400)',
  'util-colors-fuchsia-fuchsia-500': 'var(--color-util-colors-fuchsia-fuchsia-500)',
  'util-colors-fuchsia-fuchsia-600': 'var(--color-util-colors-fuchsia-fuchsia-600)',
  'util-colors-fuchsia-fuchsia-700': 'var(--color-util-colors-fuchsia-fuchsia-700)',

  'util-colors-purple-purple-50': 'var(--color-util-colors-purple-purple-50)',
  'util-colors-purple-purple-100': 'var(--color-util-colors-purple-purple-100)',
  'util-colors-purple-purple-200': 'var(--color-util-colors-purple-purple-200)',
  'util-colors-purple-purple-300': 'var(--color-util-colors-purple-purple-300)',
  'util-colors-purple-purple-400': 'var(--color-util-colors-purple-purple-400)',
  'util-colors-purple-purple-500': 'var(--color-util-colors-purple-purple-500)',
  'util-colors-purple-purple-600': 'var(--color-util-colors-purple-purple-600)',
  'util-colors-purple-purple-700': 'var(--color-util-colors-purple-purple-700)',

  'util-colors-indigo-indigo-50': 'var(--color-util-colors-indigo-indigo-50)',
  'util-colors-indigo-indigo-100': 'var(--color-util-colors-indigo-indigo-100)',
  'util-colors-indigo-indigo-200': 'var(--color-util-colors-indigo-indigo-200)',
  'util-colors-indigo-indigo-300': 'var(--color-util-colors-indigo-indigo-300)',
  'util-colors-indigo-indigo-400': 'var(--color-util-colors-indigo-indigo-400)',
  'util-colors-indigo-indigo-500': 'var(--color-util-colors-indigo-indigo-500)',
  'util-colors-indigo-indigo-600': 'var(--color-util-colors-indigo-indigo-600)',
  'util-colors-indigo-indigo-700': 'var(--color-util-colors-indigo-indigo-700)',

  'util-colors-blue-blue-50': 'var(--color-util-colors-blue-blue-50)',
  'util-colors-blue-blue-100': 'var(--color-util-colors-blue-blue-100)',
  'util-colors-blue-blue-200': 'var(--color-util-colors-blue-blue-200)',
  'util-colors-blue-blue-300': 'var(--color-util-colors-blue-blue-300)',
  'util-colors-blue-blue-400': 'var(--color-util-colors-blue-blue-400)',
  'util-colors-blue-blue-500': 'var(--color-util-colors-blue-blue-500)',
  'util-colors-blue-blue-600': 'var(--color-util-colors-blue-blue-600)',
  'util-colors-blue-blue-700': 'var(--color-util-colors-blue-blue-700)',

  'util-colors-blue-light-blue-light-50': 'var(--color-util-colors-blue-light-blue-light-50)',
  'util-colors-blue-light-blue-light-100': 'var(--color-util-colors-blue-light-blue-light-100)',
  'util-colors-blue-light-blue-light-200': 'var(--color-util-colors-blue-light-blue-light-200)',
  'util-colors-blue-light-blue-light-300': 'var(--color-util-colors-blue-light-blue-light-300)',
  'util-colors-blue-light-blue-light-400': 'var(--color-util-colors-blue-light-blue-light-400)',
  'util-colors-blue-light-blue-light-500': 'var(--color-util-colors-blue-light-blue-light-500)',
  'util-colors-blue-light-blue-light-600': 'var(--color-util-colors-blue-light-blue-light-600)',
  'util-colors-blue-light-blue-light-700': 'var(--color-util-colors-blue-light-blue-light-700)',

  'util-colors-gray-blue-gray-blue-50': 'var(--color-util-colors-gray-blue-gray-blue-50)',
  'util-colors-gray-blue-gray-blue-100': 'var(--color-util-colors-gray-blue-gray-blue-100)',
  'util-colors-gray-blue-gray-blue-200': 'var(--color-util-colors-gray-blue-gray-blue-200)',
  'util-colors-gray-blue-gray-blue-300': 'var(--color-util-colors-gray-blue-gray-blue-300)',
  'util-colors-gray-blue-gray-blue-400': 'var(--color-util-colors-gray-blue-gray-blue-400)',
  'util-colors-gray-blue-gray-blue-500': 'var(--color-util-colors-gray-blue-gray-blue-500)',
  'util-colors-gray-blue-gray-blue-600': 'var(--color-util-colors-gray-blue-gray-blue-600)',
  'util-colors-gray-blue-gray-blue-700': 'var(--color-util-colors-gray-blue-gray-blue-700)',

  'util-colors-blue-brand-blue-brand-50': 'var(--color-util-colors-blue-brand-blue-brand-50)',
  'util-colors-blue-brand-blue-brand-100': 'var(--color-util-colors-blue-brand-blue-brand-100)',
  'util-colors-blue-brand-blue-brand-200': 'var(--color-util-colors-blue-brand-blue-brand-200)',
  'util-colors-blue-brand-blue-brand-300': 'var(--color-util-colors-blue-brand-blue-brand-300)',
  'util-colors-blue-brand-blue-brand-400': 'var(--color-util-colors-blue-brand-blue-brand-400)',
  'util-colors-blue-brand-blue-brand-500': 'var(--color-util-colors-blue-brand-blue-brand-500)',
  'util-colors-blue-brand-blue-brand-600': 'var(--color-util-colors-blue-brand-blue-brand-600)',
  'util-colors-blue-brand-blue-brand-700': 'var(--color-util-colors-blue-brand-blue-brand-700)',

  'util-colors-red-red-50': 'var(--color-util-colors-red-red-50)',
  'util-colors-red-red-100': 'var(--color-util-colors-red-red-100)',
  'util-colors-red-red-200': 'var(--color-util-colors-red-red-200)',
  'util-colors-red-red-300': 'var(--color-util-colors-red-red-300)',
  'util-colors-red-red-400': 'var(--color-util-colors-red-red-400)',
  'util-colors-red-red-500': 'var(--color-util-colors-red-red-500)',
  'util-colors-red-red-600': 'var(--color-util-colors-red-red-600)',
  'util-colors-red-red-700': 'var(--color-util-colors-red-red-700)',

  'util-colors-green-green-50': 'var(--color-util-colors-green-green-50)',
  'util-colors-green-green-100': 'var(--color-util-colors-green-green-100)',
  'util-colors-green-green-200': 'var(--color-util-colors-green-green-200)',
  'util-colors-green-green-300': 'var(--color-util-colors-green-green-300)',
  'util-colors-green-green-400': 'var(--color-util-colors-green-green-400)',
  'util-colors-green-green-500': 'var(--color-util-colors-green-green-500)',
  'util-colors-green-green-600': 'var(--color-util-colors-green-green-600)',
  'util-colors-green-green-700': 'var(--color-util-colors-green-green-700)',

  'util-colors-warning-warning-50': 'var(--color-util-colors-warning-warning-50)',
  'util-colors-warning-warning-100': 'var(--color-util-colors-warning-warning-100)',
  'util-colors-warning-warning-200': 'var(--color-util-colors-warning-warning-200)',
  'util-colors-warning-warning-300': 'var(--color-util-colors-warning-warning-300)',
  'util-colors-warning-warning-400': 'var(--color-util-colors-warning-warning-400)',
  'util-colors-warning-warning-500': 'var(--color-util-colors-warning-warning-500)',
  'util-colors-warning-warning-600': 'var(--color-util-colors-warning-warning-600)',
  'util-colors-warning-warning-700': 'var(--color-util-colors-warning-warning-700)',

  'util-colors-yellow-yellow-50': 'var(--color-util-colors-yellow-yellow-50)',
  'util-colors-yellow-yellow-100': 'var(--color-util-colors-yellow-yellow-100)',
  'util-colors-yellow-yellow-200': 'var(--color-util-colors-yellow-yellow-200)',
  'util-colors-yellow-yellow-300': 'var(--color-util-colors-yellow-yellow-300)',
  'util-colors-yellow-yellow-400': 'var(--color-util-colors-yellow-yellow-400)',
  'util-colors-yellow-yellow-500': 'var(--color-util-colors-yellow-yellow-500)',
  'util-colors-yellow-yellow-600': 'var(--color-util-colors-yellow-yellow-600)',
  'util-colors-yellow-yellow-700': 'var(--color-util-colors-yellow-yellow-700)',

  'util-colors-teal-teal-50': 'var(--color-util-colors-teal-teal-50)',
  'util-colors-teal-teal-100': 'var(--color-util-colors-teal-teal-100)',
  'util-colors-teal-teal-200': 'var(--color-util-colors-teal-teal-200)',
  'util-colors-teal-teal-300': 'var(--color-util-colors-teal-teal-300)',
  'util-colors-teal-teal-400': 'var(--color-util-colors-teal-teal-400)',
  'util-colors-teal-teal-500': 'var(--color-util-colors-teal-teal-500)',
  'util-colors-teal-teal-600': 'var(--color-util-colors-teal-teal-600)',
  'util-colors-teal-teal-700': 'var(--color-util-colors-teal-teal-700)',

  'util-colors-cyan-cyan-50': 'var(--color-util-colors-cyan-cyan-50)',
  'util-colors-cyan-cyan-100': 'var(--color-util-colors-cyan-cyan-100)',
  'util-colors-cyan-cyan-200': 'var(--color-util-colors-cyan-cyan-200)',
  'util-colors-cyan-cyan-300': 'var(--color-util-colors-cyan-cyan-300)',
  'util-colors-cyan-cyan-400': 'var(--color-util-colors-cyan-cyan-400)',
  'util-colors-cyan-cyan-500': 'var(--color-util-colors-cyan-cyan-500)',
  'util-colors-cyan-cyan-600': 'var(--color-util-colors-cyan-cyan-600)',
  'util-colors-cyan-cyan-700': 'var(--color-util-colors-cyan-cyan-700)',

  'util-colors-violet-violet-50': 'var(--color-util-colors-violet-violet-50)',
  'util-colors-violet-violet-100': 'var(--color-util-colors-violet-violet-100)',
  'util-colors-violet-violet-200': 'var(--color-util-colors-violet-violet-200)',
  'util-colors-violet-violet-300': 'var(--color-util-colors-violet-violet-300)',
  'util-colors-violet-violet-400': 'var(--color-util-colors-violet-violet-400)',
  'util-colors-violet-violet-500': 'var(--color-util-colors-violet-violet-500)',
  'util-colors-violet-violet-600': 'var(--color-util-colors-violet-violet-600)',
  'util-colors-violet-violet-700': 'var(--color-util-colors-violet-violet-700)',

  'util-colors-gray-gray-50': 'var(--color-util-colors-gray-gray-50)',
  'util-colors-gray-gray-100': 'var(--color-util-colors-gray-gray-100)',
  'util-colors-gray-gray-200': 'var(--color-util-colors-gray-gray-200)',
  'util-colors-gray-gray-300': 'var(--color-util-colors-gray-gray-300)',
  'util-colors-gray-gray-400': 'var(--color-util-colors-gray-gray-400)',
  'util-colors-gray-gray-500': 'var(--color-util-colors-gray-gray-500)',
  'util-colors-gray-gray-600': 'var(--color-util-colors-gray-gray-600)',
  'util-colors-gray-gray-700': 'var(--color-util-colors-gray-gray-700)',

  'util-colors-green-light-green-light-50': 'var(--color-util-colors-green-light-green-light-50)',
  'util-colors-green-light-green-light-100': 'var(--color-util-colors-green-light-green-light-100)',
  'util-colors-green-light-green-light-200': 'var(--color-util-colors-green-light-green-light-200)',
  'util-colors-green-light-green-light-300': 'var(--color-util-colors-green-light-green-light-300)',
  'util-colors-green-light-green-light-500': 'var(--color-util-colors-green-light-green-light-500)',
  'util-colors-green-light-green-light-400': 'var(--color-util-colors-green-light-green-light-400)',
  'util-colors-green-light-green-light-600': 'var(--color-util-colors-green-light-green-light-600)',
  'util-colors-green-light-green-light-700': 'var(--color-util-colors-green-light-green-light-700)',

  'util-colors-rose-rose-50': 'var(--color-util-colors-rose-rose-50)',
  'util-colors-rose-rose-100': 'var(--color-util-colors-rose-rose-100)',
  'util-colors-rose-rose-200': 'var(--color-util-colors-rose-rose-200)',
  'util-colors-rose-rose-300': 'var(--color-util-colors-rose-rose-300)',
  'util-colors-rose-rose-400': 'var(--color-util-colors-rose-rose-400)',
  'util-colors-rose-rose-500': 'var(--color-util-colors-rose-rose-500)',
  'util-colors-rose-rose-600': 'var(--color-util-colors-rose-rose-600)',
  'util-colors-rose-rose-700': 'var(--color-util-colors-rose-rose-700)',

  'util-colors-midnight-midnight-50': 'var(--color-util-colors-midnight-midnight-50)',
  'util-colors-midnight-midnight-100': 'var(--color-util-colors-midnight-midnight-100)',
  'util-colors-midnight-midnight-200': 'var(--color-util-colors-midnight-midnight-200)',
  'util-colors-midnight-midnight-300': 'var(--color-util-colors-midnight-midnight-300)',
  'util-colors-midnight-midnight-400': 'var(--color-util-colors-midnight-midnight-400)',
  'util-colors-midnight-midnight-500': 'var(--color-util-colors-midnight-midnight-500)',
  'util-colors-midnight-midnight-600': 'var(--color-util-colors-midnight-midnight-600)',
  'util-colors-midnight-midnight-700': 'var(--color-util-colors-midnight-midnight-700)',

  'third-party-LangChain': 'var(--color-third-party-LangChain)',
  'third-party-Langfuse': 'var(--color-third-party-Langfuse)',
  'third-party-Github': 'var(--color-third-party-Github)',
  'third-party-Github-tertiary': 'var(--color-third-party-Github-tertiary)',
  'third-party-Github-secondary': 'var(--color-third-party-Github-secondary)',
  'third-party-model-bg-openai': 'var(--color-third-party-model-bg-openai)',
  'third-party-model-bg-anthropic': 'var(--color-third-party-model-bg-anthropic)',
  'third-party-model-bg-default': 'var(--color-third-party-model-bg-default)',

  'third-party-aws': 'var(--color-third-party-aws)',
  'third-party-aws-alt': 'var(--color-third-party-aws-alt)',

  'saas-background': 'var(--color-saas-background)',
  'saas-pricing-grid-bg': 'var(--color-saas-pricing-grid-bg)',

  'dify-logo-dify-logo-blue': 'var(--color-dify-logo-dify-logo-blue)',
  'dify-logo-dify-logo-black': 'var(--color-dify-logo-dify-logo-black)',

}
export default vars
