html[data-theme="light"] {
    --color-chatbot-bg: linear-gradient(180deg,
            rgba(249, 250, 251, 0.9) 0%,
            rgba(242, 244, 247, 0.9) 90.48%);
    --color-chat-bubble-bg: linear-gradient(180deg,
            #fff 0%,
            rgba(255, 255, 255, 0.6) 100%);
    --color-chat-input-mask: linear-gradient(180deg,
            rgba(255, 255, 255, 0.01) 0%,
            #F2F4F7 100%);
    --color-workflow-process-bg: linear-gradient(90deg,
            rgba(200, 206, 218, 0.2) 0%,
            rgba(200, 206, 218, 0.04) 100%);
    --color-workflow-run-failed-bg: linear-gradient(98deg,
            rgba(240, 68, 56, 0.10) 0%,
            rgba(255, 255, 255, 0) 26.01%);
    --color-workflow-batch-failed-bg: linear-gradient(92deg,
            rgba(240, 68, 56, 0.25) 0%,
            rgba(255, 255, 255, 0) 100%);
    --color-marketplace-divider-bg: linear-gradient(90deg,
            rgba(16, 24, 40, 0.08) 0%,
            rgba(255, 255, 255, 0) 100%);
    --color-marketplace-plugin-empty: linear-gradient(180deg,
            rgba(255, 255, 255, 0) 0%,
            #fcfcfd 100%);
    --color-toast-success-bg: linear-gradient(92deg,
            rgba(23, 178, 106, 0.25) 0%,
            rgba(255, 255, 255, 0) 100%);
    --color-toast-warning-bg: linear-gradient(92deg,
            rgba(247, 144, 9, 0.25) 0%,
            rgba(255, 255, 255, 0) 100%);
    --color-toast-error-bg: linear-gradient(92deg,
            rgba(240, 68, 56, 0.25) 0%,
            rgba(255, 255, 255, 0) 100%);
    --color-toast-info-bg: linear-gradient(92deg,
            rgba(11, 165, 236, 0.25) 0%);
    --color-account-teams-bg: linear-gradient(271deg,
            rgba(249, 250, 251, 0.9) -0.1%,
            rgba(242, 244, 247, 0.9) 98.26%);
    --color-app-detail-bg: linear-gradient(169deg,
            #F2F4F7 1.18%,
            #F9FAFB 99.52%);
    --color-app-detail-overlay-bg: linear-gradient(270deg,
            rgba(0, 0, 0, 0.00) 0%,
            rgba(16, 24, 40, 0.01) 8%,
            rgba(16, 24, 40, 0.18) 100%);
    --color-dataset-chunk-process-success-bg: linear-gradient(92deg, rgba(23, 178, 106, 0.25) 0%, rgba(255, 255, 255, 0.00) 100%);
    --color-dataset-chunk-process-error-bg: linear-gradient(92deg, rgba(240, 68, 56, 0.25) 0%, rgba(255, 255, 255, 0.00) 100%);
    --color-dataset-chunk-detail-card-hover-bg: linear-gradient(180deg, #F2F4F7 0%, #F9FAFB 100%);
    --color-dataset-child-chunk-expand-btn-bg: linear-gradient(90deg, rgba(200, 206, 218, 0.20) 0%, rgba(200, 206, 218, 0.04) 100%);
    --color-dataset-option-card-blue-gradient: linear-gradient(90deg, #F2F4F7 0%, #F9FAFB 100%);
    --color-dataset-option-card-purple-gradient: linear-gradient(90deg, #F0EEFA 0%, #F9FAFB 100%);
    --color-dataset-option-card-orange-gradient: linear-gradient(90deg, #F8F2EE 0%, #F9FAFB 100%);
    --color-dataset-chunk-list-mask-bg: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #FCFCFD 100%);
    --mask-top2bottom-gray-50-to-transparent: linear-gradient(180deg,
            rgba(200, 206, 218, 0.2) 0%,
            rgba(255, 255, 255, 0) 100%);
    --color-line-divider-bg: linear-gradient(90deg, rgba(16, 24, 40, 0.08) 0%, rgba(255, 255, 255, 0) 100%);
    --color-access-app-icon-mask-bg: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
    --color-premium-yearly-tip-text-background: linear-gradient(91deg, #F79009 2.18%, #DC6803 108.79%);
    --color-premium-badge-background: linear-gradient(95deg, rgba(152, 162, 178, 0.90) 0%, rgba(103, 111, 131, 0.90) 105.58%);
    --color-premium-text-background: linear-gradient(92deg, rgba(252, 252, 253, 0.95) 0%, rgba(242, 244, 247, 0.95) 97.78%);
    --color-premium-badge-border-highlight-color: #fffffff2;
    --color-price-enterprise-background: linear-gradient(180deg, rgba(185, 211, 234, 0.00) 0%, rgba(180, 209, 234, 0.92) 100%);
    --color-grid-mask-background: linear-gradient(0deg, #FFF 0%, rgba(217, 217, 217, 0.10) 62.25%, rgba(217, 217, 217, 0.10) 100%);
    --color-background-gradient-bg-fill-chat-bubble-bg-3: #e1effe;
}
