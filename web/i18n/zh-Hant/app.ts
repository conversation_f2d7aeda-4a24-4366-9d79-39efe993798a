const translation = {
  createApp: '建立應用',
  types: {
    all: '全部',
    chatbot: '聊天助手',
    agent: 'Agent',
    workflow: '工作流',
    completion: '文字生成',
    advanced: '聊天流',
    basic: '基本',
  },
  duplicate: '複製',
  duplicateTitle: '複製應用',
  export: '匯出 DSL',
  exportFailed: '匯出 DSL 失敗',
  importDSL: '匯入 DSL 檔案',
  createFromConfigFile: '透過 DSL 檔案建立',
  deleteAppConfirmTitle: '確認刪除應用？',
  deleteAppConfirmContent:
    '刪除應用將無法復原。使用者將無法存取你的應用，所有 Prompt 設定和日誌都將一併被刪除。',
  appDeleted: '應用已刪除',
  appDeleteFailed: '應用刪除失敗',
  join: '參與社群',
  communityIntro: '與團隊成員、貢獻者和開發者在不同頻道中交流',
  roadmap: '產品路線圖',
  newApp: {
    startFromBlank: '建立空白應用',
    startFromTemplate: '從應用模版建立',
    captionAppType: '想要哪種應用類型？',
    chatbotDescription: '使用大型語言模型構建聊天助手',
    completionDescription: '構建一個根據提示生成高品質文字的應用程式，例如生成文章、摘要、翻譯等。',
    completionWarning: '該類型不久後將不再支援建立',
    agentDescription: '構建一個智慧 Agent，可以自主選擇工具來完成任務',
    workflowDescription: '以工作流的形式編排生成型應用，提供更多的自訂設定。它適合有經驗的使用者。',
    workflowWarning: '正在進行 Beta 測試',
    chatbotType: '聊天助手編排方法',
    basic: '基礎編排',
    basicTip: '新手適用，可以切換成工作流編排',
    basicFor: '新手適用',
    basicDescription: '基本編排允許使用簡單的設定編排聊天機器人應用程式，而無需修改內建提示。它適合初學者。',
    advanced: '工作流編排',
    advancedFor: '進階使用者適用',
    advancedDescription: '工作流編排以工作流的形式編排聊天機器人，提供自訂設定，包括編輯內建提示的能力。它適合有經驗的使用者。',
    captionName: '應用名稱 & 圖示',
    appNamePlaceholder: '給你的應用起個名字',
    captionDescription: '描述',
    appDescriptionPlaceholder: '輸入應用的描述',
    useTemplate: '使用該模板',
    previewDemo: '預覽 Demo',
    chatApp: '助手',
    chatAppIntro:
      '我要構建一個聊天場景的應用。該應用採用一問一答模式與使用者持續對話。',
    agentAssistant: '新的智慧助手',
    completeApp: '文字生成應用',
    completeAppIntro:
      '我要構建一個根據提示生成高品質文字的應用，例如生成文章、摘要、翻譯等',
    showTemplates: '我想從範例模板中選擇',
    hideTemplates: '返回應用類型選擇',
    Create: '建立',
    Cancel: '取消',
    nameNotEmpty: '名稱不能為空',
    appTemplateNotSelected: '請選擇應用模版',
    appTypeRequired: '請選擇應用類型',
    appCreated: '應用已建立',
    appCreateFailed: '應用建立失敗',
    caution: '謹慎',
    appCreateDSLErrorPart2: '是否要繼續？',
    appCreateDSLErrorPart3: '目前的應用程式 DSL 版本：',
    Confirm: '確認',
    appCreateDSLErrorTitle: '版本不相容',
    appCreateDSLErrorPart1: '已檢測到 DSL 版本存在顯著差異。強制導入可能會導致應用程式出現故障。',
    appCreateDSLErrorPart4: '系統支援的 DSL 版本：',
    appCreateDSLWarning: '注意：DSL 版本差異可能會影響某些功能',
    learnMore: '瞭解更多資訊',
    optional: '自選',
    foundResults: '{{count}}結果',
    noAppsFound: '未找到應用程式',
    forBeginners: '對於初學者',
    noTemplateFound: '未找到範本',
    noIdeaTip: '沒有想法？查看我們的範本',
    forAdvanced: '對於高級使用者',
    workflowUserDescription: '用於自動化和批處理等單輪任務的工作流編排。',
    chatbotUserDescription: '通過簡單的配置快速構建基於 LLM 的聊天機器人。您可以稍後切換到 Chatflow。',
    completionUserDescription: '通過簡單的配置快速構建用於文本生成任務的 AI 助手。',
    foundResult: '{{count}}結果',
    advancedShortDescription: '具有記憶的複雜多回合對話的工作流程',
    workflowShortDescription: '用於單輪自動化任務的編排',
    chatbotShortDescription: '基於 LLM 的聊天機器人，設置簡單',
    agentShortDescription: '具有推理和自主工具使用的智慧代理',
    noTemplateFoundTip: '嘗試使用不同的關鍵字進行搜索。',
    agentUserDescription: '一個能夠進行反覆運算推理和自主工具使用來實現任務目標的智慧代理。',
    advancedUserDescription: '具有記憶體功能的多輪複雜對話任務的工作流程編排。',
    chooseAppType: '選擇 App Type',
    completionShortDescription: '用於文本生成任務的 AI 助手',
    dropDSLToCreateApp: '將 DSL 檔案拖放到此處以創建應用程式',
  },
  editApp: '編輯資訊',
  editAppTitle: '編輯應用資訊',
  editDone: '應用資訊已更新',
  editFailed: '更新應用資訊失敗',
  iconPicker: {
    ok: '確認',
    cancel: '取消',
    emoji: '表情符號',
    image: '圖片',
  },
  switch: '遷移為工作流編排',
  switchTipStart: '將為您建立一個使用工作流編排的新應用。新應用將',
  switchTip: '不能夠',
  switchTipEnd: '遷移回基礎編排',
  switchLabel: '新應用建立為',
  removeOriginal: '刪除原應用',
  switchStart: '開始遷移',
  typeSelector: {
    all: '所有類型',
    chatbot: '聊天助手',
    agent: 'Agent',
    workflow: '工作流',
    completion: '文字生成',
    advanced: '聊天流',
  },
  tracing: {
    title: '追蹤應用程式效能',
    description: '配置第三方 LLMOps 提供商並追蹤應用程式效能。',
    config: '配置',
    view: '查看',
    collapse: '收起',
    expand: '展開',
    tracing: '追蹤',
    disabled: '已禁用',
    disabledTip: '請先配置提供商',
    enabled: '服務中',
    tracingDescription: '捕獲應用程式執行的完整上下文，包括 LLM 調用、上下文、提示、HTTP 請求等，到第三方追蹤平台。',
    configProviderTitle: {
      configured: '已配置',
      notConfigured: '配置提供商以啟用追蹤',
      moreProvider: '更多提供商',
    },
    arize: {
      title: 'Arize',
      description: '企業級LLM可觀測性、線上與離線評估、監控和實驗平台，基於OpenTelemetry構建，專為LLM和代理驅動的應用程式設計。',
    },
    phoenix: {
      title: 'Phoenix',
      description: '開源且基於OpenTelemetry的可觀測性、評估、提示工程和實驗平台，適用於您的LLM工作流程和代理。',
    },
    langsmith: {
      title: 'LangSmith',
      description: '一個全方位的開發者平台，用於 LLM 驅動的應用程式生命週期的每個步驟。',
    },
    langfuse: {
      title: 'Langfuse',
      description: '追蹤、評估、提示管理和指標，用於調試和改進您的 LLM 應用程式。',
    },
    inUse: '使用中',
    configProvider: {
      title: '配置 ',
      placeholder: '輸入您的{{key}}',
      project: '專案',
      publicKey: '公鑰',
      secretKey: '密鑰',
      viewDocsLink: '查看{{key}}文件',
      removeConfirmTitle: '移除{{key}}配置？',
      removeConfirmContent: '當前配置正在使用中，移除它將關閉追蹤功能。',
    },
    opik: {
      title: '奧皮克',
      description: 'Opik 是一個用於評估、測試和監控 LLM 應用程式的開源平臺。',
    },
    weave: {
      title: '編織',
      description: 'Weave 是一個開源平台，用於評估、測試和監控大型語言模型應用程序。',
    },
    aliyun: {
      title: '雲端監控',
      description: '阿里雲提供的完全管理且無需維護的可觀察性平台，支持即時監控、追蹤和評估 Dify 應用程序。',
    },
  },
  answerIcon: {
    descriptionInExplore: '是否使用 web app 圖示在 Explore 中取代 🤖',
    title: '使用 web app 圖示取代 🤖',
    description: '是否在共享應用程式中使用 web app 圖示進行取代 🤖',
  },
  importFromDSLUrl: '寄件者 URL',
  importFromDSL: '從 DSL 導入',
  importFromDSLFile: '從 DSL 檔',
  importFromDSLUrlPlaceholder: '在此處貼上 DSL 連結',
  mermaid: {
    handDrawn: '手繪',
    classic: '經典',
  },
  openInExplore: '在“探索”中打開',
  newAppFromTemplate: {
    sidebar: {
      Workflow: '工作流',
      Recommended: '推薦',
      Agent: '代理',
      HR: '人力資源',
      Writing: '寫作',
      Programming: '程式設計',
      Assistant: '助理',
    },
    searchAllTemplate: '搜尋所有樣本...',
    byCategories: '按類別',
  },
  showMyCreatedAppsOnly: '我建立的',
  appSelector: {
    placeholder: '選擇應用程式...',
    noParams: '無需參數',
    params: '應用程式參數',
    label: '應用程式',
  },
  structOutput: {
    moreFillTip: '顯示最多 10 層的嵌套',
    required: '必需的',
    LLMResponse: 'LLM 回應',
    structured: '結構化的',
    configure: '配置',
    modelNotSupported: '模型不支持',
    modelNotSupportedTip: '當前模型不支持此功能，並自動降級為提示注入。',
    structuredTip: '結構化輸出是一項功能，確保模型始終生成符合您提供的 JSON 架構的響應。',
    notConfiguredTip: '結構化輸出尚未配置',
  },
  accessItemsDescription: {
    anyone: '任何人都可以訪問這個網絡應用程式',
    specific: '只有特定的群體或成員可以訪問這個網絡應用程序',
    organization: '組織中的任何人都可以訪問該網絡應用程序',
    external: '只有經過身份驗證的外部用戶才能訪問該網絡應用程序',
  },
  accessControlDialog: {
    accessItems: {
      anyone: '擁有鏈接的人',
      specific: '特定群體或成員',
      organization: '只有企業內部成員',
      external: '經過驗證的外部用戶',
    },
    operateGroupAndMember: {
      searchPlaceholder: '搜尋群組和成員',
      allMembers: '所有成員',
      expand: '擴大',
      noResult: '沒有結果',
    },
    title: '網頁應用程式存取控制',
    description: '設定網頁應用程式訪問權限',
    accessLabel: '誰可以訪問',
    groups_one: '{{count}} 群組',
    groups_other: '{{count}} 組',
    members_one: '{{count}} 成員',
    members_other: '{{count}} 成員',
    noGroupsOrMembers: '未選擇任何群組或成員',
    webAppSSONotEnabledTip: '請聯絡企業管理員配置網頁應用程式的身份驗證方法。',
    updateSuccess: '更新成功',
  },
  publishApp: {
    title: '誰可以訪問網絡應用程序',
    notSet: '未設定',
    notSetDesc: '目前沒有人能夠訪問網絡應用程序。請設置權限。',
  },
  accessControl: '網頁應用程式存取控制',
  noAccessPermission: '沒有權限訪問網絡應用程式',
  maxActiveRequestsPlaceholder: '輸入 0 以表示無限',
  maxActiveRequests: '同時最大請求數',
}

export default translation
