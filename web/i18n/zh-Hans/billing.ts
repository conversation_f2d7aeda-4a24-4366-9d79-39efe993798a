const translation = {
  currentPlan: '当前套餐',
  usagePage: {
    teamMembers: '团队成员',
    buildApps: '构建应用程序数',
    annotationQuota: '标注回复数',
    documentsUploadQuota: '文档上传配额',
    vectorSpace: '知识库数据存储空间',
    vectorSpaceTooltip: '采用高质量索引模式的文档会消耗知识数据存储资源。当知识数据存储达到限制时，将不会上传新文档。',
  },
  upgradeBtn: {
    plain: '查看套餐',
    encourage: '立即升级',
    encourageShort: '升级',
  },
  viewBilling: '管理账单及订阅',
  buyPermissionDeniedTip: '请联系企业管理员订阅',
  plansCommon: {
    title: '为您的 AI 之旅提供动力的定价套餐',
    freeTrialTipPrefix: '注册即可',
    freeTrialTip: '免费试用 200 个 OpenAI 消息额度',
    freeTrialTipSuffix: '。无需信用卡',
    yearlyTip: '支付 10 个月，享受 1 年！',
    mostPopular: '最受欢迎',
    cloud: '云服务',
    self: '自部署',
    planRange: {
      monthly: '按月',
      yearly: '按年',
    },
    month: '月',
    year: '年',
    save: '节省',
    free: '免费',
    annualBilling: '按年计费',
    comparePlanAndFeatures: '对比套餐 & 功能特性',
    priceTip: '每个团队空间/',
    currentPlan: '当前计划',
    contractSales: '联系销售',
    contractOwner: '联系团队管理员',
    startForFree: '免费开始',
    getStarted: '立即开始',
    contactSales: '联系销售',
    talkToSales: '联系销售',
    modelProviders: '支持 OpenAI/Anthropic/Llama2/Azure OpenAI/Hugging Face/Replicate',
    teamWorkspace: '{{count,number}} 个团队空间',
    teamMember_one: '{{count,number}} 名团队成员',
    teamMember_other: '{{count,number}} 名团队成员',
    annotationQuota: '标注回复数',
    buildApps: '{{count, number}} 个应用程序',
    documents: '{{count, number}} 个知识库文档上传配额',
    documentsTooltip: '从知识库的数据源导入的文档数量配额。',
    vectorSpace: '{{size}} 知识库数据存储空间',
    vectorSpaceTooltip: '采用高质量索引模式的文档会消耗知识数据存储资源。当知识数据存储达到限制时，将不会上传新文档。',
    documentsRequestQuota: '{{count,number}}/分钟 知识库请求频率限制',
    documentsRequestQuotaTooltip: '指每分钟内，一个空间在知识库中可执行的操作总数，包括数据集的创建、删除、更新，文档的上传、修改、归档，以及知识库查询等，用于评估知识库请求的性能。例如，Sandbox 用户在 1 分钟内连续执行 10 次命中测试，其工作区将在接下来的 1 分钟内无法继续执行以下操作：数据集的创建、删除、更新，文档的上传、修改等操作。',
    apiRateLimit: 'API 请求频率限制',
    apiRateLimitUnit: '{{count,number}} 次/天',
    unlimitedApiRate: 'API 请求频率无限制',
    apiRateLimitTooltip: 'API 请求频率限制涵盖所有通过 Dify API 发起的调用，例如文本生成、聊天对话、工作流执行和文档处理等。',
    documentProcessingPriority: '文档处理',
    documentProcessingPriorityUpgrade: '以更快的速度、更高的精度处理更多的数据。',
    priority: {
      'standard': '标准',
      'priority': '优先',
      'top-priority': '最高优先级',
    },
    logsHistory: '{{days}}日志历史',
    customTools: '自定义工具',
    unavailable: '不可用',
    days: '天',
    unlimited: '无限制',
    support: '支持',
    supportItems: {
      communityForums: '社区论坛',
      emailSupport: '电子邮件支持',
      priorityEmail: '优先电子邮件和聊天支持',
      logoChange: 'Logo 更改',
      SSOAuthentication: 'SSO 认证',
      personalizedSupport: '个性化支持',
      dedicatedAPISupport: '专用 API 支持',
      customIntegration: '自定义集成和支持',
      ragAPIRequest: 'RAG API 请求',
      bulkUpload: '批量上传文档',
      agentMode: '代理模式',
      workflow: '工作流',
      llmLoadingBalancing: 'LLM 负载均衡',
      llmLoadingBalancingTooltip: '向模型添加多个 API 密钥，有效绕过 API 速率限制。',
    },
    comingSoon: '即将推出',
    member: '成员',
    memberAfter: '个成员',
    messageRequest: {
      title: '{{count,number}} 条消息额度',
      titlePerMonth: '{{count,number}} 条消息额度/月',
      tooltip: '消息额度旨在帮助您便捷地试用 Dify 中的各类 OpenAI 模型。不同模型会消耗不同额度。额度用尽后，您可以切换为使用自己的 OpenAI API 密钥。',
    },
    annotatedResponse: {
      title: '{{count,number}} 个标注回复数',
      tooltip: '标注回复功能通过人工编辑标注为应用提供了可定制的高质量问答回复能力。',
    },
    ragAPIRequestTooltip: '指单独调用 Dify 知识库数据处理能力的 API。',
    receiptInfo: '只有团队所有者和团队管理员才能订阅和查看账单信息',
  },
  plans: {
    sandbox: {
      name: 'Sandbox',
      for: '核心能力的免费试用',
      description: '核心功能免费试用',
    },
    professional: {
      name: 'Professional',
      for: '适合独立开发者或小团队',
      description: '对于独立开发者/小团队',
    },
    team: {
      name: 'Team',
      for: '适合中等规模的团队',
      description: '对于中型团队',
    },
    community: {
      name: 'Community',
      for: '适用于个人用户、小型团队或非商业项目',
      description: '适用于个人用户、小型团队或非商业项目',
      price: '免费',
      btnText: '开始使用',
      includesTitle: '免费功能：',
      features: [
        '所有核心功能均在公共存储库下发布',
        '单一工作空间',
        '符合 Dify 开源许可证',
      ],
    },
    premium: {
      name: 'Premium',
      for: '对于中型组织和团队',
      description: '对于中型组织和团队',
      price: '可扩展',
      priceTip: '基于云市场',
      btnText: '获得 Premium 版',
      includesTitle: 'Community 版的所有功能，加上：',
      comingSoon: '即将支持 Microsoft Azure & Google Cloud',
      features: [
        '各个云提供商自行管理的可靠性',
        '单一工作空间',
        '自定义 WebApp & 品牌',
        '优先电子邮件 & 聊天支持',
      ],
    },
    enterprise: {
      name: 'Enterprise',
      for: '适合大人员规模的团队',
      description: '对于需要组织范围内的安全性、合规性、可扩展性、控制和更高级功能的企业',
      price: '定制',
      priceTip: '仅按年计费',
      btnText: '联系销售',
      includesTitle: 'Premium 版的所有功能，加上：',
      features: [
        '企业级可扩展部署解决方案',
        '商业许可授权',
        '专属企业级功能',
        '多个工作空间 & 企业级管理',
        'SSO',
        '由 Dify 合作伙伴支持的可协商的 SLAs',
        '高级的安全 & 控制',
        '由 Dify 官方提供的更新 & 维护',
        '专业技术支持',
      ],
    },
  },
  vectorSpace: {
    fullTip: '知识库数据存储空间已满。',
    fullSolution: '升级您的套餐以获得更多空间。',
  },
  apps: {
    fullTip1: '升级以创建更多应用',
    fullTip1des: '您已达到此计划上构建应用的限制',
    fullTip2: '计划限制已达到',
    fullTip2des: '推荐您清理不活跃的应用或者联系我们',
    contactUs: '联系我们',
  },
  annotatedResponse: {
    fullTipLine1: '升级您的套餐以',
    fullTipLine2: '标注更多对话。',
    quotaTitle: '标注的配额',
  },
  teamMembers: '团队成员',
}

export default translation
