const translation = {
  category: {
    all: 'Tot',
    bundles: 'Pachete',
    agents: 'Strategii pentru agenți',
    tools: 'Instrumente',
    extensions: 'Extensii',
    models: 'Modele',
  },
  categorySingle: {
    tool: 'Unealtă',
    bundle: 'Pachet',
    extension: 'Extensie',
    agent: 'Strategia agentului',
    model: 'Model',
  },
  list: {
    source: {
      marketplace: 'Instalează din Marketplace',
      github: 'Instalați din GitHub',
      local: 'Instalare din fișierul pachet local',
    },
    noInstalled: 'Nu sunt instalate plugin-uri',
    notFound: 'Nu au fost găsite plugin-uri',
  },
  source: {
    local: 'Fișier pachet local',
    marketplace: 'Târg',
    github: 'GitHub',
  },
  detailPanel: {
    categoryTip: {
      debugging: 'Plugin de depanare',
      github: 'Instalat de pe Github',
      marketplace: 'Instalat din Marketplace',
      local: 'Plugin local',
    },
    operation: {
      checkUpdate: 'Verificați actualizarea',
      update: 'Actualiza',
      viewDetail: 'Vezi detalii',
      remove: 'Depărta',
      install: 'Instala',
      detail: 'Detalii',
      info: 'Informații despre plugin',
    },
    toolSelector: {
      unsupportedContent: 'Versiunea de plugin instalată nu oferă această acțiune.',
      auto: 'Auto',
      empty: 'Faceți clic pe butonul "+" pentru a adăuga instrumente. Puteți adăuga mai multe instrumente.',
      uninstalledContent: 'Acest plugin este instalat din depozitul local/GitHub. Vă rugăm să utilizați după instalare.',
      descriptionLabel: 'Descrierea instrumentului',
      unsupportedContent2: 'Faceți clic pentru a comuta versiunea.',
      uninstalledLink: 'Gestionați în pluginuri',
      paramsTip1: 'Controlează parametrii de inferență LLM.',
      params: 'CONFIGURAREA RAȚIONAMENTULUI',
      paramsTip2: 'Când "Auto" este dezactivat, se folosește valoarea implicită.',
      settings: 'SETĂRI UTILIZATOR',
      unsupportedTitle: 'Acțiune neacceptată',
      placeholder: 'Selectați un instrument...',
      title: 'Adăugare instrument',
      descriptionPlaceholder: 'Scurtă descriere a scopului instrumentului, de exemplu, obțineți temperatura pentru o anumită locație.',
      toolLabel: 'Unealtă',
      uninstalledTitle: 'Instrumentul nu este instalat',
      toolSetting: 'Setările instrumentului',
      unsupportedMCPTool: 'Versiunea pluginului de strategie a agentului selectat în prezent nu suportă uneltele MCP.',
    },
    endpointDeleteContent: 'Doriți să eliminați {{name}}?',
    strategyNum: '{{num}} {{strategie}} INCLUS',
    configureApp: 'Configurați aplicația',
    actionNum: '{{num}} {{acțiune}} INCLUS',
    endpointsTip: 'Acest plugin oferă funcționalități specifice prin puncte finale și puteți configura mai multe seturi de puncte finale pentru spațiul de lucru curent.',
    switchVersion: 'Versiune de comutare',
    endpointDisableContent: 'Doriți să dezactivați {{name}}?',
    endpointModalTitle: 'Configurați punctul final',
    endpointDisableTip: 'Dezactivați punctul final',
    endpointsEmpty: 'Faceți clic pe butonul "+" pentru a adăuga un punct final',
    endpointDeleteTip: 'Eliminați punctul final',
    disabled: 'Dezactivat',
    configureTool: 'Instrumentul de configurare',
    endpointsDocLink: 'Vizualizați documentul',
    endpoints: 'Capetele',
    serviceOk: 'Serviciu OK',
    endpointModalDesc: 'Odată configurate, pot fi utilizate funcțiile furnizate de plugin prin intermediul punctelor finale API.',
    modelNum: '{{num}} MODELE INCLUSE',
    configureModel: 'Configurarea modelului',
    deprecation: {
      reason: {
        businessAdjustments: 'ajustări de afaceri',
        noMaintainer: 'fără întreținător',
        ownershipTransferred: 'proprietatea transferată',
      },
      noReason: 'Acest plugin a fost declarat învechit și nu va mai fi actualizat.',
      onlyReason: 'Acest plugin a fost depreciat din cauza {{deprecatedReason}} și nu va mai fi actualizat.',
      fullMessage: 'Acest plugin a fost declarat învechit din cauza {{deprecatedReason}}, și nu va mai fi actualizat. Vă rugăm să folosiți în schimb <CustomLink href=\'https://example.com/\'>{{-alternativePluginId}}</CustomLink>.',
    },
  },
  debugInfo: {
    viewDocs: 'Vizualizați documentele',
    title: 'Depanare',
  },
  privilege: {
    whoCanDebug: 'Cine poate depana pluginuri?',
    everyone: 'Oricine',
    title: 'Preferințe plugin',
    whoCanInstall: 'Cine poate instala și gestiona plugin-uri?',
    noone: 'Nimeni',
    admins: 'Administratori',
  },
  pluginInfoModal: {
    release: 'Elibera',
    packageName: 'Pachet',
    title: 'Informații despre plugin',
    repository: 'Depozit',
  },
  action: {
    deleteContentRight: 'plugin?',
    pluginInfo: 'Informații despre plugin',
    usedInApps: 'Acest plugin este folosit în aplicațiile {{num}}.',
    delete: 'Eliminați pluginul',
    checkForUpdates: 'Verificați dacă există actualizări',
    deleteContentLeft: 'Doriți să eliminați',
  },
  installModal: {
    labels: {
      version: 'Versiune',
      package: 'Pachet',
      repository: 'Depozit',
    },
    installing: 'Instalarea...',
    dropPluginToInstall: 'Aruncați pachetul de plugin aici pentru a instala',
    back: 'Spate',
    installFailed: 'Instalarea a eșuat',
    pluginLoadError: 'Eroare de încărcare a pluginului',
    installComplete: 'Instalare finalizată',
    installedSuccessfully: 'Instalarea cu succes',
    cancel: 'Anula',
    install: 'Instala',
    uploadingPackage: 'Încărcarea {{packageName}}...',
    installPlugin: 'Instalează pluginul',
    close: 'Închide',
    readyToInstallPackages: 'Despre instalarea următoarelor plugin-uri {{num}}',
    next: 'Următor',
    installFailedDesc: 'Pluginul a fost instalat a eșuat.',
    uploadFailed: 'Încărcarea a eșuat',
    fromTrustSource: 'Vă rugăm să vă asigurați că instalați plugin-uri numai dintr-o <trustSource>sursă de încredere</trustSource>.',
    readyToInstallPackage: 'Despre instalarea următorului plugin',
    pluginLoadErrorDesc: 'Acest plugin nu va fi instalat',
    installedSuccessfullyDesc: 'Pluginul a fost instalat cu succes.',
    readyToInstall: 'Despre instalarea următorului plugin',
    installWarning: 'Acest plugin nu este permis să fie instalat.',
  },
  installFromGitHub: {
    installFailed: 'Instalarea a eșuat',
    updatePlugin: 'Actualizați pluginul de pe GitHub',
    uploadFailed: 'Încărcarea a eșuat',
    selectVersionPlaceholder: 'Vă rugăm să selectați o versiune',
    installNote: 'Vă rugăm să vă asigurați că instalați plugin-uri numai dintr-o sursă de încredere.',
    gitHubRepo: 'Depozit GitHub',
    selectPackagePlaceholder: 'Vă rugăm să selectați un pachet',
    selectPackage: 'Selectează pachetul',
    selectVersion: 'Selectează versiunea',
    installPlugin: 'Instalați pluginul de pe GitHub',
    installedSuccessfully: 'Instalarea cu succes',
  },
  upgrade: {
    close: 'Închide',
    upgrade: 'Instala',
    description: 'Despre instalarea următorului plugin',
    upgrading: 'Instalarea...',
    successfulTitle: 'Instalarea cu succes',
    title: 'Instalează pluginul',
    usedInApps: 'Folosit în {{num}} aplicații',
  },
  error: {
    fetchReleasesError: 'Nu se pot recupera versiunile. Vă rugăm să încercați din nou mai târziu.',
    inValidGitHubUrl: 'URL GitHub nevalid. Vă rugăm să introduceți o adresă URL validă în formatul: https://github.com/owner/repo',
    noReleasesFound: 'Nu s-au găsit eliberări. Vă rugăm să verificați depozitul GitHub sau URL-ul de intrare.',
  },
  marketplace: {
    sortOption: {
      newlyReleased: 'Nou lansat',
      recentlyUpdated: 'Actualizat recent',
      mostPopular: 'Cele mai populare',
      firstReleased: 'Prima lansare',
    },
    noPluginFound: 'Nu s-a găsit niciun plugin',
    sortBy: 'Sortează după',
    discover: 'Descoperi',
    empower: 'Îmbunătățește-ți dezvoltarea AI',
    pluginsResult: '{{num}} rezultate',
    difyMarketplace: 'Piața Dify',
    moreFrom: 'Mai multe din Marketplace',
    and: 'și',
    viewMore: 'Vezi mai mult',
    partnerTip: 'Verificat de un partener Dify',
    verifiedTip: 'Verificat de Dify',
  },
  task: {
    installError: '{{errorLength}} plugin-urile nu s-au instalat, faceți clic pentru a vizualiza',
    clearAll: 'Ștergeți tot',
    installedError: '{{errorLength}} plugin-urile nu s-au instalat',
    installingWithError: 'Instalarea pluginurilor {{installingLength}}, {{successLength}} succes, {{errorLength}} eșuat',
    installingWithSuccess: 'Instalarea pluginurilor {{installingLength}}, {{successLength}} succes.',
    installing: 'Instalarea pluginurilor {{installingLength}}, 0 terminat.',
  },
  fromMarketplace: 'Din Marketplace',
  from: 'Din',
  findMoreInMarketplace: 'Află mai multe în Marketplace',
  searchInMarketplace: 'Căutare în Marketplace',
  searchTools: 'Instrumente de căutare...',
  installFrom: 'INSTALEAZĂ DE LA',
  allCategories: 'Toate categoriile',
  searchPlugins: 'Pluginuri de căutare',
  installPlugin: 'Instalează pluginul',
  install: '{{num}} instalări',
  search: 'Căutare',
  installAction: 'Instala',
  endpointsEnabled: '{{num}} seturi de puncte finale activate',
  searchCategories: 'Categorii de căutare',
  metadata: {
    title: 'Pluginuri',
  },
  difyVersionNotCompatible: 'Versiunea curentă Dify nu este compatibilă cu acest plugin, vă rugăm să faceți upgrade la versiunea minimă necesară: {{minimalDifyVersion}}',
  requestAPlugin: 'Solicitați un plugin',
  publishPlugins: 'Publicați pluginuri',
  auth: {
    saveAndAuth: 'Salvează și Autorizează',
    authRemoved: 'Autentificare eliminată',
    custom: 'Personalizat',
    addApi: 'Adăugați cheia API',
    useOAuthAuth: 'Folosește autorizarea OAuth',
    default: 'Default',
    saveOnly: 'Salvează doar',
    authorizationName: 'Numele autorizării',
    oauthClientSettings: 'Setările clientului OAuth',
    authorization: 'Autorizație',
    useOAuth: 'Folosește OAuth',
    authorizations: 'Autorizări',
    workspaceDefault: 'Spațiul de lucru implicit',
    setDefault: 'Setați ca implicit',
    addOAuth: 'Adăugați OAuth',
    useApiAuth: 'Configurarea autorizării cheii API',
    useApi: 'Folosește cheia API',
    oauthClient: 'Client OAuth',
    setupOAuth: 'Configurați clientul OAuth',
    useApiAuthDesc: 'După configurarea acreditivelor, toți membrii din spațiul de lucru pot folosi acest instrument atunci când orchestran aplicații.',
    clientInfo: 'Deoarece nu s-au găsit secretele clientului sistemului pentru acest furnizor de instrumente, este necesară configurarea manuală; pentru redirect_uri, vă rugăm să folosiți',
  },
  deprecated: 'Încetat de a mai fi utilizat',
  autoUpdate: {
    strategy: {
      disabled: {
        description: 'Pluginurile nu se vor actualiza automat',
        name: 'Dezactivat',
      },
      fixOnly: {
        selectedDescription: 'Actualizare automată doar pentru versiuni patch',
        name: 'Fix doar',
      },
      latest: {
        name: 'Ultimul',
        selectedDescription: 'Actualizați întotdeauna la cea mai recentă versiune',
        description: 'Actualizați întotdeauna la cea mai recentă versiune',
      },
    },
    upgradeMode: {
      exclude: 'Excluzi selecția',
      all: 'Actualizează tot',
      partial: 'Numai selectat',
    },
    upgradeModePlaceholder: {
      exclude: 'Pluginurile selectate nu se vor actualiza automat.',
      partial: 'Numai pluginurile selectate se vor actualiza automat. Nu există pluginuri selectate în prezent, așa că niciun plugin nu se va actualiza automat.',
    },
    operation: {
      select: 'Selectați plugin-uri',
      clearAll: 'Șterge tot',
    },
    pluginDowngradeWarning: {
      title: 'Scădere a pluginului',
      exclude: 'Exclude de la actualizarea automată',
      downgrade: 'Oricum, downgradează',
      description: 'Actualizarea automată este în prezent activată pentru acest plugin. Revenirea la o versiune anterioară poate provoca suprascrierea modificărilor tale în timpul următoarei actualizări automate.',
    },
    noPluginPlaceholder: {
      noFound: 'Nu au fost găsite plugin-uri',
      noInstalled: 'Niciun plugin instalat',
    },
    excludeUpdate: 'Următoarele {{num}} pluginuri nu se vor actualiza automat',
    updateTimeTitle: 'Timp de actualizare',
    updateSettings: 'Actualizează setările',
    changeTimezone: 'Pentru a schimba fusul orar, mergi la <setTimezone>Setări</setTimezone>',
    automaticUpdates: 'Actualizări automate',
    specifyPluginsToUpdate: 'Specificați plugin-urile de actualizat',
    partialUPdate: 'Numai următoarele {{num}} pluginuri se vor actualiza automat',
    updateTime: 'Timp de actualizare',
    nextUpdateTime: 'Următoarea actualizare automată: {{time}}',
  },
}

export default translation
