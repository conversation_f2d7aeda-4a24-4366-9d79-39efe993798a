const translation = {
  input: 'INTRARE',
  result: 'REZULTAT',
  detail: 'DETALIU',
  tracing: 'URMĂRIRE',
  resultPanel: {
    status: 'STARE',
    time: 'TIMP SCURS',
    tokens: 'TOTAL TOKENI',
  },
  meta: {
    title: 'METADATE',
    status: 'Stare',
    version: 'V<PERSON>iu<PERSON>',
    executor: 'Executor',
    startTime: 'Timp de început',
    time: 'Timp scurs',
    tokens: 'Total tokeni',
    steps: 'Pași de rulare',
  },
  resultEmpty: {
    title: 'Această rulare generează doar format JSON,',
    tipLeft: 'vă rugăm să mergeți la ',
    link: 'panoul de detalii',
    tipRight: ' pentru a o vizualiza.',
  },
  actionLogs: 'Jurnale de acțiuni',
  circularInvocationTip: 'Există o invocare circulară a instrumentelor/nodurilor în fluxul de lucru curent.',
}

export default translation
