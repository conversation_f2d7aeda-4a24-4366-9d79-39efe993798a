const translation = {
  title: '<PERSON><PERSON><PERSON>',
  desc: 'Burada Bilginin özelliklerini ve çalışma yöntemlerini değiştirebilirsiniz.',
  form: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    namePlaceholder: '<PERSON><PERSON><PERSON> ismini girin',
    nameError: '<PERSON><PERSON><PERSON> boş olamaz',
    desc: '<PERSON>ilgi açıklaması',
    descInfo: 'Lütfen Bilginin içeriğini özetlemek için net bir metinsel açıklama yazın. Bu açıklama, çıkarım için birden fazla Bilgi arasından seçim yaparken eşleştirme temeli olarak kullanılacaktır.',
    descPlaceholder: '<PERSON>u Bilginin içeriğini açıklayın. Ayrıntılı bir açıklama, AI\'nın Bilginin içeriğine zamanında erişmesini sağlar. <PERSON><PERSON> b<PERSON>rak<PERSON>l<PERSON><PERSON>, <PERSON><PERSON> var<PERSON>lan isabet stratejisini kullanır.',
    descWrite: '<PERSON>yi bir Bilgi açıklamasının nasıl yazılacağını öğrenin.',
    permissions: '<PERSON>zin<PERSON>',
    permissionsOnlyMe: 'Sadece ben',
    permissionsAllMember: 'Tüm takım üyeleri',
    permissionsInvitedMembers: 'Bazı takım üyeleri',
    me: '(Siz)',
    indexMethod: 'Dizin Yöntemi',
    indexMethodHighQuality: 'Yüksek Kalite',
    indexMethodHighQualityTip: 'Kullanıcılar sorguladığında daha yüksek doğruluk sağlamak için Yerleştirme modelini çağırır.',
    indexMethodEconomy: 'Ekonomik',
    indexMethodEconomyTip: 'Doğruluğu azaltmak için çevrimdışı vektör motorları, anahtar kelime dizinleri vb. kullanın, token harcamadan',
    embeddingModel: 'Yerleştirme Modeli',
    embeddingModelTip: 'Yerleştirme modelini değiştirmek için, lütfen ',
    embeddingModelTipLink: 'Ayarlar\'a gidin',
    retrievalSetting: {
      title: 'Geri Alım Ayarı',
      learnMore: 'Daha fazla bilgi edinin',
      description: ' geri alım yöntemi hakkında.',
      longDescription: ' geri alım yöntemi hakkında, bunu Bilgi ayarlarında istediğiniz zaman değiştirebilirsiniz.',
      method: 'Retrieval Yöntemi',
    },
    save: 'Kaydet',
    retrievalSettings: 'Alma Ayarları',
    externalKnowledgeAPI: 'Harici Bilgi API\'si',
    externalKnowledgeID: 'Harici Bilgi Kimliği',
    upgradeHighQualityTip: 'Yüksek Kalite moduna yükselttikten sonra Ekonomik moda geri dönülemez',
    indexMethodChangeToEconomyDisabledTip: 'Genel Merkezden ECO\'ya düşürme için mevcut değil',
    helpText: 'İyi bir veri kümesi açıklamasının nasıl yazılacağını öğrenin.',
    searchModel: 'Model Ara',
  },
}

export default translation
