const translation = {
  title: '<PERSON><PERSON>',
  desc: 'Verilen sorgu metnine göre Bilginin isabet etkisini test edin.',
  dateTimeFormat: 'GG/AA/YYYY ss:dd ÖÖ/ÖS',
  recents: 'Sonuçlar',
  table: {
    header: {
      source: '<PERSON><PERSON><PERSON>',
      text: 'Metin',
      time: '<PERSON><PERSON>',
    },
  },
  input: {
    title: '<PERSON><PERSON><PERSON> metin',
    placeholder: 'Bir metin girin, kısa bir bildirim cümlesi önerilir.',
    countWarning: 'En fazla 200 karakter.',
    indexWarning: '<PERSON>üksek kaliteli Bilgi sadece.',
    testing: 'Test Ediliyor',
  },
  hit: {
    title: 'GERİ ALINAN PARAGRAFLAR',
    emptyTip: 'Geri Alım Testi sonuçları burada gösterilecektir',
  },
  noRecentTip: 'Burada son sorgu sonuçları yok',
  viewChart: 'VEKTÖR GRAFİĞİNİ GÖRÜNTÜLE',
  viewDetail: 'ayrıntılara bakın',
  settingTitle: '<PERSON>',
  open: '<PERSON><PERSON><PERSON><PERSON>',
  chunkDetail: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  keyword: '<PERSON><PERSON><PERSON> kelime -ler',
  hitChunks: '{{num}} alt parçalarına basın',
  records: 'Kayıt',
}

export default translation
