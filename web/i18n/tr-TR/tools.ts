const translation = {
  title: '<PERSON><PERSON><PERSON>',
  createCustomTool: '<PERSON>zel Araç Oluştur',
  customToolTip: 'Dify özel araçları hakkında daha fazla bilgi edinin',
  type: {
    all: 'Hepsi',
    builtIn: 'Ye<PERSON><PERSON><PERSON>',
    custom: '<PERSON>zel',
    workflow: 'Workflow',
  },
  contribute: {
    line1: 'Dify\'ye ',
    line2: 'araçlar eklemekle ilgileniyorum.',
    viewGuide: '<PERSON><PERSON><PERSON><PERSON>ü<PERSON>ü<PERSON>',
  },
  author: 'Tarafından',
  auth: {
    authorized: 'Yetkilendirildi',
    setup: 'Kullanmak için yetkilendirmeyi ayarla',
    setupModalTitle: 'Yetkilendirmeyi Ayarla',
    setupModalTitleDescription: 'Kimlik bilgilerini yapılandırdıktan sonra, çalışma alanındaki tüm üyeler uygulamaları düzenlerken bu aracı kullanabilir.',
  },
  includeToolNum: '{{num}} araç dahil',
  addTool: '<PERSON><PERSON>',
  addToolModal: {
    type: 'Tür',
    category: 'Kate<PERSON><PERSON>',
    add: 'Ekle',
    added: 'Eklendi',
    manageInTools: '<PERSON><PERSON>larda Yönet',
    custom: {
      title: 'Mevcut özel araç yok',
      tip: 'Özel bir araç oluşturun',
    },
    workflow: {
      title: 'Mevcut iş akışı aracı yok',
      tip: 'İş akışlarını Studio\'da araç olarak yayınlayın',
    },
    mcp: {
      title: 'Mevcut MCP aracı yok',
      tip: 'Bir MCP sunucusu ekleyin',
    },
    agent: {
      title: 'Mevcut ajan stratejisi yok',
    },
  },
  createTool: {
    title: 'Özel Araç Oluştur',
    editAction: 'Yapılandır',
    editTitle: 'Özel Aracı Düzenle',
    name: 'İsim',
    toolNamePlaceHolder: 'Araç ismini girin',
    nameForToolCall: 'Araç çağrı adı',
    nameForToolCallPlaceHolder: 'Makine tanıması için kullanılır, örneğin getCurrentWeather, list_pets',
    nameForToolCallTip: 'Sadece rakamlar, harfler ve alt çizgileri destekler.',
    description: 'Açıklama',
    descriptionPlaceholder: 'Araç amacının kısa açıklaması, örneğin belirli bir yer için sıcaklığı al.',
    schema: 'Şema',
    schemaPlaceHolder: 'OpenAPI şemanızı buraya girin',
    viewSchemaSpec: 'OpenAPI-Swagger Spesifikasyonunu Görüntüle',
    importFromUrl: 'URL\'den İçe Aktar',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Geçerli bir URL girin',
    examples: 'Örnekler',
    exampleOptions: {
      json: 'Hava Durumu (JSON)',
      yaml: 'Evcil Hayvan Mağazası (YAML)',
      blankTemplate: 'Boş Şablon',
    },
    availableTools: {
      title: 'Kullanılabilir Araçlar',
      name: 'İsim',
      description: 'Açıklama',
      method: 'Yöntem',
      path: 'Yol',
      action: 'Eylemler',
      test: 'Test',
    },
    authMethod: {
      title: 'Yetkilendirme yöntemi',
      type: 'Yetkilendirme türü',
      keyTooltip: 'Http Başlığı Anahtarı, ne olduğunu bilmiyorsanız "Authorization" olarak bırakabilirsiniz veya özel bir değere ayarlayabilirsiniz',
      types: {
        none: 'Yok',
        api_key: 'API Anahtarı',
        apiKeyPlaceholder: 'API Anahtarı için HTTP başlık adı',
        apiValuePlaceholder: 'API Anahtarını girin',
        api_key_header: 'Başlık',
        queryParamPlaceholder: 'API Anahtarı için Sorgu parametre adı',
        api_key_query: 'Sorgu Parametre',
      },
      key: 'Anahtar',
      value: 'Değer',
      queryParam: 'Sorgu Parametresi',
      queryParamTooltip: 'API anahtarı sorgu parametresinin adı, örneğin "key" değeri "https://example.com/test?key=API_KEY" adresinde.',
    },
    authHeaderPrefix: {
      title: 'Yetki Türü',
      types: {
        basic: 'Temel',
        bearer: 'Bearer',
        custom: 'Özel',
      },
    },
    privacyPolicy: 'Gizlilik politikası',
    privacyPolicyPlaceholder: 'Gizlilik politikasını girin',
    toolInput: {
      title: 'Araç Girişi',
      name: 'İsim',
      required: 'Gerekli',
      method: 'Yöntem',
      methodSetting: 'Ayar',
      methodSettingTip: 'Kullanıcı araç yapılandırmasını doldurur',
      methodParameter: 'Parametre',
      methodParameterTip: 'Çıkarım sırasında LLM tarafından doldurulur',
      label: 'Etiketler',
      labelPlaceholder: 'Etiketleri seç (isteğe bağlı)',
      description: 'Açıklama',
      descriptionPlaceholder: 'Parametrenin anlamının açıklaması',
    },
    customDisclaimer: 'Özel feragatname',
    customDisclaimerPlaceholder: 'Özel feragatnameyi girin',
    confirmTitle: 'Kaydetmek için onaylıyor musunuz?',
    confirmTip: 'Bu aracı kullanan uygulamalar etkilenecek',
    deleteToolConfirmTitle: 'Bu Aracı silmek istiyor musunuz?',
    deleteToolConfirmContent: 'Aracın silinmesi geri alınamaz. Kullanıcılar artık aracınıza erişemeyecek.',
  },
  test: {
    title: 'Test',
    parametersValue: 'Parametreler ve Değer',
    parameters: 'Parametreler',
    value: 'Değer',
    testResult: 'Test Sonuçları',
    testResultPlaceholder: 'Test sonucu burada gösterilecektir',
  },
  thought: {
    using: 'Kullanılıyor',
    used: 'Kullanıldı',
    requestTitle: 'İstek',
    responseTitle: 'Yanıt',
  },
  setBuiltInTools: {
    info: 'Bilgi',
    setting: 'Ayar',
    toolDescription: 'Araç açıklaması',
    parameters: 'parametreler',
    string: 'string',
    number: 'numara',
    required: 'Gerekli',
    infoAndSetting: 'Bilgi ve Ayarlar',
    file: 'dosya',
  },
  noCustomTool: {
    title: 'Özel araç yok!',
    content: 'AI uygulamaları oluşturmak için özel araçlarınızı buraya ekleyin ve yönetin.',
    createTool: 'Araç Oluştur',
  },
  noSearchRes: {
    title: 'Üzgünüz, sonuç bulunamadı!',
    content: 'Aramanızla eşleşen araçlar bulamadık.',
    reset: 'Aramayı Sıfırla',
  },
  builtInPromptTitle: 'Prompt',
  toolRemoved: 'Araç kaldırıldı',
  notAuthorized: 'Araç yetkilendirilmedi',
  howToGet: 'Nasıl alınır',
  openInStudio: 'Studyoda Aç',
  toolNameUsageTip: 'Agent akıl yürütme ve prompt için araç çağrı adı',
  copyToolName: 'Adı Kopyala',
  noTools: 'Araç bulunamadı',
  mcp: {
    create: {
      cardTitle: 'MCP Sunucusu Ekle (HTTP)',
      cardLink: 'MCP sunucu entegrasyonu hakkında daha fazla bilgi edinin',
    },
    noConfigured: 'Yapılandırılmamış Sunucu',
    updateTime: 'Güncellendi',
    toolsCount: '{count} araç',
    noTools: 'Kullanılabilir araç yok',
    modal: {
      title: 'MCP Sunucusu Ekle (HTTP)',
      editTitle: 'MCP Sunucusunu Düzenle (HTTP)',
      name: 'Ad ve Simge',
      namePlaceholder: 'MCP sunucunuza ad verin',
      serverUrl: 'Sunucu URL',
      serverUrlPlaceholder: 'Sunucu endpoint URL',
      serverUrlWarning: 'Sunucu adresini güncellemek, bu sunucuya bağımlı uygulamaları kesintiye uğratabilir',
      serverIdentifier: 'Sunucu Tanımlayıcı',
      serverIdentifierTip: 'Çalışma alanındaki MCP sunucusu için benzersiz tanımlayıcı. Sadece küçük harf, rakam, alt çizgi ve tire. En fazla 24 karakter.',
      serverIdentifierPlaceholder: 'Benzersiz tanımlayıcı, örn. my-mcp-server',
      serverIdentifierWarning: 'ID değiştirildikten sonra sunucu mevcut uygulamalar tarafından tanınmayacak',
      cancel: 'İptal',
      save: 'Kaydet',
      confirm: 'Ekle ve Yetkilendir',
    },
    delete: 'MCP Sunucusunu Kaldır',
    deleteConfirmTitle: '{mcp} kaldırılsın mı?',
    operation: {
      edit: 'Düzenle',
      remove: 'Kaldır',
    },
    authorize: 'Yetkilendir',
    authorizing: 'Yetkilendiriliyor...',
    authorizingRequired: 'Yetkilendirme gerekli',
    authorizeTip: 'Yetkilendirmeden sonra araçlar burada görüntülenecektir.',
    update: 'Güncelle',
    updating: 'Güncelleniyor...',
    gettingTools: 'Araçlar alınıyor...',
    updateTools: 'Araçlar güncelleniyor...',
    toolsEmpty: 'Araçlar yüklenmedi',
    getTools: 'Araçları al',
    toolUpdateConfirmTitle: 'Araç Listesini Güncelle',
    toolUpdateConfirmContent: 'Araç listesini güncellemek mevcut uygulamaları etkileyebilir. Devam etmek istiyor musunuz?',
    toolsNum: '{count} araç dahil',
    onlyTool: '1 araç dahil',
    identifier: 'Sunucu Tanımlayıcı (Kopyalamak için Tıklayın)',
    server: {
      title: 'MCP Sunucusu',
      url: 'Sunucu URL',
      reGen: 'Sunucu URL yeniden oluşturulsun mu?',
      addDescription: 'Açıklama ekle',
      edit: 'Açıklamayı düzenle',
      modal: {
        addTitle: 'MCP Sunucusunu etkinleştirmek için açıklama ekleyin',
        editTitle: 'Açıklamayı düzenle',
        description: 'Açıklama',
        descriptionPlaceholder: 'Bu aracın ne yaptığını ve LLM tarafından nasıl kullanılması gerektiğini açıklayın',
        parameters: 'Parametreler',
        parametersTip: 'LLM\'nin amaçlarını ve kısıtlamalarını anlamasına yardımcı olmak için her parametreye açıklamalar ekleyin.',
        parametersPlaceholder: 'Parametre amacı ve kısıtlamaları',
        confirm: 'MCP Sunucusunu Etkinleştir',
      },
      publishTip: 'Uygulama yayınlanmadı. Lütfen önce uygulamayı yayınlayın.',
    },
  },
}

export default translation
