const translation = {
  createApp: 'CRIAR APLICATIVO',
  types: {
    all: 'Todos',
    chatbot: 'Chatbot',
    agent: 'Agent<PERSON>',
    workflow: 'Fluxo de trabalho',
    completion: 'Conclusão',
    basic: 'Básico',
    advanced: 'Fluxo de bate-papo',
  },
  duplicate: 'Duplicar',
  duplicateTitle: 'Duplicar Aplicativo',
  export: 'Exportar DSL',
  exportFailed: 'Falha ao exportar DSL.',
  importDSL: 'Importar arquivo DSL',
  createFromConfigFile: 'Criar a partir do arquivo DSL',
  deleteAppConfirmTitle: 'Excluir este aplicativo?',
  deleteAppConfirmContent:
    'A exclusão do aplicativo é irreversível. Os usuários não poderão mais acessar seu aplicativo e todas as configurações de prompt e logs serão permanentemente excluídas.',
  appDeleted: 'Aplicativo excluído',
  appDeleteFailed: 'Falha ao excluir aplicativo',
  join: 'Participe da comunidade',
  communityIntro:
    'Discuta com membros da equipe, colaboradores e desenvolvedores em diferentes canais.',
  roadmap: 'Veja nosso roteiro',
  newApp: {
    startFromBlank: 'Criar do zero',
    startFromTemplate: 'Criar do modelo',
    captionAppType: 'Que tipo de aplicativo você deseja criar?',
    chatbotDescription: 'Construa um aplicativo baseado em chat. Este aplicativo usa um formato de pergunta e resposta, permitindo várias rodadas de conversa contínua.',
    completionDescription: 'Construa um aplicativo que gera texto de alta qualidade com base em prompts, como geração de artigos, resumos, traduções e muito mais.',
    completionWarning: 'Este tipo de aplicativo não será mais suportado.',
    agentDescription: 'Construa um Agente inteligente que pode escolher ferramentas para completar as tarefas autonomamente',
    workflowDescription: 'Construa um aplicativo que gera texto de alta qualidade com base em fluxo de trabalho com alto grau de personalização. É adequado para usuários experientes.',
    workflowWarning: 'Atualmente em beta',
    chatbotType: 'Método de orquestração do Chatbot',
    basic: 'Básico',
    basicTip: 'Para iniciantes, pode mudar para o Chatflow mais tarde',
    basicFor: 'PARA INICIANTES',
    basicDescription: 'A Orquestração Básica permite orquestrar um aplicativo Chatbot usando configurações simples, sem a capacidade de modificar prompts integrados. É adequado para iniciantes.',
    advanced: 'Chatflow',
    advancedFor: 'Para usuários avançados',
    advancedDescription: 'A Orquestração de Fluxo de Trabalho orquestra Chatbots na forma de fluxos de trabalho, oferecendo um alto grau de personalização, incluindo a capacidade de editar prompts integrados. É adequado para usuários experientes.',
    captionName: 'Ícone e nome do aplicativo',
    appNamePlaceholder: 'Dê um nome para o seu aplicativo',
    captionDescription: 'Descrição',
    appDescriptionPlaceholder: 'Digite a descrição do aplicativo',
    useTemplate: 'Usar este modelo',
    previewDemo: 'Visualizar demonstração',
    chatApp: 'Assistente',
    chatAppIntro:
      'Eu quero construir um aplicativo baseado em chat. Este aplicativo usa um formato de pergunta e resposta, permitindo várias rodadas de conversa contínua.',
    agentAssistant: 'Novo Assistente de Agente',
    completeApp: 'Gerador de Texto',
    completeAppIntro:
      'Eu quero criar um aplicativo que gera texto de alta qualidade com base em prompts, como geração de artigos, resumos, traduções e muito mais.',
    showTemplates: 'Quero escolher a partir de um modelo',
    hideTemplates: 'Voltar para a seleção de modo',
    Create: 'Criar',
    Cancel: 'Cancelar',
    nameNotEmpty: 'O nome não pode estar vazio',
    appTemplateNotSelected: 'Por favor, selecione um modelo',
    appTypeRequired: 'Por favor, selecione um tipo de aplicativo',
    appCreated: 'Aplicativo criado',
    appCreateFailed: 'Falha ao criar aplicativo',
    caution: 'Cuidado',
    appCreateDSLErrorPart1: 'Uma diferença significativa nas versões DSL foi detectada. Forçar a importação pode causar mau funcionamento do aplicativo.',
    appCreateDSLErrorPart4: 'Versão DSL suportada pelo sistema:',
    Confirm: 'Confirmar',
    appCreateDSLErrorTitle: 'Incompatibilidade de versão',
    appCreateDSLWarning: 'Cuidado: a diferença de versão DSL pode afetar determinados recursos',
    appCreateDSLErrorPart3: 'Versão DSL do aplicativo atual:',
    appCreateDSLErrorPart2: 'Você quer continuar?',
    learnMore: 'Saiba Mais',
    optional: 'Opcional',
    chooseAppType: 'Escolha um tipo de aplicativo',
    forBeginners: 'Tipos de aplicativos mais básicos',
    noTemplateFound: 'Nenhum modelo encontrado',
    foundResults: '{{contagem}} Resultados',
    foundResult: '{{contagem}} Resultado',
    completionUserDescription: 'Crie rapidamente um assistente de IA para tarefas de geração de texto com configuração simples.',
    noIdeaTip: 'Sem ideias? Confira nossos modelos',
    workflowUserDescription: 'Construa fluxos autônomos de IA visualmente com simplicidade de arrastar e soltar.',
    chatbotUserDescription: 'Crie rapidamente um chatbot baseado em LLM com configuração simples. Você pode alternar para o fluxo de chat mais tarde.',
    agentShortDescription: 'Agente inteligente com raciocínio e uso de ferramenta autônoma',
    forAdvanced: 'PARA USUÁRIOS AVANÇADOS',
    chatbotShortDescription: 'Chatbot baseado em LLM com configuração simples',
    advancedUserDescription: 'Fluxo com recursos adicionais de memória e interface de chatbot.',
    noTemplateFoundTip: 'Tente pesquisar usando palavras-chave diferentes.',
    agentUserDescription: 'Um agente inteligente capaz de raciocínio iterativo e uso autônomo de ferramentas para atingir os objetivos da tarefa.',
    completionShortDescription: 'Assistente de IA para tarefas de geração de texto',
    workflowShortDescription: 'Fluxo agêntico para automações inteligentes',
    noAppsFound: 'Nenhum aplicativo encontrado',
    advancedShortDescription: 'Fluxo aprimorado para conversas de múltiplos turnos',
    dropDSLToCreateApp: 'Cole o arquivo DSL aqui para criar o aplicativo',
  },
  editApp: 'Editar Informações',
  editAppTitle: 'Editar Informações do Aplicativo',
  editDone: 'Informações do aplicativo atualizadas',
  editFailed: 'Falha ao atualizar informações do aplicativo',
  iconPicker: {
    ok: 'OK',
    cancel: 'Cancelar',
    emoji: 'Emoji',
    image: 'Imagem',
  },
  switch: 'Mudar para Orquestração de Fluxo de Trabalho',
  switchTipStart: 'Será criada uma nova cópia do aplicativo para você e a nova cópia mudará para Orquestração de Fluxo de Trabalho. A nova cópia não permitirá a ',
  switchTip: 'volta',
  switchTipEnd: ' para Orquestração Básica.',
  switchLabel: 'A cópia do aplicativo a ser criada',
  removeOriginal: 'Excluir o aplicativo original',
  switchStart: 'Iniciar mudança',
  typeSelector: {
    all: 'Todos os Tipos',
    chatbot: 'Chatbot',
    agent: 'Agente',
    workflow: 'Fluxo de trabalho',
    completion: 'Conclusão',
    advanced: 'Fluxo de bate-papo',
  },
  tracing: {
    title: 'Rastreamento de desempenho do aplicativo',
    description: 'Configurando um provedor LLMOps de terceiros e rastreando o desempenho do aplicativo.',
    config: 'Configurar',
    collapse: 'Recolher',
    expand: 'Expandir',
    tracing: 'Rastreamento',
    disabled: 'Desativado',
    disabledTip: 'Por favor, configure o provedor primeiro',
    enabled: 'Em serviço',
    tracingDescription: 'Captura o contexto completo da execução do aplicativo, incluindo chamadas LLM, contexto, prompts, solicitações HTTP e mais, para uma plataforma de rastreamento de terceiros.',
    configProviderTitle: {
      configured: 'Configurado',
      notConfigured: 'Configure o provedor para habilitar o rastreamento',
      moreProvider: 'Mais provedores',
    },
    arize: {
      title: 'Arize',
      description: 'Observabilidade de LLM de nível empresarial, avaliação online e offline, monitoramento e experimentação—impulsionada pelo OpenTelemetry. Projetado especificamente para aplicações baseadas em LLM e agentes.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Plataforma de observabilidade, avaliação, engenharia de prompts e experimentação de código aberto baseada em OpenTelemetry para seus fluxos de trabalho e agentes de LLM.',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'Uma plataforma de desenvolvedor completa para cada etapa do ciclo de vida do aplicativo impulsionado por LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Rastreamentos, avaliações, gerenciamento de prompts e métricas para depurar e melhorar seu aplicativo LLM.',
    },
    inUse: 'Em uso',
    configProvider: {
      title: 'Configurar ',
      placeholder: 'Insira sua {{key}}',
      project: 'Projeto',
      publicKey: 'Chave Pública',
      secretKey: 'Chave Secreta',
      viewDocsLink: 'Ver documentação de {{key}}',
      removeConfirmTitle: 'Remover configuração de {{key}}?',
      removeConfirmContent: 'A configuração atual está em uso, removê-la desligará o recurso de Rastreamento.',
    },
    view: 'Vista',
    opik: {
      description: 'Opik é uma plataforma de código aberto para avaliar, testar e monitorar aplicativos LLM.',
      title: 'Opik',
    },
    weave: {
      description: 'Weave é uma plataforma de código aberto para avaliar, testar e monitorar aplicações de LLM.',
      title: 'Trançar',
    },
    aliyun: {
      title: 'Monitoramento em Nuvem',
      description: 'A plataforma de observabilidade totalmente gerenciada e sem manutenção fornecida pela Alibaba Cloud, permite monitoramento, rastreamento e avaliação prontos para uso de aplicações Dify.',
    },
  },
  answerIcon: {
    descriptionInExplore: 'Se o ícone do web app deve ser usado para substituir 🤖 no Explore',
    description: 'Se o ícone web app deve ser usado para substituir 🤖 no aplicativo compartilhado',
    title: 'Use o ícone do web app para substituir 🤖',
  },
  importFromDSLUrlPlaceholder: 'Cole o link DSL aqui',
  importFromDSLUrl: 'Do URL',
  importFromDSLFile: 'Do arquivo DSL',
  importFromDSL: 'Importar de DSL',
  mermaid: {
    handDrawn: 'Mão desenhada',
    classic: 'Clássico',
  },
  openInExplore: 'Abrir no Explore',
  newAppFromTemplate: {
    sidebar: {
      Programming: 'Programação',
      Agent: 'Agente',
      HR: 'RH',
      Workflow: 'Fluxo de trabalho',
      Writing: 'Escrita',
      Recommended: 'Recomendado',
      Assistant: 'Assistente',
    },
    searchAllTemplate: 'Pesquisar todos os modelos...',
    byCategories: 'POR CATEGORIAS',
  },
  showMyCreatedAppsOnly: 'Mostrar apenas meus aplicativos criados',
  appSelector: {
    label: 'APLICAÇÃO',
    noParams: 'Não são necessários parâmetros',
    placeholder: 'Selecione um aplicativo...',
    params: 'PARÂMETROS DO APLICATIVO',
  },
  structOutput: {
    LLMResponse: 'Resposta do LLM',
    configure: 'Configurar',
    required: 'Requerido',
    modelNotSupported: 'Modelo não suportado',
    structured: 'Estruturado',
    modelNotSupportedTip: 'O modelo atual não suporta esse recurso e é automaticamente rebaixado para injeção de prompt.',
    structuredTip: 'Saídas Estruturadas é um recurso que garante que o modelo sempre gerará respostas que seguem o seu Esquema JSON fornecido.',
    moreFillTip: 'Mostrando um máximo de 10 níveis de aninhamento',
    notConfiguredTip: 'A saída estruturada ainda não foi configurada.',
  },
  accessItemsDescription: {
    anyone: 'Qualquer pessoa pode acessar o aplicativo web',
    specific: 'Apenas grupos ou membros específicos podem acessar o aplicativo web',
    organization: 'Qualquer pessoa na organização pode acessar o aplicativo web',
    external: 'Apenas usuários externos autenticados podem acessar o aplicativo Web.',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Qualquer pessoa com o link',
      specific: 'Grupos específicos ou membros',
      organization: 'Apenas membros dentro da empresa',
      external: 'Usuários externos autenticados',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Pesquisar grupos e membros',
      allMembers: 'Todos os membros',
      expand: 'Expandir',
      noResult: 'Nenhum resultado',
    },
    title: 'Controle de Acesso do Aplicativo Web',
    description: 'Defina as permissões de acesso do aplicativo da web',
    accessLabel: 'Quem tem acesso',
    groups_one: '{{count}} GRUPO',
    groups_other: '{{count}} GRUPOS',
    members_other: '{{count}} MEMBROS',
    noGroupsOrMembers: 'Nenhum grupo ou membro selecionado',
    updateSuccess: 'Atualização bem-sucedida',
    members_one: '{{count}} MEMBRO',
    webAppSSONotEnabledTip: 'Por favor, entre em contato com o administrador da empresa para configurar o método de autenticação da aplicação web.',
  },
  publishApp: {
    title: 'Quem pode acessar o aplicativo web',
    notSet: 'Não definido',
    notSetDesc: 'Atualmente, ninguém pode acessar o aplicativo web. Por favor, defina as permissões.',
  },
  accessControl: 'Controle de Acesso do Aplicativo Web',
  noAccessPermission: 'Sem permissão para acessar o aplicativo web',
  maxActiveRequestsPlaceholder: 'Digite 0 para ilimitado',
  maxActiveRequests: 'Máximo de solicitações simultâneas',
}

export default translation
