const translation = {
  title: 'Ferramentas',
  createCustomTool: 'Criar Ferramenta Personalizada',
  type: {
    all: 'Todas',
    builtIn: 'Integradas',
    custom: 'Personalizadas',
    workflow: 'Fluxo de trabalho',
  },
  contribute: {
    line1: 'Estou interessado em ',
    line2: 'contribuir com ferramentas para o Dify.',
    viewGuide: 'Ver o guia',
  },
  author: 'Por',
  auth: {
    authorized: 'Autorizado',
    setup: 'Configurar autorização para usar',
    setupModalTitle: 'Configurar Autorização',
    setupModalTitleDescription: 'Após configurar as credenciais, todos os membros do espaço de trabalho podem usar essa ferramenta ao orquestrar aplicativos.',
  },
  includeToolNum: '{{num}} ferramentas incluídas',
  addTool: 'Adicionar Ferramenta',
  createTool: {
    title: 'Criar Ferramenta Personalizada',
    editAction: 'Configurar',
    editTitle: 'Editar Ferramenta Personalizada',
    name: 'No<PERSON>',
    toolNamePlaceHolder: 'Digite o nome da ferramenta',
    schema: 'Esquema',
    schemaPlaceHolder: 'Digite seu esquema OpenAPI aqui',
    viewSchemaSpec: 'Ver a Especificação OpenAPI-Swagger',
    importFromUrl: 'Importar de URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Digite uma URL válida',
    examples: 'Exemplos',
    exampleOptions: {
      json: 'Clima(JSON)',
      yaml: 'Pet Store(YAML)',
      blankTemplate: 'Modelo em Branco',
    },
    availableTools: {
      title: 'Ferramentas Disponíveis',
      name: 'Nome',
      description: 'Descrição',
      method: 'Método',
      path: 'Caminho',
      action: 'Ações',
      test: 'Testar',
    },
    authMethod: {
      title: 'Método de Autorização',
      type: 'Tipo de Autorização',
      keyTooltip: 'Chave do Cabeçalho HTTP, você pode deixar como "Authorization" se não tiver ideia do que é ou definir um valor personalizado',
      types: {
        none: 'Nenhum',
        api_key: 'Chave de API',
        apiKeyPlaceholder: 'Nome do cabeçalho HTTP para a Chave de API',
        apiValuePlaceholder: 'Digite a Chave de API',
        api_key_query: 'Parâmetro de consulta',
        queryParamPlaceholder: 'Nome do parâmetro de consulta para a chave da API',
        api_key_header: 'Cabeçalho',
      },
      key: 'Chave',
      value: 'Valor',
      queryParam: 'Parâmetro de consulta',
      queryParamTooltip: 'O nome do parâmetro de consulta da chave da API a ser passado, por exemplo, "key" em "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      title: 'Tipo de Autenticação',
      types: {
        basic: 'Básica',
        bearer: 'Bearer',
        custom: 'Personalizada',
      },
    },
    privacyPolicy: 'Política de Privacidade',
    privacyPolicyPlaceholder: 'Digite a política de privacidade',
    customDisclaimer: 'Aviso Personalizado',
    customDisclaimerPlaceholder: 'Digite o aviso personalizado',
    deleteToolConfirmTitle: 'Excluir esta ferramenta?',
    deleteToolConfirmContent: 'Excluir a ferramenta é irreversível. Os usuários não poderão mais acessar sua ferramenta.',
    toolInput: {
      label: 'Tags',
      methodSetting: 'Ambiente',
      methodParameterTip: 'Preenchimentos de LLM durante a inferência',
      methodSettingTip: 'O usuário preenche a configuração da ferramenta',
      methodParameter: 'Parâmetro',
      name: 'Nome',
      description: 'Descrição',
      method: 'Método',
      required: 'Necessário',
      title: 'Entrada de ferramenta',
      labelPlaceholder: 'Escolha tags(opcional)',
      descriptionPlaceholder: 'Descrição do significado do parâmetro',
    },
    description: 'Descrição',
    nameForToolCall: 'Nome da chamada da ferramenta',
    confirmTip: 'Os aplicativos que usam essa ferramenta serão afetados',
    confirmTitle: 'Confirme para salvar ?',
    nameForToolCallTip: 'Suporta apenas números, letras e sublinhados.',
    descriptionPlaceholder: 'Breve descrição da finalidade da ferramenta, por exemplo, obter a temperatura para um local específico.',
    nameForToolCallPlaceHolder: 'Usado para reconhecimento de máquina, como getCurrentWeather, list_pets',
  },
  test: {
    title: 'Testar',
    parametersValue: 'Parâmetros e Valor',
    parameters: 'Parâmetros',
    value: 'Valor',
    testResult: 'Resultados do Teste',
    testResultPlaceholder: 'O resultado do teste será exibido aqui',
  },
  thought: {
    using: 'Usando',
    used: 'Usado',
    requestTitle: 'Requisição para',
    responseTitle: 'Resposta de',
  },
  setBuiltInTools: {
    info: 'Informações',
    setting: 'Configuração',
    toolDescription: 'Descrição da Ferramenta',
    parameters: 'parâmetros',
    string: 'string',
    number: 'número',
    required: 'Obrigatório',
    infoAndSetting: 'Informações e Configurações',
    file: 'arquivo',
  },
  noCustomTool: {
    title: 'Nenhuma ferramenta personalizada!',
    content: 'Adicione e gerencie suas ferramentas personalizadas aqui para construir aplicativos de IA.',
    createTool: 'Criar Ferramenta',
  },
  noSearchRes: {
    title: 'Desculpe, sem resultados!',
    content: 'Não encontramos nenhuma ferramenta que corresponda à sua pesquisa.',
    reset: 'Redefinir Pesquisa',
  },
  builtInPromptTitle: 'Prompt',
  toolRemoved: 'Ferramenta removida',
  notAuthorized: 'Ferramenta não autorizada',
  howToGet: 'Como obter',
  addToolModal: {
    category: 'categoria',
    type: 'tipo',
    add: 'adicionar',
    added: 'Adicionado',
    manageInTools: 'Gerenciar em Ferramentas',
    custom: {
      title: 'Nenhuma ferramenta personalizada disponível',
      tip: 'Crie uma ferramenta personalizada',
    },
    workflow: {
      title: 'Nenhuma ferramenta de fluxo de trabalho disponível',
      tip: 'Publique fluxos de trabalho como ferramentas no Studio',
    },
    mcp: {
      title: 'Nenhuma ferramenta MCP disponível',
      tip: 'Adicionar um servidor MCP',
    },
    agent: {
      title: 'Nenhuma estratégia de agente disponível',
    },
  },
  openInStudio: 'Abrir no Studio',
  customToolTip: 'Saiba mais sobre as ferramentas personalizadas da Dify',
  toolNameUsageTip: 'Nome da chamada da ferramenta para raciocínio e solicitação do agente',
  copyToolName: 'Nome da cópia',
  noTools: 'Nenhuma ferramenta encontrada',
  mcp: {
    create: {
      cardTitle: 'Adicionar Servidor MCP (HTTP)',
      cardLink: 'Saiba mais sobre a integração do servidor MCP',
    },
    noConfigured: 'Servidor Não Configurado',
    updateTime: 'Atualizado',
    toolsCount: '{{count}} ferramentas',
    noTools: 'Nenhuma ferramenta disponível',
    modal: {
      title: 'Adicionar Servidor MCP (HTTP)',
      editTitle: 'Editar Servidor MCP (HTTP)',
      name: 'Nome & Ícone',
      namePlaceholder: 'Dê um nome ao seu servidor MCP',
      serverUrl: 'URL do Servidor',
      serverUrlPlaceholder: 'URL para o endpoint do servidor',
      serverUrlWarning: 'Atualizar o endereço do servidor pode interromper aplicações que dependem deste servidor',
      serverIdentifier: 'Identificador do Servidor',
      serverIdentifierTip: 'Identificador único para o servidor MCP dentro do espaço de trabalho. Apenas letras minúsculas, números, sublinhados e hífens. Até 24 caracteres.',
      serverIdentifierPlaceholder: 'Identificador único, ex: meu-servidor-mcp',
      serverIdentifierWarning: 'O servidor não será reconhecido por aplicativos existentes após uma mudança de ID',
      cancel: 'Cancelar',
      save: 'Salvar',
      confirm: 'Adicionar e Autorizar',
    },
    delete: 'Remover Servidor MCP',
    deleteConfirmTitle: 'Você gostaria de remover {{mcp}}?',
    operation: {
      edit: 'Editar',
      remove: 'Remover',
    },
    authorize: 'Autorizar',
    authorizing: 'Autorizando...',
    authorizingRequired: 'Autorização é necessária',
    authorizeTip: 'Após a autorização, as ferramentas serão exibidas aqui.',
    update: 'Atualizar',
    updating: 'Atualizando',
    gettingTools: 'Obtendo Ferramentas...',
    updateTools: 'Atualizando Ferramentas...',
    toolsEmpty: 'Ferramentas não carregadas',
    getTools: 'Obter ferramentas',
    toolUpdateConfirmTitle: 'Atualizar Lista de Ferramentas',
    toolUpdateConfirmContent: 'Atualizar a lista de ferramentas pode afetar aplicativos existentes. Você deseja continuar?',
    toolsNum: '{{count}} ferramentas incluídas',
    onlyTool: '1 ferramenta incluída',
    identifier: 'Identificador do Servidor (Clique para Copiar)',
    server: {
      title: 'Servidor MCP',
      url: 'URL do Servidor',
      reGen: 'Você deseja regenerar a URL do servidor?',
      addDescription: 'Adicionar descrição',
      edit: 'Editar descrição',
      modal: {
        addTitle: 'Adicionar descrição para habilitar o servidor MCP',
        editTitle: 'Editar descrição',
        description: 'Descrição',
        descriptionPlaceholder: 'Explique o que esta ferramenta faz e como deve ser utilizada pelo LLM',
        parameters: 'Parâmetros',
        parametersTip: 'Adicione descrições para cada parâmetro para ajudar o LLM a entender seus propósitos e restrições.',
        parametersPlaceholder: 'Propósito e restrições do parâmetro',
        confirm: 'Habilitar Servidor MCP',
      },
      publishTip: 'Aplicativo não publicado. Por favor, publique o aplicativo primeiro.',
    },
  },
}

export default translation
