const translation = {
  category: {
    extensions: 'Extensões',
    all: 'Todo',
    bundles: 'Pacotes',
    models: 'Modelos',
    agents: 'Estratégias do agente',
    tools: 'Ferramentas',
  },
  categorySingle: {
    model: 'Modelo',
    bundle: 'Pacote',
    agent: 'Estratégia do agente',
    extension: 'Extensão',
    tool: 'Ferramenta',
  },
  list: {
    source: {
      marketplace: 'Instalar do Marketplace',
      github: 'Instalar do GitHub',
      local: 'Instalar a partir do arquivo de pacote local',
    },
    noInstalled: 'Nenhum plug-in instalado',
    notFound: 'Nenhum plugin encontrado',
  },
  source: {
    local: 'Arquivo de pacote local',
    github: 'GitHub',
    marketplace: 'Mercado',
  },
  detailPanel: {
    categoryTip: {
      debugging: 'Plugin de depuração',
      marketplace: 'Instalado do Marketplace',
      local: 'Plug-in local',
      github: 'Instalado a partir do Github',
    },
    operation: {
      checkUpdate: 'Verifique a atualização',
      install: 'Instalar',
      update: 'Atualização',
      info: 'Informações do plugin',
      detail: 'Detalhes',
      remove: 'Retirar',
      viewDetail: 'Ver detalhes',
    },
    toolSelector: {
      uninstalledLink: 'Gerenciar em plug-ins',
      unsupportedContent2: 'Clique para mudar de versão.',
      auto: 'Auto',
      title: 'Adicionar ferramenta',
      params: 'CONFIGURAÇÃO DE RACIOCÍNIO',
      toolLabel: 'Ferramenta',
      paramsTip1: 'Controla os parâmetros de inferência do LLM.',
      descriptionLabel: 'Descrição da ferramenta',
      uninstalledContent: 'Este plug-in é instalado a partir do repositório local/GitHub. Por favor, use após a instalação.',
      paramsTip2: 'Quando \'Auto\' está desativado, o valor padrão é usado.',
      placeholder: 'Selecione uma ferramenta...',
      empty: 'Clique no botão \'+\' para adicionar ferramentas. Você pode adicionar várias ferramentas.',
      settings: 'CONFIGURAÇÕES DO USUÁRIO',
      unsupportedContent: 'A versão do plug-in instalada não fornece essa ação.',
      descriptionPlaceholder: 'Breve descrição da finalidade da ferramenta, por exemplo, obter a temperatura para um local específico.',
      uninstalledTitle: 'Ferramenta não instalada',
      unsupportedTitle: 'Ação sem suporte',
      toolSetting: 'Configurações da Ferramenta',
      unsupportedMCPTool: 'A versão atual do plugin de estratégia do agente selecionado não suporta ferramentas MCP.',
    },
    serviceOk: 'Serviço OK',
    endpointsTip: 'Este plug-in fornece funcionalidades específicas por meio de endpoints e você pode configurar vários conjuntos de endpoints para o workspace atual.',
    strategyNum: '{{num}} {{estratégia}} INCLUSO',
    endpointDisableContent: 'Gostaria de desativar {{name}}?',
    endpointDeleteContent: 'Gostaria de remover {{name}}?',
    endpointsEmpty: 'Clique no botão \'+\' para adicionar um endpoint',
    configureModel: 'Configurar modelo',
    endpointModalDesc: 'Uma vez configurados, os recursos fornecidos pelo plug-in por meio de endpoints de API podem ser usados.',
    endpointDeleteTip: 'Remover endpoint',
    endpointDisableTip: 'Desativar ponto de extremidade',
    modelNum: '{{num}} MODELOS INCLUÍDOS',
    actionNum: '{{num}} {{ação}} INCLUSO',
    switchVersion: 'Versão do Switch',
    endpoints: 'Extremidade',
    disabled: 'Desactivado',
    configureApp: 'Configurar aplicativo',
    configureTool: 'Ferramenta de configuração',
    endpointsDocLink: 'Veja o documento',
    endpointModalTitle: 'Ponto de extremidade de configuração',
    deprecation: {
      reason: {
        businessAdjustments: 'ajustes de negócios',
        ownershipTransferred: 'propriedade transferida',
        noMaintainer: 'sem mantenedor',
      },
      onlyReason: 'Este plugin foi descontinuado devido a {{deprecatedReason}} e não será mais atualizado.',
      noReason: 'Este plugin foi descontinuado e não será mais atualizado.',
      fullMessage: 'Este plugin foi descontinuado devido a {{deprecatedReason}}, e não receberá mais atualizações. Por favor, use <CustomLink href=\'https://example.com/\'>{{-alternativePluginId}}</CustomLink> em vez disso.',
    },
  },
  debugInfo: {
    title: 'Depuração',
    viewDocs: 'Ver documentos',
  },
  privilege: {
    whoCanInstall: 'Quem pode instalar e gerenciar plugins?',
    admins: 'Administradores',
    noone: 'Ninguém',
    whoCanDebug: 'Quem pode depurar plugins?',
    title: 'Preferências de plug-ins',
    everyone: 'Todos',
  },
  pluginInfoModal: {
    repository: 'Repositório',
    title: 'Informações do plugin',
    packageName: 'Pacote',
    release: 'Soltar',
  },
  action: {
    deleteContentLeft: 'Gostaria de remover',
    deleteContentRight: 'plugin?',
    delete: 'Remover plugin',
    pluginInfo: 'Informações do plugin',
    checkForUpdates: 'Verifique se há atualizações',
    usedInApps: 'Este plugin está sendo usado em aplicativos {{num}}.',
  },
  installModal: {
    labels: {
      version: 'Versão',
      repository: 'Repositório',
      package: 'Pacote',
    },
    installPlugin: 'Instale o plugin',
    close: 'Fechar',
    installedSuccessfullyDesc: 'O plugin foi instalado com sucesso.',
    next: 'Próximo',
    installFailedDesc: 'O plug-in foi instalado falhou.',
    installedSuccessfully: 'Instalação bem-sucedida',
    install: 'Instalar',
    installFailed: 'Falha na instalação',
    readyToInstallPackages: 'Prestes a instalar os seguintes plugins {{num}}',
    back: 'Voltar',
    installComplete: 'Instalação concluída',
    readyToInstallPackage: 'Prestes a instalar o seguinte plugin',
    cancel: 'Cancelar',
    fromTrustSource: 'Certifique-se de instalar apenas plug-ins de uma <trustSource>fonte confiável</trustSource>.',
    pluginLoadError: 'Erro de carregamento do plug-in',
    readyToInstall: 'Prestes a instalar o seguinte plugin',
    pluginLoadErrorDesc: 'Este plugin não será instalado',
    uploadFailed: 'Falha no upload',
    installing: 'Instalar...',
    uploadingPackage: 'Carregando {{packageName}} ...',
    dropPluginToInstall: 'Solte o pacote de plug-in aqui para instalar',
    installWarning: 'Este plugin não é permitido ser instalado.',
  },
  installFromGitHub: {
    selectVersionPlaceholder: 'Selecione uma versão',
    updatePlugin: 'Atualizar plugin do GitHub',
    installPlugin: 'Instale o plugin do GitHub',
    gitHubRepo: 'Repositório GitHub',
    installFailed: 'Falha na instalação',
    selectVersion: 'Selecione a versão',
    uploadFailed: 'Falha no upload',
    installedSuccessfully: 'Instalação bem-sucedida',
    installNote: 'Certifique-se de instalar apenas plug-ins de uma fonte confiável.',
    selectPackagePlaceholder: 'Selecione um pacote',
    selectPackage: 'Selecione o pacote',
  },
  upgrade: {
    title: 'Instale o plugin',
    successfulTitle: 'Instalação bem-sucedida',
    close: 'Fechar',
    upgrading: 'Instalar...',
    upgrade: 'Instalar',
    description: 'Prestes a instalar o seguinte plugin',
    usedInApps: 'Usado em aplicativos {{num}}',
  },
  error: {
    inValidGitHubUrl: 'URL do GitHub inválida. Insira um URL válido no formato: https://github.com/owner/repo',
    noReleasesFound: 'Nenhuma versão encontrada. Verifique o repositório GitHub ou a URL de entrada.',
    fetchReleasesError: 'Não é possível recuperar versões. Por favor, tente novamente mais tarde.',
  },
  marketplace: {
    sortOption: {
      mostPopular: 'Mais popular',
      firstReleased: 'Lançado pela primeira vez',
      recentlyUpdated: 'Atualizado recentemente',
      newlyReleased: 'Recém-lançado',
    },
    sortBy: 'Ordenar por',
    viewMore: 'Ver mais',
    and: 'e',
    pluginsResult: '{{num}} resultados',
    empower: 'Capacite seu desenvolvimento de IA',
    difyMarketplace: 'Mercado Dify',
    moreFrom: 'Mais do Marketplace',
    noPluginFound: 'Nenhum plugin encontrado',
    discover: 'Descobrir',
    verifiedTip: 'Verificado pelo Dify',
    partnerTip: 'Verificado por um parceiro da Dify',
  },
  task: {
    installedError: 'Falha na instalação dos plug-ins {{errorLength}}',
    installingWithSuccess: 'Instalando plugins {{installingLength}}, {{successLength}} sucesso.',
    installError: '{{errorLength}} plugins falha ao instalar, clique para ver',
    installingWithError: 'Instalando plug-ins {{installingLength}}, {{successLength}} sucesso, {{errorLength}} falhou',
    installing: 'Instalando plugins {{installingLength}}, 0 feito.',
    clearAll: 'Apagar tudo',
  },
  installAction: 'Instalar',
  endpointsEnabled: '{{num}} conjuntos de endpoints habilitados',
  searchPlugins: 'Pesquisar plugins',
  searchInMarketplace: 'Pesquisar no Marketplace',
  installPlugin: 'Instale o plugin',
  from: 'De',
  searchTools: 'Ferramentas de pesquisa...',
  search: 'Procurar',
  fromMarketplace: 'Do Marketplace',
  allCategories: 'Todas as categorias',
  install: '{{num}} instala',
  searchCategories: 'Categorias de pesquisa',
  findMoreInMarketplace: 'Saiba mais no Marketplace',
  installFrom: 'INSTALAR DE',
  metadata: {
    title: 'Plugins',
  },
  difyVersionNotCompatible: 'A versão atual do Dify não é compatível com este plugin, por favor atualize para a versão mínima exigida: {{minimalDifyVersion}}',
  requestAPlugin: 'Solicitar um plugin',
  publishPlugins: 'Publicar plugins',
  auth: {
    oauthClient: 'Cliente OAuth',
    useOAuthAuth: 'Use a autorização OAuth',
    useOAuth: 'Use OAuth',
    setDefault: 'Definir como padrão',
    saveOnly: 'Salvar apenas',
    authRemoved: 'Autorização removida',
    workspaceDefault: 'Espaço de trabalho padrão',
    setupOAuth: 'Configurar Cliente OAuth',
    addApi: 'Adicionar chave da API',
    authorizationName: 'Nome da Autorização',
    authorization: 'Autorização',
    custom: 'Custom',
    authorizations: 'Autorizações',
    default: 'Padrão',
    saveAndAuth: 'Salvar e Autorizar',
    useApi: 'Use a chave da API',
    oauthClientSettings: 'Configurações do Cliente OAuth',
    useApiAuth: 'Configuração de Autorização de Chave da API',
    addOAuth: 'Adicionar OAuth',
    useApiAuthDesc: 'Após configurar as credenciais, todos os membros dentro do espaço de trabalho podem usar esta ferramenta ao orquestrar aplicações.',
    clientInfo: 'Como não foram encontrados segredos de cliente do sistema para este provedor de ferramentas, é necessário configurá-lo manualmente. Para redirect_uri, use',
  },
  deprecated: 'Obsoleto',
  autoUpdate: {
    strategy: {
      disabled: {
        name: 'Desativado',
        description: 'Os plugins não atualizarão automaticamente',
      },
      fixOnly: {
        selectedDescription: 'Atualização automática apenas para versões de patch',
        name: 'Reparar Apenas',
      },
      latest: {
        description: 'Sempre atualize para a versão mais recente',
        selectedDescription: 'Sempre atualize para a versão mais recente',
        name: 'Último',
      },
    },
    upgradeMode: {
      all: 'Atualizar tudo',
      exclude: 'Excluir selecionados',
      partial: 'Somente selecionado',
    },
    upgradeModePlaceholder: {
      exclude: 'Plugins selecionados não serão atualizados automaticamente',
      partial: 'Apenas plugins selecionados serão atualizados automaticamente. Nenhum plugin está atualmente selecionado, então nenhum plugin será atualizado automaticamente.',
    },
    operation: {
      select: 'Selecionar plugins',
      clearAll: 'Limpar tudo',
    },
    pluginDowngradeWarning: {
      downgrade: 'Descer de nível de qualquer forma',
      exclude: 'Excluir da atualização automática',
      title: 'Rebaixamento do Plugin',
      description: 'A atualização automática está atualmente habilitada para este plugin. Reverter a versão pode causar a sobrescrição de suas alterações durante a próxima atualização automática.',
    },
    noPluginPlaceholder: {
      noFound: 'Nenhum plugin foi encontrado.',
      noInstalled: 'Nenhum plugin instalado',
    },
    updateTime: 'Atualizar hora',
    automaticUpdates: 'Atualizações automáticas',
    excludeUpdate: 'Os seguintes {{num}} plugins não serão atualizados automaticamente',
    updateTimeTitle: 'Atualizar hora',
    specifyPluginsToUpdate: 'Especifique os plugins a serem atualizados',
    changeTimezone: 'Para mudar o fuso horário, vá para <setTimezone>Configurações</setTimezone>',
    nextUpdateTime: 'Próxima atualização automática: {{time}}',
    partialUPdate: 'Apenas os seguintes {{num}} plugins serão atualizados automaticamente',
    updateSettings: 'Atualizar Configurações',
  },
}

export default translation
