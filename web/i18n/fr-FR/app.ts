const translation = {
  createApp: 'CRÉER UNE APPLICATION',
  types: {
    all: 'Tout',
    chatbot: 'Chatbot',
    agent: 'Agent',
    workflow: 'Flux de travail',
    completion: 'Terminaison',
    basic: 'Basique',
    advanced: 'Chatflow',
  },
  duplicate: 'Dupliquer',
  duplicateTitle: 'Dupliquer l\'application',
  export: 'Exporter DSL',
  exportFailed: 'Échec de l\'exportation du DSL.',
  importDSL: 'Importer le fichier DSL',
  createFromConfigFile: 'Créer à partir du fichier DSL',
  deleteAppConfirmTitle: 'Supprimer cette application ?',
  deleteAppConfirmContent:
    'La suppression de l\'application est irréversible. Les utilisateurs ne pourront plus accéder à votre application et toutes les configurations de prompt et les journaux seront définitivement supprimés.',
  appDeleted: 'Application supprimée',
  appDeleteFailed: 'Échec de la suppression de l\'application',
  join: 'Rejoindre la communauté',
  communityIntro:
    'Discutez avec les membres de l\'équipe, les contributeurs et les développeurs sur différents canaux.',
  roadmap: 'Voir notre feuille de route',
  newApp: {
    startFromBlank: 'Créer à partir de zéro',
    startFromTemplate: 'Créer à partir d\'un modèle',
    captionAppType: 'Quel type d\'application souhaitez-vous créer ?',
    chatbotDescription: 'Construisez une application basée sur le chat. Cette application utilise un format question-réponse, permettant ainsi plusieurs tours de conversation continue.',
    completionDescription: 'Construisez une application qui génère du texte de haute qualité en fonction des invites, telles que la génération d\'articles, de résumés, de traductions, et plus encore.',
    completionWarning: 'Ce type d\'application ne sera plus pris en charge.',
    agentDescription: 'Construisez un agent intelligent capable de choisir automatiquement les outils pour accomplir les tâches',
    workflowDescription: 'Construisez une application qui génère du texte de haute qualité en fonction d\'un flux de travail avec un haut degré de personnalisation. Il convient aux utilisateurs expérimentés.',
    workflowWarning: 'Actuellement en version bêta',
    chatbotType: 'Méthode d\'orchestration du chatbot',
    basic: 'Basique',
    basicTip: 'Pour les débutants, peut passer à Chatflow plus tard',
    basicFor: 'POUR LES DÉBUTANTS',
    basicDescription: 'L\'orchestration de base permet d\'orchestrer une application Chatbot à l\'aide de paramètres simples, sans possibilité de modifier les invites intégrées. Il convient aux débutants.',
    advanced: 'Chatflow',
    advancedFor: 'Pour les utilisateurs avancés',
    advancedDescription: 'L\'orchestration de flux de travail orchestre les Chatbots sous forme de workflows, offrant un haut degré de personnalisation, y compris la possibilité de modifier les invites intégrées. Il convient aux utilisateurs expérimentés.',
    captionName: 'Icône et nom de l\'application',
    appNamePlaceholder: 'Donnez un nom à votre application',
    captionDescription: 'Description',
    appDescriptionPlaceholder: 'Entrez la description de l\'application',
    useTemplate: 'Utiliser ce modèle',
    previewDemo: 'Aperçu de la démo',
    chatApp: 'Assistant',
    chatAppIntro:
      'Je veux construire une application basée sur le chat. Cette application utilise un format question-réponse, permettant plusieurs tours de conversation continue.',
    agentAssistant: 'Nouvel assistant agent',
    completeApp: 'Générateur de texte',
    completeAppIntro:
      'Je veux créer une application qui génère du texte de haute qualité en fonction des invites, telles que la génération d\'articles, de résumés, de traductions, et plus encore.',
    showTemplates: 'Je veux choisir parmi un modèle',
    hideTemplates: 'Revenir à la sélection de mode',
    Create: 'Créer',
    Cancel: 'Annuler',
    nameNotEmpty: 'Le nom ne peut pas être vide',
    appTemplateNotSelected: 'Veuillez sélectionner un modèle',
    appTypeRequired: 'Veuillez sélectionner un type d\'application',
    appCreated: 'Application créée',
    appCreateFailed: 'Échec de la création de l\'application',
    Confirm: 'Confirmer',
    caution: 'Prudence',
    appCreateDSLWarning: 'Attention : la différence de version DSL peut affecter certaines fonctionnalités',
    appCreateDSLErrorPart4: 'Version DSL prise en charge par le système :',
    appCreateDSLErrorPart1: 'Une différence significative entre les versions DSL a été détectée. Forcer l’importation peut entraîner un dysfonctionnement de l’application.',
    appCreateDSLErrorTitle: 'Incompatibilité de version',
    appCreateDSLErrorPart3: 'Version actuelle de l’application DSL :',
    appCreateDSLErrorPart2: 'Voulez-vous continuer ?',
    foundResults: '{{compte}} Résultats',
    workflowShortDescription: 'Flux agentique pour automatisations intelligentes',
    agentShortDescription: 'Agent intelligent avec raisonnement et utilisation autonome de l’outil',
    learnMore: 'Pour en savoir plus',
    noTemplateFound: 'Aucun modèle trouvé',
    completionShortDescription: 'Assistant IA pour les tâches de génération de texte',
    chatbotShortDescription: 'Chatbot basé sur LLM avec configuration simple',
    advancedUserDescription: 'Workflow avec fonctionnalités de mémoire et interface de chatbot.',
    noTemplateFoundTip: 'Essayez d’effectuer une recherche à l’aide de mots-clés différents.',
    noAppsFound: 'Aucune application trouvée',
    chooseAppType: 'Choisissez un type d’application',
    forAdvanced: 'POUR LES UTILISATEURS AVANCÉS',
    chatbotUserDescription: 'Créez rapidement un chatbot basé sur LLM avec une configuration simple. Vous pouvez passer à Chatflow plus tard.',
    workflowUserDescription: 'Créez visuellement des flux IA autonomes avec la simplicité du glisser-déposer.',
    completionUserDescription: 'Créez rapidement un assistant IA pour les tâches de génération de texte avec une configuration simple.',
    agentUserDescription: 'Un agent intelligent capable d’un raisonnement itératif et d’une utilisation autonome d’outils pour atteindre les objectifs de la tâche.',
    forBeginners: 'Types d’applications plus basiques',
    foundResult: '{{compte}} Résultat',
    noIdeaTip: 'Pas d’idées ? Consultez nos modèles',
    optional: 'Optionnel',
    advancedShortDescription: 'Workflow amélioré pour conversations multi-tours',
    dropDSLToCreateApp: 'Déposez le fichier DSL ici pour créer une application',
  },
  editApp: 'Modifier les informations',
  editAppTitle: 'Modifier les informations de l\'application',
  editDone: 'Informations sur l\'application mises à jour',
  editFailed: 'Échec de la mise à jour des informations de l\'application',
  iconPicker: {
    ok: 'OK',
    cancel: 'Annuler',
    emoji: 'Emoji',
    image: 'Image',
  },
  switch: 'Passer à l\'orchestration de flux de travail',
  switchTipStart: 'Une nouvelle copie de l\'application sera créée pour vous, et la nouvelle copie passera à l\'orchestration de flux de travail. La nouvelle copie ne permettra pas le ',
  switchTip: 'retour',
  switchTipEnd: ' à l\'orchestration de base.',
  switchLabel: 'La copie de l\'application à créer',
  removeOriginal: 'Supprimer l\'application d\'origine',
  switchStart: 'Commencer la commutation',
  typeSelector: {
    all: 'Tous Types',
    chatbot: 'Chatbot',
    agent: 'Agent',
    workflow: 'Flux de travail',
    completion: 'Terminaison',
    advanced: 'Chatflow',
  },
  tracing: {
    title: 'Traçage des performances de l\'application',
    description: 'Configuration d\'un fournisseur LLMOps tiers et traçage des performances de l\'application.',
    config: 'Configurer',
    collapse: 'Réduire',
    expand: 'Développer',
    tracing: 'Traçage',
    disabled: 'Désactivé',
    disabledTip: 'Veuillez d\'abord configurer le fournisseur',
    enabled: 'En service',
    tracingDescription: 'Capturez le contexte complet de l\'exécution de l\'application, y compris les appels LLM, le contexte, les prompts, les requêtes HTTP et plus encore, vers une plateforme de traçage tierce.',
    configProviderTitle: {
      configured: 'Configuré',
      notConfigured: 'Configurez le fournisseur pour activer le traçage',
      moreProvider: 'Plus de fournisseurs',
    },
    arize: {
      title: 'Arize',
      description: 'Observabilité de LLM de niveau entreprise, évaluation en ligne et hors ligne, surveillance et expérimentation—alimentée par OpenTelemetry. Conçue spécialement pour les applications basées sur LLM et agents.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Plateforme open-source basée sur OpenTelemetry pour l’observabilité, l’évaluation, l’ingénierie des prompts et l’expérimentation de vos flux de travail et agents LLM.',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'Une plateforme de développement tout-en-un pour chaque étape du cycle de vie des applications basées sur LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Traces, évaluations, gestion des prompts et métriques pour déboguer et améliorer votre application LLM.',
    },
    inUse: 'En utilisation',
    configProvider: {
      title: 'Configurer ',
      placeholder: 'Entrez votre {{key}}',
      project: 'Projet',
      publicKey: 'Clé Publique',
      secretKey: 'Clé Secrète',
      viewDocsLink: 'Voir la documentation de {{key}}',
      removeConfirmTitle: 'Supprimer la configuration de {{key}} ?',
      removeConfirmContent: 'La configuration actuelle est en cours d\'utilisation, sa suppression désactivera la fonction de Traçage.',
    },
    view: 'Vue',
    opik: {
      description: 'Opik est une plate-forme open-source pour l’évaluation, le test et la surveillance des applications LLM.',
      title: 'Opik',
    },
    weave: {
      title: 'Tisser',
      description: 'Weave est une plateforme open-source pour évaluer, tester et surveiller les applications LLM.',
    },
    aliyun: {
      title: 'Surveillance Cloud',
      description: 'La plateforme d\'observabilité entièrement gérée et sans maintenance fournie par Alibaba Cloud permet une surveillance, un traçage et une évaluation prêts à l\'emploi des applications Dify.',
    },
  },
  answerIcon: {
    description: 'S’il faut utiliser l’icône web app pour remplacer 🤖 dans l’application partagée',
    title: 'Utiliser l’icône web app pour remplacer 🤖',
    descriptionInExplore: 'Utilisation de l’icône web app pour remplacer 🤖 dans Explore',
  },
  importFromDSLUrlPlaceholder: 'Collez le lien DSL ici',
  importFromDSL: 'Importation à partir d’une DSL',
  importFromDSLUrl: 'À partir de l’URL',
  importFromDSLFile: 'À partir d’un fichier DSL',
  mermaid: {
    handDrawn: 'Dessiné à la main',
    classic: 'Classique',
  },
  openInExplore: 'Ouvrir dans Explorer',
  newAppFromTemplate: {
    sidebar: {
      HR: 'RH',
      Assistant: 'Assistant',
      Writing: 'Écriture',
      Programming: 'Programmation',
      Recommended: 'Recommandé',
      Workflow: 'Flux de travail',
      Agent: 'Agent',
    },
    byCategories: 'PAR CATÉGORIES',
    searchAllTemplate: 'Rechercher dans tous les modèles...',
  },
  showMyCreatedAppsOnly: 'Afficher uniquement mes applications créées',
  appSelector: {
    noParams: 'Aucun paramètre nécessaire',
    params: 'PARAMÈTRES DE L’APPLICATION',
    label: 'APPLI',
    placeholder: 'Sélectionnez une application...',
  },
  structOutput: {
    LLMResponse: 'Réponse LLM',
    notConfiguredTip: 'La sortie structurée n\'a pas encore été configurée.',
    required: 'Obligatoire',
    structuredTip: 'Les sorties structurées sont une fonctionnalité qui garantit que le modèle générera toujours des réponses qui respectent votre schéma JSON fourni.',
    modelNotSupportedTip: 'Le modèle actuel ne prend pas en charge cette fonctionnalité et est automatiquement rétrogradé à l\'injection de prompt.',
    modelNotSupported: 'Modèle non pris en charge',
    moreFillTip: 'Affichage d\'un maximum de 10 niveaux d\'imbrication',
    configure: 'Configurer',
    structured: 'systématique',
  },
  accessItemsDescription: {
    anyone: 'Tout le monde peut accéder à l\'application web.',
    specific: 'Seules des groupes ou membres spécifiques peuvent accéder à l\'application web.',
    organization: 'Toute personne dans l\'organisation peut accéder à l\'application web.',
    external: 'Seuls les utilisateurs externes authentifiés peuvent accéder à l\'application Web.',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Quiconque avec le lien',
      specific: 'Groupes ou membres spécifiques',
      organization: 'Seuls les membres au sein de l\'entreprise',
      external: 'Utilisateurs externes authentifiés',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Rechercher des groupes et des membres',
      allMembers: 'Tous les membres',
      expand: 'Développer',
      noResult: 'Aucun résultat',
    },
    title: 'Contrôle d\'accès à l\'application Web',
    description: 'Définir les autorisations d\'accès à l\'application web',
    accessLabel: 'Qui a accès',
    groups_one: '{{count}} GROUPE',
    groups_other: '{{count}} GROUPES',
    members_one: '{{count}} MEMBRE',
    members_other: '{{count}} MEMBRES',
    noGroupsOrMembers: 'Aucun groupe ou membre sélectionné',
    webAppSSONotEnabledTip: 'Veuillez contacter l\'administrateur de l\'entreprise pour configurer la méthode d\'authentification de l\'application web.',
    updateSuccess: 'Mise à jour réussie',
  },
  publishApp: {
    title: 'Qui peut accéder à l\'application web',
    notSet: 'Non défini',
    notSetDesc: 'Actuellement, personne ne peut accéder à l\'application web. Veuillez définir les autorisations.',
  },
  accessControl: 'Contrôle d\'accès à l\'application Web',
  noAccessPermission: 'Pas de permission d\'accéder à l\'application web',
  maxActiveRequestsPlaceholder: 'Entrez 0 pour illimité',
  maxActiveRequests: 'Nombre maximal de requêtes simultanées',
}

export default translation
