const translation = {
  pageTitle: 'Salut, commençons !👋',
  welcome: 'Bien<PERSON>ue sur Dify, veuillez vous connecter pour continuer.',
  email: 'Adresse e-mail',
  emailPlaceholder: 'Votre email',
  password: 'Mot de passe',
  passwordPlaceholder: 'Votre mot de passe',
  name: 'Nom d\'utilisateur',
  namePlaceholder: 'Votre nom d\'utilisateur',
  forget: 'Mot de passe oublié ?',
  signBtn: 'Se connecter',
  installBtn: 'Mettre en place',
  setAdminAccount: 'Configuration d\'un compte administrateur',
  setAdminAccountDesc: 'Privilèges maximum pour le compte administrateur, qui peut être utilisé pour créer des applications et gérer les fournisseurs de LLM, etc.',
  createAndSignIn: 'Créer et se connecter',
  oneMoreStep: 'Une étape de plus',
  createSample: 'Sur la base de ces informations, nous créerons une application exemple pour vous',
  invitationCode: 'Code d\'invitation',
  invitationCodePlaceholder: 'Votre code d\'invitation',
  interfaceLanguage: 'Langue de l\'interface',
  timezone: 'Fuseau horaire',
  go: 'Aller à Dify',
  sendUsMail: 'Envoyez-nous votre introduction, et nous nous occuperons de la demande d\'invitation.',
  acceptPP: 'J\'ai lu et j\'accepte la politique de confidentialité',
  reset: 'Veuillez exécuter la commande suivante pour réinitialiser votre mot de passe',
  withGitHub: 'Continuer avec GitHub',
  withGoogle: 'Continuer avec Google',
  rightTitle: 'Débloquez le plein potentiel des LLM',
  rightDesc: 'Construisez sans effort des applications IA visuellement captivantes, opérationnelles et améliorables.',
  tos: 'Conditions de Service',
  pp: 'Politique de Confidentialité',
  tosDesc: 'En vous inscrivant, vous acceptez nos',
  goToInit: 'Si vous n\'avez pas initialisé le compte, veuillez vous rendre sur la page d\'initialisation',
  dontHave: 'Vous n\'avez pas ?',
  invalidInvitationCode: 'Code d\'invitation invalide',
  accountAlreadyInited: 'Compte déjà initialisé',
  forgotPassword: 'Mot de passe oublié?',
  resetLinkSent: 'Lien de réinitialisation envoyé',
  sendResetLink: 'Envoyer le lien de réinitialisation',
  backToSignIn: 'Retour à la connexion',
  forgotPasswordDesc: 'Veuillez entrer votre adresse e-mail pour réinitialiser votre mot de passe. Nous vous enverrons un e-mail avec des instructions sur la réinitialisation de votre mot de passe.',
  checkEmailForResetLink: 'Veuillez vérifier votre e-mail pour un lien de réinitialisation de votre mot de passe. S\'il n\'apparaît pas dans quelques minutes, assurez-vous de vérifier votre dossier de spam.',
  passwordChanged: 'Connectez-vous maintenant',
  changePassword: 'Changer le mot de passe',
  changePasswordTip: 'Veuillez entrer un nouveau mot de passe pour votre compte',
  invalidToken: 'Token invalide ou expiré',
  confirmPassword: 'Confirmez le mot de passe',
  confirmPasswordPlaceholder: 'Confirmez votre nouveau mot de passe',
  passwordChangedTip: 'Votre mot de passe a été changé avec succès',
  error: {
    emailEmpty: 'Une adresse e-mail est requise',
    emailInValid: 'Veuillez entrer une adresse email valide',
    nameEmpty: 'Le nom est requis',
    passwordEmpty: 'Un mot de passe est requis',
    passwordInvalid: 'Le mot de passe doit contenir des lettres et des chiffres, et la longueur doit être supérieure à 8.',
    passwordLengthInValid: 'Le mot de passe doit comporter au moins 8 caractères.',
    registrationNotAllowed: 'Compte introuvable. Veuillez contacter l’administrateur système pour vous inscrire.',
  },
  license: {
    tip: 'Avant de commencer Dify Community Edition, lisez le GitHub',
    link: 'Licence Open-source',
  },
  join: 'Rejoindre',
  joinTipStart: 'Je vous invite à rejoindre',
  joinTipEnd: 'équipe sur Dify',
  invalid: 'Le lien a expiré',
  explore: 'Explorez Dify',
  activatedTipStart: 'Vous avez rejoint le',
  activatedTipEnd: 'équipe',
  activated: 'Connectez-vous maintenant',
  adminInitPassword: 'Mot de passe d\'initialisation de l\'administrateur',
  validate: 'Valider',
  sso: 'Poursuivre avec l’authentification unique',
  checkCode: {
    verificationCode: 'Code de vérification',
    useAnotherMethod: 'Utiliser une autre méthode',
    didNotReceiveCode: 'Vous n’avez pas reçu le code ?',
    emptyCode: 'Le code est requis',
    verify: 'Vérifier',
    verificationCodePlaceholder: 'Entrez le code à 6 chiffres',
    resend: 'Renvoyer',
    invalidCode: 'Code non valide',
    checkYourEmail: 'Vérifiez vos e-mails',
    validTime: 'Gardez à l’esprit que le code est valable 5 minutes',
    tips: 'Nous envoyons un code de vérification à <strong>{{email}}</strong>',
  },
  sendVerificationCode: 'Envoyer le code de vérification',
  or: 'OU',
  continueWithCode: 'Continuer avec le code',
  useVerificationCode: 'Utiliser le code de vérification',
  noLoginMethod: 'Méthode d’authentification non configurée',
  backToLogin: 'Retour à la connexion',
  changePasswordBtn: 'Définir un mot de passe',
  setYourAccount: 'Configurer votre compte',
  withSSO: 'Poursuivre avec l’authentification unique',
  resetPassword: 'Réinitialiser le mot de passe',
  back: 'Précédent',
  enterYourName: 'Veuillez entrer votre nom d’utilisateur',
  noLoginMethodTip: 'Veuillez contacter l’administrateur système pour ajouter une méthode d’authentification.',
  resetPasswordDesc: 'Tapez l’adresse e-mail que vous avez utilisée pour vous inscrire sur Dify et nous vous enverrons un e-mail de réinitialisation de mot de passe.',
  usePassword: 'Utiliser le mot de passe',
  licenseInactiveTip: 'La licence Dify Enterprise de votre espace de travail est inactive. Veuillez contacter votre administrateur pour continuer à utiliser Dify.',
  licenseLostTip: 'Échec de la connexion au serveur de licences Dify. Veuillez contacter votre administrateur pour continuer à utiliser Dify.',
  licenseExpired: 'Licence expirée',
  licenseLost: 'Licence perdue',
  licenseExpiredTip: 'La licence Dify Enterprise de votre espace de travail a expiré. Veuillez contacter votre administrateur pour continuer à utiliser Dify.',
  licenseInactive: 'Licence inactive',
  webapp: {
    noLoginMethodTip: 'Veuillez contacter l\'administrateur système pour ajouter une méthode d\'authentification.',
    noLoginMethod: 'Méthode d\'authentification non configurée pour l\'application web',
    disabled: 'L\'authentification de l\'application web est désactivée. Veuillez contacter l\'administrateur du système pour l\'activer. Vous pouvez essayer d\'utiliser l\'application directement.',
    login: 'Connexion',
  },
}

export default translation
