const translation = {
  input: 'ENTRÉE',
  result: 'RÉSULTAT',
  detail: 'DÉTAIL',
  tracing: 'TRACE',
  resultPanel: {
    status: 'STATUT',
    time: 'TEMPS ÉCOULÉ',
    tokens: 'TOTAL DES JETONS',
  },
  meta: {
    title: 'MÉTADONNÉES',
    status: 'Statut',
    version: 'Version',
    executor: 'Exécuteur',
    startTime: 'Heure de début',
    time: 'Temps écoulé',
    tokens: 'Total des jetons',
    steps: 'Étapes d\'exécution',
  },
  resultEmpty: {
    title: 'Cela exécute uniquement le format de sortie JSON,',
    tipLeft: 'veuillez aller à ',
    link: 'panneau de détail',
    tipRight: ' visualisez-le.',
  },
  actionLogs: 'Journaux d’actions',
  circularInvocationTip: 'Il y a un appel circulaire d’outils/nœuds dans le flux de travail actuel.',
}

export default translation
