const translation = {
  title: 'Outils',
  createCustomTool: 'Créer un Outil Personnalisé',
  type: {
    all: 'Tout',
    builtIn: 'Intégré',
    custom: 'Personnalisé',
    workflow: 'Flux de travail',
  },
  contribute: {
    line1: 'Je suis intéressé par',
    line2: 'contribuer des outils à Dify.',
    viewGuide: 'Voir le guide',
  },
  author: 'Par',
  auth: {
    authorized: 'Autorisé',
    setup: 'Mettez en place l\'autorisation à utiliser',
    setupModalTitle: 'Configurer l\'Autorisation',
    setupModalTitleDescription: 'Après avoir configuré les identifiants, tous les membres de l\'espace de travail peuvent utiliser cet outil lors de l\'orchestration des applications.',
  },
  includeToolNum: '{{num}} outils inclus',
  addTool: 'Ajouter un outil',
  createTool: {
    title: 'Créer un Outil Personnalisé',
    editAction: 'Configurer',
    editTitle: 'Modifier l\'Outil Personnalisé',
    name: 'Nom',
    toolNamePlaceHolder: 'Entrez le nom de l\'outil',
    schema: 'Schéma',
    schemaPlaceHolder: 'Entrez votre schéma OpenAPI ici',
    viewSchemaSpec: 'Voir la spécification OpenAPI-Swagger',
    importFromUrl: 'Importer depuis l\'URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Veuillez entrer une URL valide',
    examples: 'Exemples',
    exampleOptions: {
      json: 'Météo(JSON)',
      yaml: 'Animalerie (YAML)',
      blankTemplate: 'Modèle Vierge',
    },
    availableTools: {
      title: 'Outils Disponibles',
      name: 'Nom',
      description: 'Description',
      method: 'Méthode',
      path: 'Chemin',
      action: 'Actions',
      test: 'Test',
    },
    authMethod: {
      title: 'Méthode d\'autorisation',
      type: 'Type d\'autorisation',
      keyTooltip: 'Clé de l\'en-tête HTTP. Vous pouvez la laisser telle quelle avec "Autorisation" si vous n\'avez aucune idée de ce que c\'est, ou la définir sur une valeur personnalisée.',
      types: {
        none: 'Aucun',
        api_key: 'Clé API',
        apiKeyPlaceholder: 'Nom de l\'en-tête HTTP pour la clé API',
        apiValuePlaceholder: 'Entrez la clé API',
        api_key_query: 'Paramètre de requête',
        queryParamPlaceholder: 'Nom du paramètre de requête pour la clé API',
        api_key_header: 'En-tête',
      },
      key: 'Clé',
      value: 'Valeur',
      queryParam: 'Paramètre de requête',
      queryParamTooltip: 'Le nom du paramètre de requête de la clé API à passer, par exemple "key" dans "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      title: 'Type d\'Authentification',
      types: {
        basic: 'Basique',
        bearer: 'Porteur',
        custom: 'Personnalisé',
      },
    },
    privacyPolicy: 'Politique de confidentialité',
    privacyPolicyPlaceholder: 'Veuillez entrer la politique de confidentialité',
    customDisclaimer: 'Clause de non-responsabilité personnalisée',
    customDisclaimerPlaceholder: 'Entrez le texte de la clause de non-responsabilité personnalisée',
    deleteToolConfirmTitle: 'Supprimer cet outil ?',
    deleteToolConfirmContent: 'La suppression de l\'outil est irréversible. Les utilisateurs ne pourront plus accéder à votre outil.',
    toolInput: {
      required: 'Obligatoire',
      name: 'Nom',
      label: 'Étiquettes',
      title: 'Entrée d’outil',
      methodSetting: 'Réglage',
      labelPlaceholder: 'Choisir des balises(facultatif)',
      descriptionPlaceholder: 'Description de la signification du paramètre',
      method: 'Méthode',
      methodParameter: 'Paramètre',
      methodSettingTip: 'L’utilisateur renseigne la configuration de l’outil',
      methodParameterTip: 'Remplissages LLM pendant l’inférence',
      description: 'Description',
    },
    nameForToolCallTip: 'Ne prend en charge que les chiffres, les lettres et les traits de soulignement.',
    confirmTitle: 'Confirmer pour enregistrer ?',
    nameForToolCall: 'Nom de l’appel de l’outil',
    confirmTip: 'Les applications utilisant cet outil seront affectées',
    description: 'Description',
    nameForToolCallPlaceHolder: 'Utilisé pour la reconnaissance automatique, tels que getCurrentWeather, list_pets',
    descriptionPlaceholder: 'Brève description de l’objectif de l’outil, par exemple, obtenir la température d’un endroit spécifique.',
  },
  test: {
    title: 'Test',
    parametersValue: 'Paramètres & Valeur',
    parameters: 'Paramètres',
    value: 'Valeur',
    testResult: 'Résultats du Test',
    testResultPlaceholder: 'Le résultat du test s\'affichera ici',
  },
  thought: {
    using: 'Utilisation',
    used: 'Utilisé',
    requestTitle: 'Demande à',
    responseTitle: 'Réponse de',
  },
  setBuiltInTools: {
    info: 'Infos',
    setting: 'Paramètres',
    toolDescription: 'Description de l\'outil',
    parameters: 'paramètres',
    string: 'chaîne',
    number: 'nombre',
    required: 'Requis',
    infoAndSetting: 'Infos & Paramètres',
    file: 'lime',
  },
  noCustomTool: {
    title: 'Pas d\'outils personnalisés !',
    content: 'Ajoutez et gérez vos outils personnalisés ici pour construire des applications IA.',
    createTool: 'Créer un outil',
  },
  noSearchRes: {
    title: 'Désolé, aucun résultat !',
    content: 'Nous n\'avons trouvé aucun outil correspondant à votre recherche.',
    reset: 'Réinitialiser la recherche',
  },
  builtInPromptTitle: 'Invite',
  toolRemoved: 'Outil supprimé',
  notAuthorized: 'Outil non autorisé',
  howToGet: 'Comment obtenir',
  addToolModal: {
    type: 'type',
    added: 'supplémentaire',
    add: 'ajouter',
    category: 'catégorie',
    manageInTools: 'Gérer dans Outils',
    custom: {
      title: 'Aucun outil personnalisé disponible',
      tip: 'Créer un outil personnalisé',
    },
    workflow: {
      title: 'Aucun outil de workflow disponible',
      tip: 'Publier des workflows en tant qu\'outils dans le Studio',
    },
    mcp: {
      title: 'Aucun outil MCP disponible',
      tip: 'Ajouter un serveur MCP',
    },
    agent: {
      title: 'Aucune stratégie d\'agent disponible',
    },
  },
  openInStudio: 'Ouvrir dans Studio',
  customToolTip: 'En savoir plus sur les outils personnalisés Dify',
  toolNameUsageTip: 'Nom de l’appel de l’outil pour le raisonnement et l’invite de l’agent',
  copyToolName: 'Copier le nom',
  noTools: 'Aucun outil trouvé',
  mcp: {
    create: {
      cardTitle: 'Ajouter un Serveur MCP (HTTP)',
      cardLink: 'En savoir plus sur l\'intégration du serveur MCP',
    },
    noConfigured: 'Serveur Non Configuré',
    updateTime: 'Mis à jour',
    toolsCount: '{count} outils',
    noTools: 'Aucun outil disponible',
    modal: {
      title: 'Ajouter un Serveur MCP (HTTP)',
      editTitle: 'Modifier le Serveur MCP (HTTP)',
      name: 'Nom & Icône',
      namePlaceholder: 'Nommez votre serveur MCP',
      serverUrl: 'URL du Serveur',
      serverUrlPlaceholder: 'URL de l\'endpoint du serveur',
      serverUrlWarning: 'Mettre à jour l\'adresse du serveur peut perturber les applications qui dépendent de ce serveur',
      serverIdentifier: 'Identifiant du Serveur',
      serverIdentifierTip: 'Identifiant unique pour le serveur MCP au sein de l\'espace de travail. Seulement des lettres minuscules, des chiffres, des traits de soulignement et des tirets. Jusqu\'à 24 caractères.',
      serverIdentifierPlaceholder: 'Identifiant unique, par ex. mon-serveur-mcp',
      serverIdentifierWarning: 'Le serveur ne sera pas reconnu par les applications existantes après un changement d\'ID',
      cancel: 'Annuler',
      save: 'Enregistrer',
      confirm: 'Ajouter & Authoriser',
    },
    delete: 'Supprimer le Serveur MCP',
    deleteConfirmTitle: 'Souhaitez-vous supprimer {mcp}?',
    operation: {
      edit: 'Modifier',
      remove: 'Supprimer',
    },
    authorize: 'Autoriser',
    authorizing: 'Autorisation en cours...',
    authorizingRequired: 'L\'autorisation est requise',
    authorizeTip: 'Après autorisation, les outils seront affichés ici.',
    update: 'Mettre à jour',
    updating: 'Mise à jour en cours',
    gettingTools: 'Obtention des Outils...',
    updateTools: 'Mise à jour des Outils...',
    toolsEmpty: 'Outils non chargés',
    getTools: 'Obtenir des outils',
    toolUpdateConfirmTitle: 'Mettre à jour la Liste des Outils',
    toolUpdateConfirmContent: 'La mise à jour de la liste des outils peut affecter les applications existantes. Souhaitez-vous continuer?',
    toolsNum: '{count} outils inclus',
    onlyTool: '1 outil inclus',
    identifier: 'Identifiant du Serveur (Cliquez pour Copier)',
    server: {
      title: 'Serveur MCP',
      url: 'URL du Serveur',
      reGen: 'Voulez-vous régénérer l\'URL du serveur?',
      addDescription: 'Ajouter une description',
      edit: 'Modifier la description',
      modal: {
        addTitle: 'Ajouter une description pour activer le serveur MCP',
        editTitle: 'Modifier la description',
        description: 'Description',
        descriptionPlaceholder: 'Expliquez ce que fait cet outil et comment il doit être utilisé par le LLM',
        parameters: 'Paramètres',
        parametersTip: 'Ajoutez des descriptions pour chaque paramètre afin d\'aider le LLM à comprendre leur objectif et leurs contraintes.',
        parametersPlaceholder: 'Objectif et contraintes du paramètre',
        confirm: 'Activer le Serveur MCP',
      },
      publishTip: 'Application non publiée. Merci de publier l\'application en premier.',
    },
  },
}

export default translation
