const translation = {
  title: 'Тестирование поиска',
  desc: 'Проверьте эффективность поиска в базе знаний на основе заданного текста запроса.',
  dateTimeFormat: 'DD.MM.YYYY HH:mm',
  recents: 'Недавние',
  table: {
    header: {
      source: 'Источник',
      text: 'Текст',
      time: 'Время',
    },
  },
  input: {
    title: 'Исходный текст',
    placeholder: 'Пож<PERSON>луйста, введите текст, рекомендуется использовать короткое повествовательное предложение.',
    countWarning: 'До 200 символов.',
    indexWarning: 'Только база знаний высокого качества.',
    testing: 'Тестирование',
  },
  hit: {
    title: 'НАЙДЕННЫЕ АБЗАЦЫ',
    emptyTip: 'Результаты тестирования поиска будут отображаться здесь',
  },
  noRecentTip: 'Здесь нет результатов недавних запросов',
  viewChart: 'Посмотреть ВЕКТОРНУЮ ДИАГРАММУ',
  viewDetail: 'Подробнее',
  settingTitle: 'Настройка извлечения',
  records: 'Записи',
  hitChunks: 'Попадание {{num}} дочерних чанков',
  chunkDetail: 'Деталь Чанка',
  open: 'Открытый',
  keyword: 'Ключевые слова',
}

export default translation
