const translation = {
  currentPlan: 'Текущий тарифный план',
  upgradeBtn: {
    plain: 'Обновить тарифный план',
    encourage: 'Обновить сейчас',
    encourageShort: 'Обновить',
  },
  viewBilling: 'Управление счетами и подписками',
  buyPermissionDeniedTip: 'Пожалуйста, свяжитесь с администратором вашей организации, чтобы подписаться',
  plansCommon: {
    title: 'Выберите тарифный план, который подходит именно вам',
    yearlyTip: 'Получите 2 месяца бесплатно, подписавшись на год!',
    mostPopular: 'Самый популярный',
    planRange: {
      monthly: 'Ежемесячно',
      yearly: 'Ежегодно',
    },
    month: 'месяц',
    year: 'год',
    save: 'Сэкономить ',
    free: 'Бесплатно',
    currentPlan: 'Текущий тарифный план',
    contractSales: 'Связаться с отделом продаж',
    contractOwner: 'Связаться с руководителем команды',
    startForFree: 'Начать бесплатно',
    getStartedWith: 'Начать с ',
    contactSales: 'Связаться с отделом продаж',
    talkToSales: 'Поговорить с отделом продаж',
    modelProviders: 'Поставщики моделей',
    teamMembers: 'Участники команды',
    annotationQuota: 'Квота аннотаций',
    buildApps: 'Создать приложения',
    vectorSpace: 'Векторное пространство',
    vectorSpaceBillingTooltip: 'Каждый 1 МБ может хранить около 1,2 миллиона символов векторизованных данных (оценка с использованием Embeddings OpenAI, варьируется в зависимости от модели).',
    vectorSpaceTooltip: 'Векторное пространство - это система долговременной памяти, необходимая LLM для понимания ваших данных.',
    documentsUploadQuota: 'Квота загрузки документов',
    documentProcessingPriority: 'Приоритет обработки документов',
    documentProcessingPriorityTip: 'Для более высокого приоритета обработки документов, пожалуйста, обновите свой тарифный план.',
    documentProcessingPriorityUpgrade: 'Обрабатывайте больше данных с большей точностью и на более высоких скоростях.',
    priority: {
      'standard': 'Стандартный',
      'priority': 'Приоритетный',
      'top-priority': 'Высокий приоритет',
    },
    logsHistory: 'История журналов',
    customTools: 'Пользовательские инструменты',
    unavailable: 'Недоступно',
    days: 'дней',
    unlimited: 'Неограниченно',
    support: 'Поддержка',
    supportItems: {
      communityForums: 'Форумы сообщества',
      emailSupport: 'Поддержка по электронной почте',
      priorityEmail: 'Приоритетная поддержка по электронной почте и в чате',
      logoChange: 'Изменение логотипа',
      SSOAuthentication: 'SSO аутентификация',
      personalizedSupport: 'Персональная поддержка',
      dedicatedAPISupport: 'Выделенная поддержка API',
      customIntegration: 'Пользовательская интеграция и поддержка',
      ragAPIRequest: 'Запросы RAG API',
      bulkUpload: 'Массовая загрузка документов',
      agentMode: 'Режим агента',
      workflow: 'Рабочий процесс',
      llmLoadingBalancing: 'Балансировка нагрузки LLM',
      llmLoadingBalancingTooltip: 'Добавьте несколько ключей API к моделям, эффективно обходя ограничения скорости API.',
    },
    comingSoon: 'Скоро',
    member: 'Участник',
    memberAfter: 'Участник',
    messageRequest: {
      title: 'Кредиты на сообщения',
      tooltip: 'Квоты вызова сообщений для различных тарифных планов, использующих модели OpenAI (кроме gpt4). Сообщения, превышающие лимит, будут использовать ваш ключ API OpenAI.',
      titlePerMonth: '{{count,number}} сообщений/месяц',
    },
    annotatedResponse: {
      title: 'Ограничения квоты аннотаций',
      tooltip: 'Ручное редактирование и аннотирование ответов обеспечивает настраиваемые высококачественные возможности ответов на вопросы для приложений. (Применимо только в чат-приложениях)',
    },
    ragAPIRequestTooltip: 'Относится к количеству вызовов API, вызывающих только возможности обработки базы знаний Dify.',
    receiptInfo: 'Только владелец команды и администратор команды могут подписываться и просматривать информацию о выставлении счетов',
    cloud: 'Облачный сервис',
    annualBilling: 'Ежегодная оплата',
    apiRateLimit: 'Ограничение скорости API',
    self: 'Самостоятельно размещенный',
    teamMember_other: '{{count,number}} Члены команды',
    apiRateLimitUnit: '{{count,number}}/день',
    unlimitedApiRate: 'Нет ограничений на количество запросов к API',
    freeTrialTip: 'бесплатная пробная версия из 200 вызовов OpenAI.',
    freeTrialTipSuffix: 'Кредитная карта не требуется',
    teamMember_one: '{{count,number}} Член команды',
    getStarted: 'Начать',
    teamWorkspace: '{{count,number}} Командное рабочее пространство',
    freeTrialTipPrefix: 'Зарегистрируйтесь и получите',
    comparePlanAndFeatures: 'Сравните планы и функции',
    documents: '{{count,number}} Документов знаний',
    documentsRequestQuota: '{{count,number}}/мин Лимит Частоты Запросов на Знание',
    apiRateLimitTooltip: 'Ограничение скорости API применяется ко всем запросам, сделанным через API Dify, включая генерацию текста, чатовую переписку, выполнение рабочих процессов и обработку документов.',
    documentsRequestQuotaTooltip: 'Указывает общее количество действий, которые рабочая область может выполнять в минуту внутри базы знаний, включая создание, удаление, обновление наборов данных, загрузку документов, модификации, архивирование и запросы к базе знаний. Эта метрика используется для оценки производительности запросов к базе знаний. Например, если пользователь Sandbox выполняет 10 последовательных тестов за один минуту, его рабочая область будет временно ограничена в выполнении следующих действий в течение следующей минуты: создание, удаление, обновление наборов данных и загрузка или модификация документов.',
    priceTip: 'по рабочему месту/',
    documentsTooltip: 'Квота на количество документов, импортируемых из источника знаний.',
  },
  plans: {
    sandbox: {
      name: 'Песочница',
      description: '200 бесплатных пробных использований GPT',
      includesTitle: 'Включает:',
      for: 'Бесплатная пробная версия основных возможностей',
    },
    professional: {
      name: 'Профессиональный',
      description: 'Для частных лиц и небольших команд, чтобы разблокировать больше возможностей по доступной цене.',
      includesTitle: 'Все в бесплатном плане, плюс:',
      for: 'Для независимых разработчиков/малых команд',
    },
    team: {
      name: 'Команда',
      description: 'Сотрудничайте без ограничений и наслаждайтесь высочайшей производительностью.',
      includesTitle: 'Все в профессиональном плане, плюс:',
      for: 'Для команд среднего размера',
    },
    enterprise: {
      name: 'Корпоративный',
      description: 'Получите полный набор возможностей и поддержку для крупномасштабных критически важных систем.',
      includesTitle: 'Все в командном плане, плюс:',
      features: {
        7: 'Обновления и обслуживание от Dify официально',
        4: 'ССО',
        8: 'Профессиональная техническая поддержка',
        6: 'Современная безопасность и контроль',
        2: 'Эксклюзивные функции для предприятий',
        1: 'Коммерческая лицензия',
        3: 'Множественные рабочие области и управление предприятием',
        0: 'Решения для масштабируемого развертывания корпоративного уровня',
        5: 'Согласованные Соглашения об Уровне Услуг от Dify Partners',
      },
      price: 'Пользовательский',
      priceTip: 'Только годовая подписка',
      for: 'Для команд большого размера',
      btnText: 'Связаться с отделом продаж',
    },
    community: {
      features: {
        0: 'Все основные функции выпущены в публичном репозитории',
        1: 'Единое рабочее пространство',
        2: 'Соблюдает Лицензию на открытое программное обеспечение Dify',
      },
      name: 'Сообщество',
      btnText: 'Начните с сообщества',
      price: 'Свободно',
      includesTitle: 'Бесплатные функции:',
      description: 'Для отдельных пользователей, малых команд или некоммерческих проектов',
      for: 'Для отдельных пользователей, малых команд или некоммерческих проектов',
    },
    premium: {
      features: {
        3: 'Приоритетная поддержка по электронной почте и чату',
        1: 'Единое рабочее пространство',
        2: 'Настройка логотипа и брендинга веб-приложения',
        0: 'Самостоятельное управление надежностью различными облачными провайдерами',
      },
      description: 'Для средних организаций и команд',
      includesTitle: 'Всё из Сообщества, плюс:',
      priceTip: 'На основе облачного маркетплейса',
      btnText: 'Получите Премиум в',
      comingSoon: 'Поддержка Microsoft Azure и Google Cloud скоро появится',
      price: 'Масштабируемый',
      for: 'Для средних организаций и команд',
      name: 'Премиум',
    },
  },
  vectorSpace: {
    fullTip: 'Векторное пространство заполнено.',
    fullSolution: 'Обновите свой тарифный план, чтобы получить больше места.',
  },
  apps: {
    fullTipLine1: 'Обновите свой тарифный план, чтобы',
    fullTipLine2: 'создавать больше приложений.',
    fullTip2des: 'Рекомендуется удалить неактивные приложения, чтобы освободить место, или свяжитесь с нами.',
    fullTip2: 'Достигнут лимит плана',
    contactUs: 'Свяжитесь с нами',
    fullTip1des: 'Вы достигли предела создания приложений по этому плану',
    fullTip1: 'Обновите, чтобы создать больше приложений',
  },
  annotatedResponse: {
    fullTipLine1: 'Обновите свой тарифный план, чтобы',
    fullTipLine2: 'аннотировать больше разговоров.',
    quotaTitle: 'Квота ответов аннотаций',
  },
  usagePage: {
    buildApps: 'Создавайте приложения',
    teamMembers: 'Члены команды',
    vectorSpaceTooltip: 'Документы с режимом индексирования высокого качества будут потреблять ресурсы Хранилища Знаний. Когда Хранилище Знаний достигнет предела, новые документы не будут загружены.',
    annotationQuota: 'Квота аннотации',
    vectorSpace: 'Хранилище данных знаний',
    documentsUploadQuota: 'Квота на загрузку документов',
  },
  teamMembers: 'Члены команды',
}

export default translation
