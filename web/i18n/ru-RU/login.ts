const translation = {
  pageTitle: 'Привет, давайте начнем!👋',
  welcome: 'До<PERSON><PERSON><PERSON> пожаловать в Dify, пожалуйста, войдите, чтобы продолжить.',
  email: 'Адрес электронной почты',
  emailPlaceholder: 'Ваш адрес электронной почты',
  password: 'Пароль',
  passwordPlaceholder: 'Ваш пароль',
  name: 'Имя пользователя',
  namePlaceholder: 'Ваше имя пользователя',
  forget: 'Забыли пароль?',
  signBtn: 'Войти',
  sso: 'Продолжить с SSO',
  installBtn: 'Настроить',
  setAdminAccount: 'Настройка учетной записи администратора',
  setAdminAccountDesc: 'Максимальные привилегии для учетной записи администратора, которые можно использовать для создания приложений, управления поставщиками LLM и т. д.',
  createAndSignIn: 'Создать и войти',
  oneMoreStep: 'Еще один шаг',
  createSample: 'На основе этой информации мы создадим для вас пример приложения',
  invitationCode: 'Пригласительный код',
  invitationCodePlaceholder: 'Ваш пригласительный код',
  interfaceLanguage: 'Язык интерфейса',
  timezone: 'Часовой пояс',
  go: 'Перейти к Dify',
  sendUsMail: 'Отправьте нам по электронной почте свое представление, и мы обработаем запрос на приглашение.',
  acceptPP: 'Я прочитал и принимаю политику конфиденциальности',
  reset: 'Пожалуйста, выполните следующую команду, чтобы сбросить пароль',
  withGitHub: 'Продолжить с GitHub',
  withGoogle: 'Продолжить с Google',
  rightTitle: 'Раскройте весь потенциал LLM',
  rightDesc: 'Без труда создавайте визуально привлекательные, работоспособные и улучшаемые приложения ИИ.',
  tos: 'Условия обслуживания',
  pp: 'Политика конфиденциальности',
  tosDesc: 'Регистрируясь, вы соглашаетесь с нашими',
  goToInit: 'Если вы не инициализировали учетную запись, перейдите на страницу инициализации',
  dontHave: 'Нет?',
  invalidInvitationCode: 'Неверный пригласительный код',
  accountAlreadyInited: 'Учетная запись уже инициализирована',
  forgotPassword: 'Забыли пароль?',
  resetLinkSent: 'Ссылка для сброса отправлена',
  sendResetLink: 'Отправить ссылку для сброса',
  backToSignIn: 'Вернуться к входу',
  forgotPasswordDesc: 'Пожалуйста, введите свой адрес электронной почты, чтобы сбросить пароль. Мы отправим вам электронное письмо с инструкциями о том, как сбросить пароль.',
  checkEmailForResetLink: 'Пожалуйста, проверьте свою электронную почту на наличие ссылки для сброса пароля. Если она не появится в течение нескольких минут, обязательно проверьте папку со спамом.',
  passwordChanged: 'Войдите сейчас',
  changePassword: 'Изменить пароль',
  changePasswordTip: 'Пожалуйста, введите новый пароль для своей учетной записи',
  invalidToken: 'Неверный или просроченный токен',
  confirmPassword: 'Подтвердите пароль',
  confirmPasswordPlaceholder: 'Подтвердите свой новый пароль',
  passwordChangedTip: 'Ваш пароль был успешно изменен',
  error: {
    emailEmpty: 'Адрес электронной почты обязателен',
    emailInValid: 'Пожалуйста, введите действительный адрес электронной почты',
    nameEmpty: 'Имя обязательно',
    passwordEmpty: 'Пароль обязателен',
    passwordLengthInValid: 'Пароль должен содержать не менее 8 символов',
    passwordInvalid: 'Пароль должен содержать буквы и цифры, а длина должна быть больше 8',
    registrationNotAllowed: 'Аккаунт не найден. Пожалуйста, свяжитесь с системным администратором для регистрации.',
  },
  license: {
    tip: 'Перед запуском Dify Community Edition ознакомьтесь с лицензией GitHub',
    link: 'Лицензия с открытым исходным кодом',
  },
  join: 'Присоединиться',
  joinTipStart: 'Приглашаем вас присоединиться к',
  joinTipEnd: 'команде на Dify',
  invalid: 'Ссылка истекла',
  explore: 'Изучить Dify',
  activatedTipStart: 'Вы присоединились к команде',
  activatedTipEnd: '',
  activated: 'Войдите сейчас',
  adminInitPassword: 'Пароль инициализации администратора',
  validate: 'Проверить',
  checkCode: {
    verify: 'Проверять',
    resend: 'Отправить',
    invalidCode: 'Неверный код',
    didNotReceiveCode: 'Не получили код?',
    emptyCode: 'Код обязателен для заполнения',
    verificationCode: 'Проверочный код',
    checkYourEmail: 'Проверьте свою электронную почту',
    tips: 'Мы отправляем код подтверждения на <strong>{{email}}</strong>',
    validTime: 'Имейте в виду, что код действителен в течение 5 минут',
    verificationCodePlaceholder: 'Введите 6-значный код',
    useAnotherMethod: 'Используйте другой метод',
  },
  back: 'Назад',
  changePasswordBtn: 'Установите пароль',
  usePassword: 'Использовать пароль',
  continueWithCode: 'Продолжить с кодом',
  resetPassword: 'Сброс пароля',
  withSSO: 'Продолжение работы с SSO',
  noLoginMethod: 'Метод аутентификации не настроен',
  useVerificationCode: 'Используйте код подтверждения',
  sendVerificationCode: 'Отправить код подтверждения',
  setYourAccount: 'Настройте свою учетную запись',
  backToLogin: 'Вернуться к входу',
  enterYourName: 'Пожалуйста, введите свое имя пользователя',
  noLoginMethodTip: 'Обратитесь к системному администратору, чтобы добавить метод аутентификации.',
  resetPasswordDesc: 'Введите адрес электронной почты, который вы использовали для регистрации в Dify, и мы отправим вам электронное письмо для сброса пароля.',
  or: 'ИЛИ',
  licenseInactive: 'Лицензия неактивна',
  licenseLostTip: 'Не удалось подключить сервер лицензий Dify. Обратитесь к своему администратору, чтобы продолжить использование Dify.',
  licenseExpired: 'Срок действия лицензии истек',
  licenseLost: 'Утеряна лицензия',
  licenseInactiveTip: 'Лицензия Dify Enterprise для рабочего пространства неактивна. Обратитесь к своему администратору, чтобы продолжить использование Dify.',
  licenseExpiredTip: 'Срок действия лицензии Dify Enterprise для рабочего пространства истек. Обратитесь к своему администратору, чтобы продолжить использование Dify.',
  webapp: {
    noLoginMethod: 'Метод аутентификации не настроен для веб-приложения',
    noLoginMethodTip: 'Пожалуйста, свяжитесь с администратором системы, чтобы добавить метод аутентификации.',
    disabled: 'Аутентификация веб-приложения отключена. Пожалуйста, свяжитесь с администратором системы, чтобы включить ее. Вы можете попробовать использовать приложение напрямую.',
    login: 'Вход',
  },
}

export default translation
