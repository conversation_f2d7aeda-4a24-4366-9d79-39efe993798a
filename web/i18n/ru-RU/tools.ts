const translation = {
  title: 'Инструменты',
  createCustomTool: 'Создать пользовательский инструмент',
  customToolTip: 'Узнать больше о пользовательских инструментах Dify',
  type: {
    all: 'Все',
    builtIn: 'Встроенные',
    custom: 'Пользовательские',
    workflow: 'Рабочий процесс',
  },
  contribute: {
    line1: 'Я заинтересован в',
    line2: 'внесении инструментов в Dify.',
    viewGuide: 'Посмотреть руководство',
  },
  author: 'Автор',
  auth: {
    authorized: 'Авторизовано',
    setup: 'Настроить авторизацию для использования',
    setupModalTitle: 'Настроить авторизацию',
    setupModalTitleDescription: 'После настройки учетных данных все участники рабочего пространства смогут использовать этот инструмент при оркестровке приложений.',
  },
  includeToolNum: 'Включено {{num}} инструментов',
  addTool: 'Добавить инструмент',
  addToolModal: {
    type: 'тип',
    category: 'категория',
    add: 'добавить',
    added: 'добавлено',
    manageInTools: 'Управлять в инструментах',
    custom: {
      title: 'Нет доступного пользовательского инструмента',
      tip: 'Создать пользовательский инструмент',
    },
    workflow: {
      title: 'Нет доступного инструмента рабочего процесса',
      tip: 'Публиковать рабочие процессы как инструменты в Студии',
    },
    mcp: {
      title: 'Нет доступного инструмента MCP',
      tip: 'Добавить сервер MCP',
    },
    agent: {
      title: 'Нет доступной стратегии агента',
    },
  },
  createTool: {
    title: 'Создать пользовательский инструмент',
    editAction: 'Настроить',
    editTitle: 'Редактировать пользовательский инструмент',
    name: 'Название',
    toolNamePlaceHolder: 'Введите название инструмента',
    nameForToolCall: 'Название вызова инструмента',
    nameForToolCallPlaceHolder: 'Используется для машинного распознавания, например getCurrentWeather, list_pets',
    nameForToolCallTip: 'Поддерживаются только цифры, буквы и подчеркивания.',
    description: 'Описание',
    descriptionPlaceholder: 'Краткое описание назначения инструмента, например, получить температуру для определенного местоположения.',
    schema: 'Схема',
    schemaPlaceHolder: 'Введите свою схему OpenAPI здесь',
    viewSchemaSpec: 'Посмотреть спецификацию OpenAPI-Swagger',
    importFromUrl: 'Импортировать из URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Пожалуйста, введите действительный URL',
    examples: 'Примеры',
    exampleOptions: {
      json: 'Погода (JSON)',
      yaml: 'Зоомагазин (YAML)',
      blankTemplate: 'Пустой шаблон',
    },
    availableTools: {
      title: 'Доступные инструменты',
      name: 'Название',
      description: 'Описание',
      method: 'Метод',
      path: 'Путь',
      action: 'Действия',
      test: 'Тест',
    },
    authMethod: {
      title: 'Метод авторизации',
      type: 'Тип авторизации',
      keyTooltip: 'Ключ заголовка HTTP, вы можете оставить его как "Authorization", если не знаете, что это такое, или установить его на пользовательское значение',
      types: {
        none: 'Нет',
        api_key: 'Ключ API',
        apiKeyPlaceholder: 'Название заголовка HTTP для ключа API',
        apiValuePlaceholder: 'Введите ключ API',
        api_key_header: 'Заголовок',
        queryParamPlaceholder: 'Имя параметра запроса для API-ключа',
        api_key_query: 'Параметр запроса',
      },
      key: 'Ключ',
      value: 'Значение',
      queryParam: 'Параметр запроса',
      queryParamTooltip: 'Название параметра запроса API-ключа, который нужно передать, например, "key" в "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      title: 'Тип авторизации',
      types: {
        basic: 'Базовый',
        bearer: 'Bearer',
        custom: 'Пользовательский',
      },
    },
    privacyPolicy: 'Политика конфиденциальности',
    privacyPolicyPlaceholder: 'Пожалуйста, введите политику конфиденциальности',
    toolInput: {
      title: 'Входные данные инструмента',
      name: 'Название',
      required: 'Обязательно',
      method: 'Метод',
      methodSetting: 'Настройка',
      methodSettingTip: 'Пользователь заполняет конфигурацию инструмента',
      methodParameter: 'Параметр',
      methodParameterTip: 'LLM заполняет во время вывода',
      label: 'Теги',
      labelPlaceholder: 'Выберите теги (необязательно)',
      description: 'Описание',
      descriptionPlaceholder: 'Описание значения параметра',
    },
    customDisclaimer: 'Пользовательский отказ от ответственности',
    customDisclaimerPlaceholder: 'Пожалуйста, введите пользовательский отказ от ответственности',
    confirmTitle: 'Подтвердить сохранение?',
    confirmTip: 'Приложения, использующие этот инструмент, будут затронуты',
    deleteToolConfirmTitle: 'Удалить этот инструмент?',
    deleteToolConfirmContent: 'Удаление инструмента необратимо. Пользователи больше не смогут получить доступ к вашему инструменту.',
  },
  test: {
    title: 'Тест',
    parametersValue: 'Параметры и значение',
    parameters: 'Параметры',
    value: 'Значение',
    testResult: 'Результаты теста',
    testResultPlaceholder: 'Результат теста будет отображаться здесь',
  },
  thought: {
    using: 'Использование',
    used: 'Использовано',
    requestTitle: 'Запрос к',
    responseTitle: 'Ответ от',
  },
  setBuiltInTools: {
    info: 'Информация',
    setting: 'Настройка',
    toolDescription: 'Описание инструмента',
    parameters: 'параметры',
    string: 'строка',
    number: 'число',
    required: 'Обязательно',
    infoAndSetting: 'Информация и настройки',
    file: 'файл',
  },
  noCustomTool: {
    title: 'Нет пользовательских инструментов!',
    content: 'Добавьте и управляйте своими пользовательскими инструментами здесь для создания приложений ИИ.',
    createTool: 'Создать инструмент',
  },
  noSearchRes: {
    title: 'Извините, результаты не найдены!',
    content: 'Мы не смогли найти никаких инструментов, соответствующих вашему поиску.',
    reset: 'Сбросить поиск',
  },
  builtInPromptTitle: 'Подсказка',
  toolRemoved: 'Инструмент удален',
  notAuthorized: 'Инструмент не авторизован',
  howToGet: 'Как получить',
  openInStudio: 'Открыть в Studio',
  toolNameUsageTip: 'Название вызова инструмента для рассуждений агента и подсказок',
  copyToolName: 'Копировать имя',
  noTools: 'Инструменты не найдены',
  mcp: {
    create: {
      cardTitle: 'Добавить MCP сервер (HTTP)',
      cardLink: 'Узнайте больше об интеграции MCP сервера',
    },
    noConfigured: 'Неконфигурированный сервер',
    updateTime: 'Обновлено',
    toolsCount: '{count} инструментов',
    noTools: 'Нет доступных инструментов',
    modal: {
      title: 'Добавить MCP сервер (HTTP)',
      editTitle: 'Редактировать MCP сервер (HTTP)',
      name: 'Имя и иконка',
      namePlaceholder: 'Назовите ваш MCP сервер',
      serverUrl: 'URL сервера',
      serverUrlPlaceholder: 'URL конечной точки сервера',
      serverUrlWarning: 'Обновление адреса сервера может нарушить работу приложений, которые зависят от этого сервера',
      serverIdentifier: 'Идентификатор сервера',
      serverIdentifierTip: 'Уникальный идентификатор MCP сервера в рабочем пространстве. Только строчные буквы, цифры, подчеркивания и дефисы. Максимум 24 символа.',
      serverIdentifierPlaceholder: 'Уникальный идентификатор, например, мой-сервер-mcp',
      serverIdentifierWarning: 'Сервер не будет распознан существующими приложениями после изменения ID',
      cancel: 'Отмена',
      save: 'Сохранить',
      confirm: 'Добавить и авторизовать',
    },
    delete: 'Удалить MCP сервер',
    deleteConfirmTitle: 'Вы действительно хотите удалить {mcp}?',
    operation: {
      edit: 'Редактировать',
      remove: 'Удалить',
    },
    authorize: 'Авторизовать',
    authorizing: 'Авторизация...',
    authorizingRequired: 'Требуется авторизация',
    authorizeTip: 'После авторизации инструменты будут отображены здесь.',
    update: 'Обновить',
    updating: 'Обновление',
    gettingTools: 'Получение инструментов...',
    updateTools: 'Обновление инструментов...',
    toolsEmpty: 'Инструменты не загружены',
    getTools: 'Получить инструменты',
    toolUpdateConfirmTitle: 'Обновить список инструментов',
    toolUpdateConfirmContent: 'Обновление списка инструментов может повлиять на существующие приложения. Вы хотите продолжить?',
    toolsNum: '{count} инструментов включено',
    onlyTool: '1 инструмент включен',
    identifier: 'Идентификатор сервера (Нажмите, чтобы скопировать)',
    server: {
      title: 'MCP Сервер',
      url: 'URL сервера',
      reGen: 'Хотите регенерировать URL сервера?',
      addDescription: 'Добавить описание',
      edit: 'Редактировать описание',
      modal: {
        addTitle: 'Добавить описание, чтобы включить MCP сервер',
        editTitle: 'Редактировать описание',
        description: 'Описание',
        descriptionPlaceholder: 'Объясните, что делает этот инструмент и как его должен использовать LLM',
        parameters: 'Параметры',
        parametersTip: 'Добавьте описания для каждого параметра, чтобы помочь LLM понять их назначение и ограничения.',
        parametersPlaceholder: 'Назначение и ограничения параметра',
        confirm: 'Активировать MCP сервер',
      },
      publishTip: 'Приложение не опубликовано. Пожалуйста, сначала опубликуйте приложение.',
    },
  },
}

export default translation
