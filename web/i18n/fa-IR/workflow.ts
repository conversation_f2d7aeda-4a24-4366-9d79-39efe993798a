const translation = {
  common: {
    undo: 'بازگشت',
    redo: 'پیشرفت',
    editing: 'ویرایش',
    autoSaved: 'ذخیره خودکار',
    unpublished: 'منتشر نشده',
    published: 'منتشر شده',
    publish: 'انتشار',
    update: 'به‌روزرسانی',
    run: 'اجرا',
    running: 'در حال اجرا',
    inRunMode: 'در حالت اجرا',
    inPreview: 'در پیش‌نمایش',
    inPreviewMode: 'در حالت پیش‌نمایش',
    preview: 'پیش‌نمایش',
    viewRunHistory: 'مشاهده تاریخچه اجرا',
    runHistory: 'تاریخچه اجرا',
    goBackToEdit: 'بازگشت به ویرایشگر',
    conversationLog: 'گزارش مکالمات',
    features: 'ویژگی‌ها',
    debugAndPreview: 'پیش‌نمایش',
    restart: 'راه‌اندازی مجدد',
    currentDraft: 'پیش‌نویس فعلی',
    currentDraftUnpublished: 'پیش‌نویس فعلی منتشر نشده',
    latestPublished: 'آخرین نسخه منتشر شده',
    publishedAt: 'منتشر شده',
    restore: 'بازیابی',
    runApp: 'اجرای اپلیکیشن',
    batchRunApp: 'اجرای دسته‌ای اپلیکیشن',
    accessAPIReference: 'دسترسی به مستندات API',
    embedIntoSite: 'درج در سایت',
    addTitle: 'افزودن عنوان...',
    addDescription: 'افزودن توضیحات...',
    noVar: 'هیچ متغیری',
    searchVar: 'جستجوی متغیر',
    variableNamePlaceholder: 'نام متغیر',
    setVarValuePlaceholder: 'تنظیم متغیر',
    needConnectTip: 'این مرحله به هیچ چیزی متصل نیست',
    maxTreeDepth: 'حداکثر عمق {{depth}} نود در هر شاخه',
    workflowProcess: 'فرآیند جریان کار',
    notRunning: 'هنوز در حال اجرا نیست',
    previewPlaceholder: 'محتوا را در کادر زیر وارد کنید تا اشکال‌زدایی چت‌بات را شروع کنید',
    effectVarConfirm: {
      title: 'حذف متغیر',
      content: 'متغیر در نودهای دیگر استفاده شده است. آیا همچنان می‌خواهید آن را حذف کنید؟',
    },
    insertVarTip: 'برای درج سریع کلید \'/\' را فشار دهید',
    processData: 'پردازش داده‌ها',
    input: 'ورودی',
    output: 'خروجی',
    jinjaEditorPlaceholder: 'برای درج متغیر \'/\' یا \'{\' را تایپ کنید',
    viewOnly: 'فقط مشاهده',
    showRunHistory: 'نمایش تاریخچه اجرا',
    enableJinja: 'فعال‌سازی پشتیبانی از الگوهای Jinja',
    learnMore: 'اطلاعات بیشتر',
    copy: 'کپی',
    duplicate: 'تکرار',
    pasteHere: 'چسباندن اینجا',
    pointerMode: 'حالت اشاره‌گر',
    handMode: 'حالت دست',
    model: 'مدل',
    workflowAsTool: 'جریان کار به عنوان ابزار',
    configureRequired: 'پیکربندی مورد نیاز',
    configure: 'پیکربندی',
    manageInTools: 'مدیریت در ابزارها',
    workflowAsToolTip: 'پیکربندی ابزار پس از به‌روزرسانی جریان کار مورد نیاز است.',
    viewDetailInTracingPanel: 'مشاهده جزئیات',
    syncingData: 'همگام‌سازی داده‌ها، فقط چند ثانیه',
    importDSL: 'وارد کردن DSL',
    importDSLTip: 'پیش‌نویس فعلی بر روی هم نوشته خواهد شد. قبل از وارد کردن، جریان کار را به عنوان نسخه پشتیبان صادر کنید.',
    backupCurrentDraft: 'پشتیبان‌گیری از پیش‌نویس فعلی',
    chooseDSL: 'انتخاب فایل DSL(yml)',
    overwriteAndImport: 'بازنویسی و وارد کردن',
    importFailure: 'خطا در وارد کردن',
    importSuccess: 'وارد کردن موفقیت‌آمیز',
    parallelTip: {
      click: {
        title: 'کلیک کنید',
        desc: 'اضافه کردن',
      },
      drag: {
        desc: 'برای اتصال',
        title: 'کشیدن',
      },
      depthLimit: 'حد لایه تودرتو موازی لایه های {{num}}',
      limit: 'موازی سازی به شاخه های {{num}} محدود می شود.',
    },
    disconnect: 'قطع',
    jumpToNode: 'پرش به این گره',
    parallelRun: 'اجرای موازی',
    addParallelNode: 'افزودن گره موازی',
    parallel: 'موازی',
    branch: 'شاخه',
    featuresDocLink: 'بیشتر بدانید',
    featuresDescription: 'بهبود تجربه کاربری برنامه وب',
    ImageUploadLegacyTip: 'اکنون می توانید متغیرهای نوع فایل را در فرم شروع ایجاد کنید. ما دیگر از ویژگی آپلود تصویر در آینده پشتیبانی نخواهیم کرد.',
    fileUploadTip: 'ویژگی های آپلود تصویر برای آپلود فایل ارتقا یافته است.',
    importWarning: 'احتیاط',
    importWarningDetails: 'تفاوت نسخه DSL ممکن است بر ویژگی های خاصی تأثیر بگذارد',
    openInExplore: 'باز کردن در کاوش',
    onFailure: 'در مورد شکست',
    addFailureBranch: 'افزودن برنچ Fail',
    noHistory: 'بدون تاریخچه',
    loadMore: 'بارگذاری گردش کار بیشتر',
    exportPNG: 'صادرات به فرمت PNG',
    noExist: 'هیچگونه متغیری وجود ندارد',
    exitVersions: 'نسخه‌های خروجی',
    referenceVar: 'متغیر مرجع',
    exportSVG: 'صادرات به فرمت SVG',
    exportJPEG: 'صادرات به فرمت JPEG',
    exportImage: 'تصویر را صادر کنید',
    versionHistory: 'تاریخچه نسخه',
    publishUpdate: 'به‌روزرسانی منتشر کنید',
    needEndNode: 'باید گره پایان اضافه شود',
    needAnswerNode: 'باید گره پاسخ اضافه شود',
    addBlock: 'نود اضافه کنید',
    tagBound: 'تعداد برنامه‌هایی که از این برچسب استفاده می‌کنند',
  },
  env: {
    envPanelTitle: 'متغیرهای محیطی',
    envDescription: 'متغیرهای محیطی می‌توانند برای ذخیره اطلاعات خصوصی و اعتبارنامه‌ها استفاده شوند. آنها فقط خواندنی هستند و می‌توانند در حین صادر کردن از فایل DSL جدا شوند.',
    envPanelButton: 'افزودن متغیر',
    modal: {
      title: 'افزودن متغیر محیطی',
      editTitle: 'ویرایش متغیر محیطی',
      type: 'نوع',
      name: 'نام',
      namePlaceholder: 'نام متغیر',
      value: 'مقدار',
      valuePlaceholder: 'مقدار متغیر',
      secretTip: 'برای تعریف اطلاعات حساس یا داده‌ها، با تنظیمات DSL برای جلوگیری از نشت پیکربندی شده است.',
      description: 'توضیحات',
      descriptionPlaceholder: 'متغیر را توصیف کنید',
    },
    export: {
      title: 'آیا متغیرهای محیطی مخفی را صادر کنید؟',
      checkbox: 'صادر کردن مقادیر مخفی',
      ignore: 'صادر کردن DSL',
      export: 'صادر کردن DSL با مقادیر مخفی',
    },
  },
  chatVariable: {
    panelTitle: 'متغیرهای مکالمه',
    panelDescription: 'متغیرهای مکالمه برای ذخیره اطلاعات تعاملی که LLM نیاز به یادآوری دارد استفاده می‌شوند، از جمله تاریخچه مکالمه، فایل‌های آپلود شده و ترجیحات کاربر. آنها قابل خواندن و نوشتن هستند.',
    docLink: 'برای اطلاعات بیشتر به مستندات ما مراجعه کنید.',
    button: 'افزودن متغیر',
    modal: {
      title: 'افزودن متغیر مکالمه',
      editTitle: 'ویرایش متغیر مکالمه',
      name: 'نام',
      namePlaceholder: 'نام متغیر',
      type: 'نوع',
      value: 'مقدار پیش‌فرض',
      valuePlaceholder: 'مقدار پیش‌فرض، برای عدم تنظیم خالی بگذارید',
      description: 'توضیحات',
      descriptionPlaceholder: 'متغیر را توصیف کنید',
      editInJSON: 'ویرایش در JSON',
      oneByOne: 'افزودن یکی یکی',
      editInForm: 'ویرایش در فرم',
      arrayValue: 'مقدار',
      addArrayValue: 'افزودن مقدار',
      objectKey: 'کلید',
      objectType: 'نوع',
      objectValue: 'مقدار پیش‌فرض',
    },
    storedContent: 'محتوای ذخیره شده',
    updatedAt: 'به‌روزرسانی شده در ',
  },
  changeHistory: {
    title: 'تاریخچه تغییرات',
    placeholder: 'هنوز تغییری ایجاد نکردید',
    clearHistory: 'پاک کردن تاریخچه',
    hint: 'راهنما',
    hintText: 'عملیات ویرایش شما در تاریخچه تغییرات پیگیری می‌شود که برای مدت این جلسه بر روی دستگاه شما ذخیره می‌شود. این تاریخچه هنگام خروج از ویرایشگر پاک خواهد شد.',
    stepBackward_one: '{{count}} قدم به عقب',
    stepBackward_other: '{{count}} قدم به عقب',
    stepForward_one: '{{count}} قدم به جلو',
    stepForward_other: '{{count}} قدم به جلو',
    sessionStart: 'شروع جلسه',
    currentState: 'وضعیت کنونی',
    noteAdd: 'یادداشت اضافه شده است',
    noteChange: 'یادداشت تغییر کرده است',
    noteDelete: 'یادداشت حذف شده است',
    nodeDelete: 'نود حذف شد',
    nodeAdd: 'نود اضافه شد',
    nodeDragStop: 'گره منتقل شد',
    edgeDelete: 'گره قطع شده است',
    nodeResize: 'اندازه نود تغییر یافته است',
    nodePaste: 'نود پیست شده است',
    nodeTitleChange: 'عنوان نود تغییر کرد',
    nodeConnect: 'گره متصل است',
    nodeDescriptionChange: 'شرح نود تغییر کرد',
    nodeChange: 'نود تغییر کرد',
  },
  errorMsg: {
    fieldRequired: '{{field}} الزامی است',
    authRequired: 'احراز هویت ضروری است',
    invalidJson: '{{field}} JSON معتبر نیست',
    fields: {
      variable: 'نام متغیر',
      variableValue: 'مقدار متغیر',
      code: 'کد',
      model: 'مدل',
      rerankModel: 'مدل مجدد رتبه‌بندی',
      visionVariable: 'متغیر بینایی',
    },
    invalidVariable: 'متغیر نامعتبر',
    rerankModelRequired: 'قبل از روشن کردن Rerank Model، لطفا تأیید کنید که مدل با موفقیت در تنظیمات پیکربندی شده است.',
    noValidTool: '{{field}} هیچ ابزار معتبری انتخاب نشده است',
    toolParameterRequired: '{{field}}: پارامتر [{{param}}] مورد نیاز است',
  },
  singleRun: {
    testRun: 'اجرای آزمایشی',
    startRun: 'شروع اجرا',
    running: 'در حال اجرا',
    testRunIteration: 'تکرار اجرای آزمایشی',
    back: 'بازگشت',
    iteration: 'تکرار',
    loop: 'حلقه',
  },
  tabs: {
    'tools': 'ابزارها',
    'allTool': 'همه',
    'builtInTool': 'درون‌ساخت',
    'customTool': 'سفارشی',
    'workflowTool': 'جریان کار',
    'question-understand': 'درک سوال',
    'logic': 'منطق',
    'transform': 'تبدیل',
    'utilities': 'ابزارهای کاربردی',
    'noResult': 'نتیجه‌ای پیدا نشد',
    'searchTool': 'ابزار جستجو',
    'plugin': 'افزونه',
    'agent': 'استراتژی نمایندگی',
    'blocks': 'گره‌ها',
    'searchBlock': 'گره جستجو',
    'addAll': 'همه را اضافه کنید',
    'allAdded': 'همه اضافه شده است',
  },
  blocks: {
    'start': 'شروع',
    'end': 'پایان',
    'answer': 'پاسخ',
    'llm': 'مدل زبان بزرگ',
    'knowledge-retrieval': 'استخراج دانش',
    'question-classifier': 'دسته‌بندی سوالات',
    'if-else': 'IF/ELSE',
    'code': 'کد',
    'template-transform': 'الگو',
    'http-request': 'درخواست HTTP',
    'variable-assigner': 'تخصیص‌دهنده متغیر',
    'variable-aggregator': 'تجمع‌دهنده متغیر',
    'assigner': 'تخصیص‌دهنده متغیر',
    'iteration-start': 'شروع تکرار',
    'iteration': 'تکرار',
    'parameter-extractor': 'استخراج‌کننده پارامتر',
    'list-operator': 'عملگر لیست',
    'document-extractor': 'استخراج کننده سند',
    'agent': 'عامل',
    'loop-start': 'شروع حلقه',
    'loop-end': 'خروج از حلقه',
    'loop': 'حلقه',
  },
  blocksAbout: {
    'start': 'پارامترهای اولیه برای راه‌اندازی جریان کار را تعریف کنید',
    'end': 'پایان و نوع نتیجه یک جریان کار را تعریف کنید',
    'answer': 'محتوای پاسخ مکالمه چت را تعریف کنید',
    'llm': 'استفاده از مدل‌های زبان بزرگ برای پاسخ به سوالات یا پردازش زبان طبیعی',
    'knowledge-retrieval': 'اجازه می‌دهد تا محتوای متنی مرتبط با سوالات کاربر از دانش استخراج شود',
    'question-classifier': 'شرایط دسته‌بندی سوالات کاربر را تعریف کنید، مدل زبان بزرگ می‌تواند بر اساس توضیحات دسته‌بندی، نحوه پیشرفت مکالمه را تعریف کند',
    'if-else': 'اجازه می‌دهد تا جریان کار به دو شاخه بر اساس شرایط if/else تقسیم شود',
    'code': 'اجرای یک قطعه کد Python یا NodeJS برای پیاده‌سازی منطق سفارشی',
    'template-transform': 'تبدیل داده‌ها به رشته با استفاده از سینتاکس الگوهای Jinja',
    'http-request': 'اجازه می‌دهد تا درخواست‌های سرور از طریق پروتکل HTTP ارسال شوند',
    'variable-assigner': 'تجمع متغیرهای چند شاخه‌ای به یک متغیر واحد برای پیکربندی یکپارچه نودهای پایین‌دستی.',
    'assigner': 'گره تخصیص متغیر برای اختصاص مقادیر به متغیرهای قابل نوشتن (مانند متغیرهای مکالمه) استفاده می‌شود.',
    'variable-aggregator': 'تجمع متغیرهای چند شاخه‌ای به یک متغیر واحد برای پیکربندی یکپارچه نودهای پایین‌دستی.',
    'iteration': 'اجرای چندین مرحله روی یک شیء لیست تا همه نتایج خروجی داده شوند.',
    'parameter-extractor': 'استفاده از مدل زبان بزرگ برای استخراج پارامترهای ساختاری از زبان طبیعی برای فراخوانی ابزارها یا درخواست‌های HTTP.',
    'list-operator': 'برای فیلتر کردن یا مرتب سازی محتوای آرایه استفاده می شود.',
    'document-extractor': 'برای تجزیه اسناد آپلود شده به محتوای متنی استفاده می شود که به راحتی توسط LLM قابل درک است.',
    'agent': 'فراخوانی مدل های زبان بزرگ برای پاسخ به سوالات یا پردازش زبان طبیعی',
    'loop-end': 'معادل "شکستن". این گره هیچ مورد پیکربندی ندارد. هنگامی که بدنه حلقه به این گره می‌رسد، حلقه متوقف می‌شود.',
    'loop': 'یک حلقه منطقی را اجرا کنید تا زمانی که شرایط خاتمه برآورده شود یا حداکثر تعداد حلقه به پایان برسد.',
  },
  operator: {
    zoomIn: 'بزرگ‌نمایی',
    zoomOut: 'کوچک‌نمایی',
    zoomTo50: 'بزرگ‌نمایی به 50%',
    zoomTo100: 'بزرگ‌نمایی به 100%',
    zoomToFit: 'تناسب با اندازه',
  },
  panel: {
    userInputField: 'فیلد ورودی کاربر',
    helpLink: 'لینک کمک',
    about: 'درباره',
    createdBy: 'ساخته شده توسط',
    nextStep: 'مرحله بعدی',
    runThisStep: 'اجرا کردن این مرحله',
    checklist: 'چک‌لیست',
    checklistTip: 'اطمینان حاصل کنید که همه مسائل قبل از انتشار حل شده‌اند',
    checklistResolved: 'تمام مسائل حل شده‌اند',
    change: 'تغییر',
    optional: '(اختیاری)',
    moveToThisNode: 'به این گره بروید',
    selectNextStep: 'گام بعدی را انتخاب کنید',
    changeBlock: 'تغییر گره',
    organizeBlocks: 'گره‌ها را سازماندهی کنید',
    addNextStep: 'مرحله بعدی را به این فرآیند اضافه کنید',
    minimize: 'خروج از حالت تمام صفحه',
    maximize: 'بیشینه‌سازی بوم',
  },
  nodes: {
    common: {
      outputVars: 'متغیرهای خروجی',
      insertVarTip: 'درج متغیر',
      memory: {
        memory: 'حافظه',
        memoryTip: 'تنظیمات حافظه چت',
        windowSize: 'اندازه پنجره',
        conversationRoleName: 'نام نقش مکالمه',
        user: 'پیشوند کاربر',
        assistant: 'پیشوند دستیار',
      },
      memories: {
        title: 'حافظه‌ها',
        tip: 'حافظه چت',
        builtIn: 'درون‌ساخت',
      },
      errorHandle: {
        none: {
          title: 'هیچ کدام',
          desc: 'اگر یک استثنا رخ دهد و مدیریت نشود، گره از کار می افتد',
        },
        defaultValue: {
          title: 'مقدار پیش فرض',
          output: 'مقدار پیش فرض خروجی',
          tip: 'در صورت خطا، به زیر مقدار برمی گردد.',
          inLog: 'استثنای گره، خروجی بر اساس مقادیر پیش فرض.',
          desc: 'هنگامی که خطایی رخ می دهد، یک محتوای خروجی ثابت را مشخص کنید.',
        },
        failBranch: {
          title: 'شاخه Fail',
          desc: 'هنگامی که خطایی رخ می دهد، شاخه استثنا را اجرا می کند',
          customize: 'برای سفارشی کردن منطق برنچ fail به بوم بروید.',
          inLog: 'Node exception، به طور خودکار شاخه fail را اجرا می کند. خروجی گره یک نوع خطا و پیام خطا را برمی گرداند و آنها را به پایین دست ارسال می کند.',
          customizeTip: 'هنگامی که شاخه fail فعال می شود، استثنائات پرتاب شده توسط گره ها فرآیند را خاتمه نمی دهند. در عوض، به طور خودکار شاخه شکست از پیش تعریف شده را اجرا می کند و به شما امکان می دهد پیام های خطا، گزارش ها، اصلاحات یا پرش از اقدامات را به طور انعطاف پذیر ارائه دهید.',
        },
        partialSucceeded: {
          tip: 'گره های {{num}} در این فرآیند وجود دارند که به طور غیرعادی اجرا می شوند، لطفا برای بررسی سیاههها به ردیابی بروید.',
        },
        title: 'مدیریت خطا',
        tip: 'استراتژی مدیریت استثنا، زمانی که یک گره با یک استثنا مواجه می شود، فعال می شود.',
      },
      retry: {
        times: 'بار',
        retryInterval: 'فاصله تلاش مجدد',
        retryOnFailure: 'در مورد شکست دوباره امتحان کنید',
        ms: 'خانم',
        retry: 'دوباره',
        retries: '{{عدد}} تلاش های مجدد',
        maxRetries: 'حداکثر تلاش مجدد',
        retrying: 'تلاش مجدد...',
        retryFailed: 'تلاش مجدد ناموفق بود',
        retryTimes: '{{times}} بار در صورت شکست دوباره امتحان کنید',
        retrySuccessful: 'امتحان مجدد با موفقیت انجام دهید',
        retryFailedTimes: '{{بار}} تلاش های مجدد ناموفق بود',
      },
      typeSwitch: {
        input: 'مقدار ورودی',
        variable: 'از متغیر استفاده کن',
      },
    },
    start: {
      required: 'الزامی',
      inputField: 'فیلد ورودی',
      builtInVar: 'متغیرهای درون‌ساخت',
      outputVars: {
        query: 'ورودی کاربر',
        memories: {
          des: 'تاریخچه مکالمات',
          type: 'نوع پیام',
          content: 'محتوای پیام',
        },
        files: 'لیست فایل‌ها',
      },
      noVarTip: 'ورودی‌هایی را که می‌توان در جریان کار استفاده کرد، تنظیم کنید',
    },
    end: {
      outputs: 'خروجی‌ها',
      output: {
        type: 'نوع خروجی',
        variable: 'متغیر خروجی',
      },
      type: {
        'none': 'هیچ',
        'plain-text': 'متن ساده',
        'structured': 'ساختاری',
      },
    },
    answer: {
      answer: 'پاسخ',
      outputVars: 'متغیرهای خروجی',
    },
    llm: {
      model: 'مدل',
      variables: 'متغیرها',
      context: 'متن',
      contextTooltip: 'می‌توانید دانش را به عنوان متن وارد کنید',
      notSetContextInPromptTip: 'برای فعال کردن ویژگی متن، لطفاً متغیر متن را در PROMPT پر کنید.',
      prompt: 'پیشنهاد',
      roleDescription: {
        system: 'دستورات سطح بالا برای مکالمه را ارائه دهید',
        user: 'دستورات، پرسش‌ها، یا هر ورودی متنی را به مدل ارائه دهید',
        assistant: 'پاسخ‌های مدل بر اساس پیام‌های کاربر',
      },
      addMessage: 'افزودن پیام',
      vision: 'بینایی',
      files: 'فایل‌ها',
      resolution: {
        name: 'وضوح',
        high: 'بالا',
        low: 'پایین',
      },
      outputVars: {
        output: 'تولید محتوا',
        usage: 'اطلاعات استفاده از مدل',
      },
      singleRun: {
        variable: 'متغیر',
      },
      sysQueryInUser: 'sys.query در پیام کاربر ضروری است',
      jsonSchema: {
        warningTips: {
          saveSchema: 'لطفاً قبل از ذخیره‌سازی طرح، ویرایش فیلد فعلی را کامل کنید.',
        },
        import: 'واردات از JSON',
        addField: 'فیلد اضافه کنید',
        required: 'ضروری',
        generatedResult: 'نتیجه تولید شده',
        generate: 'تولید کنید',
        doc: 'بیشتر درباره خروجی ساختار یافته بیاموزید',
        generating: 'تولید طرح‌واره JSON...',
        instruction: 'دستورالعمل',
        back: 'عقب',
        resetDefaults: 'تنظیم مجدد',
        showAdvancedOptions: 'نمایش گزینه‌های پیشرفته',
        regenerate: 'تولید مجدد',
        apply: 'اعمال کنید',
        title: 'الگوی خروجی ساختاری',
        promptTooltip: 'تبدیل توصیف متنی به یک ساختار استاندارد شده JSON Schema.',
        stringValidations: 'اعتبارسنجی رشته',
        resultTip: 'این نتیجه تولید شده است. اگر راضی نیستید، می‌توانید به عقب برگردید و درخواست خود را ویرایش کنید.',
        descriptionPlaceholder: 'توضیحات را اضافه کنید',
        addChildField: 'افزودن فیلد فرزند',
        generateJsonSchema: 'ایجاد اسکیما JSON',
        promptPlaceholder: 'اسکیمای JSON خود را توصیف کنید...',
        fieldNamePlaceholder: 'نام میدان',
        generationTip: 'شما می‌توانید از زبان طبیعی برای ایجاد سریع یک طرح‌واره JSON استفاده کنید.',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'متغیر جستجو',
      knowledge: 'دانش',
      outputVars: {
        output: 'داده‌های تقسیم‌بندی شده بازیابی',
        content: 'محتوای تقسیم‌بندی شده',
        title: 'عنوان تقسیم‌بندی شده',
        icon: 'آیکون تقسیم‌بندی شده',
        url: 'URL تقسیم‌بندی شده',
        metadata: 'سایر متاداده‌ها',
      },
      metadata: {
        options: {
          disabled: {
            title: 'متعادل',
            subTitle: 'عدم فعال‌سازی فیلترهای متاداده',
          },
          automatic: {
            title: 'خودکار',
            desc: 'شرایط فیلتر متاداده را بر اساس متغیر جستجو به صورت خودکار تولید کنید',
            subTitle: 'شرایط فیلتر متادیتا را به طور خودکار بر اساس پرسش کاربر تولید کنید',
          },
          manual: {
            subTitle: 'به‌صورت دستی شرایط فیلتر کردن متادیتا را اضافه کنید',
            title: 'دستوری',
          },
        },
        panel: {
          add: 'شرط اضافه کنید',
          placeholder: 'مقدار را وارد کنید',
          datePlaceholder: 'زمانی را انتخاب کنید...',
          search: 'جستجوی متا داده',
          select: 'متغیر را انتخاب کنید...',
          title: 'شرایط فیلتر متادیتا',
          conditions: 'شرایط',
        },
        title: 'فیلتر کردن فراداده',
      },
    },
    http: {
      inputVars: 'متغیرهای ورودی',
      api: 'API',
      apiPlaceholder: 'URL را وارد کنید، برای درج متغیر \' / \' را تایپ کنید',
      notStartWithHttp: 'API باید با http:// یا https:// شروع شود',
      key: 'کلید',
      value: 'مقدار',
      bulkEdit: 'ویرایش دسته‌ای',
      keyValueEdit: 'ویرایش کلید-مقدار',
      headers: 'هدرها',
      params: 'پارامترها',
      body: 'بدن',
      outputVars: {
        body: 'محتوای پاسخ',
        statusCode: 'کد وضعیت پاسخ',
        headers: 'فهرست هدر پاسخ JSON',
        files: 'لیست فایل‌ها',
      },
      authorization: {
        'authorization': 'احراز هویت',
        'authorizationType': 'نوع احراز هویت',
        'no-auth': 'هیچ',
        'api-key': 'کلید API',
        'auth-type': 'نوع احراز هویت',
        'basic': 'پایه',
        'bearer': 'دارنده',
        'custom': 'سفارشی',
        'api-key-title': 'کلید API',
        'header': 'هدر',
      },
      insertVarPlaceholder: 'برای درج متغیر \'/\' را تایپ کنید',
      timeout: {
        title: 'زمان‌توقف',
        connectLabel: 'زمان‌توقف اتصال',
        connectPlaceholder: 'زمان‌توقف اتصال را به ثانیه وارد کنید',
        readLabel: 'زمان‌توقف خواندن',
        readPlaceholder: 'زمان‌توقف خواندن را به ثانیه وارد کنید',
        writeLabel: 'زمان‌توقف نوشتن',
        writePlaceholder: 'زمان‌توقف نوشتن را به ثانیه وارد کنید',
      },
      binaryFileVariable: 'متغیر فایل باینری',
      type: 'نوع',
      extractListPlaceholder: 'فهرست آیتم لیست را وارد کنید، متغیر درج \'/\' را تایپ کنید',
      curl: {
        title: 'وارد کردن از cURL',
        placeholder: 'رشته cURL را اینجا بچسبانید',
      },
      verifySSL: {
        title: 'گواهی SSL را تأیید کنید',
        warningTooltip: 'غیرفعال کردن تأیید SSL برای محیط‌های تولید توصیه نمی‌شود. این فقط باید در توسعه یا آزمایش استفاده شود، زیرا این کار اتصال را در معرض تهدیدات امنیتی مانند حملات میانی قرار می‌دهد.',
      },
    },
    code: {
      inputVars: 'متغیرهای ورودی',
      outputVars: 'متغیرهای خروجی',
      advancedDependencies: 'وابستگی‌های پیشرفته',
      advancedDependenciesTip: 'برخی وابستگی‌های پیش‌بارگذاری شده که زمان بیشتری برای مصرف نیاز دارند یا به طور پیش‌فرض در اینجا موجود نیستند، اضافه کنید',
      searchDependencies: 'جستجوی وابستگی‌ها',
      syncFunctionSignature: 'امضای تابع همگام‌سازی را به کد متصل کنید',
    },
    templateTransform: {
      inputVars: 'متغیرهای ورودی',
      code: 'کد',
      codeSupportTip: 'فقط Jinja2 را پشتیبانی می‌کند',
      outputVars: {
        output: 'محتوای تبدیل‌شده',
      },
    },
    ifElse: {
      if: 'اگر',
      else: 'در غیر این صورت',
      elseDescription: 'برای تعریف منطق که باید زمانی که شرط if برآورده نشود، اجرا شود.',
      and: 'و',
      or: 'یا',
      operator: 'عملگر',
      notSetVariable: 'لطفاً ابتدا متغیر را تنظیم کنید',
      comparisonOperator: {
        'contains': 'شامل',
        'not contains': 'شامل نمی‌شود',
        'start with': 'شروع با',
        'end with': 'پایان با',
        'is': 'است',
        'is not': 'نیست',
        'empty': 'خالی است',
        'not empty': 'خالی نیست',
        'null': 'خالی',
        'not null': 'خالی نیست',
        'regex match': 'مسابقه regex',
        'in': 'در',
        'not exists': 'وجود ندارد',
        'all of': 'همه از',
        'not in': 'نه در',
        'exists': 'موجود',
        'after': 'بعد از',
        'before': 'قبل از',
      },
      enterValue: 'مقدار را وارد کنید',
      addCondition: 'افزودن شرط',
      conditionNotSetup: 'شرط تنظیم نشده است',
      selectVariable: 'متغیر را انتخاب کنید...',
      optionName: {
        video: 'ویدئو',
        doc: 'توضیحات',
        localUpload: 'آپلود محلی',
        audio: 'صوتی',
        url: 'آدرس',
        image: 'تصویر',
      },
      select: 'انتخاب',
      addSubVariable: 'متغیر فرعی',
      condition: 'شرط',
    },
    variableAssigner: {
      title: 'تخصیص متغیرها',
      outputType: 'نوع خروجی',
      varNotSet: 'متغیر تنظیم نشده است',
      noVarTip: 'متغیرهایی را که باید اختصاص داده شوند اضافه کنید',
      type: {
        string: 'رشته',
        number: 'عدد',
        object: 'شیء',
        array: 'آرایه',
      },
      aggregationGroup: 'گروه تجمع',
      aggregationGroupTip: 'فعال کردن این ویژگی اجازه می‌دهد تا تجمع‌کننده متغیرها چندین مجموعه متغیر را تجمیع کند.',
      addGroup: 'افزودن گروه',
      outputVars: {
        varDescribe: '{{groupName}} خروجی',
      },
      setAssignVariable: 'تعیین متغیر تخصیص یافته',
    },
    assigner: {
      'assignedVariable': 'متغیر اختصاص داده شده',
      'writeMode': 'حالت نوشتن',
      'writeModeTip': 'وقتی متغیر اختصاص داده شده یک آرایه است، حالت افزودن به انتها اضافه می‌کند.',
      'over-write': 'بازنویسی',
      'append': 'افزودن',
      'plus': 'به علاوه',
      'clear': 'پاک کردن',
      'setVariable': 'تنظیم متغیر',
      'variable': 'متغیر',
      'operations': {
        'clear': 'روشن',
        'over-write': 'بازنویسی',
        'set': 'مجموعه',
        '*=': '*=',
        'overwrite': 'بازنویسی',
        '+=': '+=',
        'title': 'عملیات',
        'extend': 'گسترش',
        '-=': '-=',
        'append': 'الحاق',
        '/=': '/=',
        'remove-first': 'حذف اول',
        'remove-last': 'آخرین را حذف کنید',
      },
      'noVarTip': 'برای افزودن متغیرها روی دکمه "+" کلیک کنید',
      'selectAssignedVariable': 'متغیر اختصاص داده شده را انتخاب کنید...',
      'noAssignedVars': 'هیچ متغیر اختصاص داده شده در دسترس نیست',
      'setParameter': 'پارامتر را تنظیم کنید...',
      'assignedVarsDescription': 'متغیرهای اختصاص داده شده باید متغیرهای قابل نوشتن مانند متغیرهای مکالمه باشند.',
      'variables': 'متغیرهای',
      'varNotSet': 'متغیر NOT Set',
    },
    tool: {
      inputVars: 'متغیرهای ورودی',
      outputVars: {
        text: 'محتوای تولید شده توسط ابزار',
        files: {
          title: 'فایل‌های تولید شده توسط ابزار',
          type: 'نوع پشتیبانی. در حال حاضر فقط تصاویر پشتیبانی می‌شود',
          transfer_method: 'روش انتقال. مقدار آن remote_url یا local_file است',
          url: 'URL تصویر',
          upload_file_id: 'شناسه فایل آپلود شده',
        },
        json: 'json تولید شده توسط ابزار',
      },
      authorize: 'مجوز دادن',
      settings: 'تنظیمات',
      insertPlaceholder2: 'متغیر را وارد کنید',
      insertPlaceholder1: 'نوع کنید یا فشار دهید',
    },
    questionClassifiers: {
      model: 'مدل',
      inputVars: 'متغیرهای ورودی',
      outputVars: {
        className: 'نام کلاس',
        usage: 'اطلاعات استفاده از مدل',
      },
      class: 'کلاس',
      classNamePlaceholder: 'نام کلاس خود را بنویسید',
      advancedSetting: 'تنظیمات پیشرفته',
      topicName: 'نام موضوع',
      topicPlaceholder: 'نام موضوع خود را بنویسید',
      addClass: 'افزودن کلاس',
      instruction: 'دستورالعمل',
      instructionTip: 'دستورالعمل‌های اضافی را برای کمک به دسته‌بند سوالات برای درک بهتر نحوه دسته‌بندی سوالات وارد کنید.',
      instructionPlaceholder: 'دستورالعمل خود را بنویسید',
    },
    parameterExtractor: {
      inputVar: 'متغیر ورودی',
      outputVars: {
        isSuccess: 'موفقیت‌آمیز است. در صورت موفقیت مقدار 1 و در صورت شکست مقدار 0 است.',
        errorReason: 'دلیل خطا',
        usage: 'اطلاعات استفاده از مدل',
      },
      extractParameters: 'استخراج پارامترها',
      importFromTool: 'وارد کردن از ابزارها',
      addExtractParameter: 'افزودن پارامتر استخراج شده',
      addExtractParameterContent: {
        name: 'نام',
        namePlaceholder: 'نام پارامتر استخراج شده',
        type: 'نوع',
        typePlaceholder: 'نوع پارامتر استخراج شده',
        description: 'توضیحات',
        descriptionPlaceholder: 'توضیحات پارامتر استخراج شده',
        required: 'الزامی',
        requiredContent: 'الزامی فقط به عنوان مرجع برای استنتاج مدل استفاده می‌شود و برای اعتبارسنجی اجباری خروجی پارامتر نیست.',
      },
      extractParametersNotSet: 'پارامترهای استخراج شده تنظیم نشده‌اند',
      instruction: 'دستورالعمل',
      instructionTip: 'دستورالعمل‌های اضافی را برای کمک به استخراج‌کننده پارامتر برای درک نحوه استخراج پارامترها وارد کنید.',
      advancedSetting: 'تنظیمات پیشرفته',
      reasoningMode: 'حالت استدلال',
      reasoningModeTip: 'می‌توانید حالت استدلال مناسب را بر اساس توانایی مدل برای پاسخ به دستورات برای فراخوانی عملکردها یا پیشنهادات انتخاب کنید.',
    },
    iteration: {
      deleteTitle: 'حذف نود تکرار؟',
      deleteDesc: 'حذف نود تکرار باعث حذف تمام نودهای فرزند خواهد شد',
      input: 'ورودی',
      output: 'متغیرهای خروجی',
      iteration_one: '{{count}} تکرار',
      iteration_other: '{{count}} تکرارها',
      currentIteration: 'تکرار فعلی',
      ErrorMethod: {
        continueOnError: 'ادامه در خطا',
        operationTerminated: 'فسخ',
        removeAbnormalOutput: 'حذف خروجی غیرطبیعی',
      },
      error_one: '{{تعداد}} خطا',
      error_other: '{{تعداد}} خطاهای',
      parallelMode: 'حالت موازی',
      errorResponseMethod: 'روش پاسخ به خطا',
      parallelModeEnableTitle: 'حالت موازی فعال است',
      parallelModeUpper: 'حالت موازی',
      comma: ',',
      parallelModeEnableDesc: 'در حالت موازی، وظایف درون تکرارها از اجرای موازی پشتیبانی می کنند. می توانید این را در پانل ویژگی ها در سمت راست پیکربندی کنید.',
      MaxParallelismTitle: 'حداکثر موازی سازی',
      parallelPanelDesc: 'در حالت موازی، وظایف در تکرار از اجرای موازی پشتیبانی می کنند.',
      MaxParallelismDesc: 'حداکثر موازی سازی برای کنترل تعداد وظایف اجرا شده به طور همزمان در یک تکرار واحد استفاده می شود.',
      answerNodeWarningDesc: 'هشدار حالت موازی: گره های پاسخ، تکالیف متغیر مکالمه و عملیات خواندن/نوشتن مداوم در تکرارها ممکن است باعث استثنائات شود.',
    },
    note: {
      addNote: 'افزودن یادداشت',
      editor: {
        placeholder: 'یادداشت خود را بنویسید...',
        small: 'کوچک',
        medium: 'متوسط',
        large: 'بزرگ',
        bold: 'پررنگ',
        italic: 'ایتالیک',
        strikethrough: 'خط‌خورده',
        link: 'لینک',
        openLink: 'باز کردن',
        unlink: 'حذف لینک',
        enterUrl: 'URL را وارد کنید...',
        invalidUrl: 'URL نامعتبر',
        bulletList: 'فهرست گلوله‌ای',
        showAuthor: 'نمایش نویسنده',
      },
    },
    docExtractor: {
      outputVars: {
        text: 'متن استخراج شده',
      },
      inputVar: 'متغیر ورودی',
      learnMore: 'بیشتر بدانید',
      supportFileTypes: 'انواع فایل های پشتیبانی: {{types}}.',
    },
    listFilter: {
      outputVars: {
        result: 'نتیجه فیلتر',
        first_record: 'اولین رکورد',
        last_record: 'آخرین رکورد',
      },
      limit: 'بالا N',
      inputVar: 'متغیر ورودی',
      filterCondition: 'وضعیت فیلتر',
      orderBy: 'سفارش بر اساس',
      filterConditionKey: 'کلید وضعیت فیلتر',
      desc: 'نزولی',
      filterConditionComparisonOperator: 'عملگر مقایسه شرایط فیلتر',
      filterConditionComparisonValue: 'مقدار شرایط فیلتر',
      selectVariableKeyPlaceholder: 'کلید متغیر فرعی را انتخاب کنید',
      asc: 'صعودی',
      extractsCondition: 'مورد N را استخراج کنید',
    },
    agent: {
      strategy: {
        searchPlaceholder: 'جست وجو در استراتژی های عاملی',
        tooltip: 'استراتژی های مختلف عامل تعیین می کنند که سیستم چگونه فراخوانی های ابزار چند مرحله ای را برنامه ریزی و اجرا می کند.',
        label: 'استراتژی عامل',
        configureTip: 'لطفا استراتژی عامل را پیکربندی کنید.',
        selectTip: 'استراتژی عامل را انتخاب کنید',
        configureTipDesc: 'پس از پیکربندی استراتژی عامل، این گره به طور خودکار پیکربندی های باقیمانده را بارگیری می کند. این استراتژی بر مکانیسم استدلال ابزار چند مرحله ای تأثیر خواهد گذاشت.',
        shortLabel: 'استراتژی',
      },
      pluginInstaller: {
        installing: 'نصب',
        install: 'نصب',
      },
      modelNotInMarketplace: {
        manageInPlugins: 'مدیریت در پلاگین ها',
        title: 'مدل نصب نشده است',
        desc: 'این مدل از مخزن Local یا GitHub نصب شده است. لطفا پس از نصب استفاده کنید.',
      },
      modelNotSupport: {
        desc: 'نسخه افزونه نصب شده این مدل را ارائه نمی دهد.',
        title: 'مدل پشتیبانی نشده',
        descForVersionSwitch: 'نسخه افزونه نصب شده این مدل را ارائه نمی دهد. برای تغییر نسخه کلیک کنید.',
      },
      modelSelectorTooltips: {
        deprecated: 'این مدل منسوخ شده است',
      },
      outputVars: {
        files: {
          transfer_method: 'روش انتقال. ارزش remote_url یا local_file',
          upload_file_id: 'شناسه فایل را آپلود کنید',
          title: 'فایل های تولید شده توسط عامل',
          url: 'آدرس اینترنتی تصویر',
          type: 'نوع پشتیبانی. اکنون فقط از تصویر پشتیبانی می کند',
        },
        text: 'محتوای تولید شده توسط عامل',
        json: 'عامل JSON را تولید کرد',
      },
      checkList: {
        strategyNotSelected: 'استراتژی انتخاب نشده است',
      },
      installPlugin: {
        changelog: 'گزارش تغییر',
        install: 'نصب',
        cancel: 'لغو',
        title: 'افزونه را نصب کنید',
        desc: 'در مورد نصب افزونه زیر',
      },
      pluginNotFoundDesc: 'این پلاگین از GitHub نصب شده است. لطفا برای نصب مجدد به پلاگین ها بروید',
      linkToPlugin: 'پیوند به پلاگین ها',
      toolbox: 'جعبه ابزار',
      maxIterations: 'حداکثر تکرارها',
      strategyNotSet: 'استراتژی عامل تنظیم نشده است',
      strategyNotInstallTooltip: '{{strategy}} نصب نشده است',
      modelNotSelected: 'مدل انتخاب نشده است',
      toolNotInstallTooltip: '{{ابزار}} نصب نشده است',
      tools: 'ابزار',
      learnMore: 'بیشتر بدانید',
      pluginNotInstalledDesc: 'این پلاگین از GitHub نصب شده است. لطفا برای نصب مجدد به پلاگین ها بروید',
      unsupportedStrategy: 'استراتژی پشتیبانی نشده',
      modelNotInstallTooltip: 'این مدل نصب نشده است',
      notAuthorized: 'مجاز نیست',
      toolNotAuthorizedTooltip: '{{ابزار}} مجاز نیست',
      configureModel: 'پیکربندی مدل',
      pluginNotInstalled: 'این افزونه نصب نشده است',
      strategyNotFoundDesc: 'نسخه افزونه نصب شده این استراتژی را ارائه نمی دهد.',
      strategyNotFoundDescAndSwitchVersion: 'نسخه افزونه نصب شده این استراتژی را ارائه نمی دهد. برای تغییر نسخه کلیک کنید.',
      model: 'مدل',
      parameterSchema: 'طرح پارامتر',
      clickToViewParameterSchema: 'برای مشاهده طرح پارامتر کلیک کنید',
    },
    loop: {
      ErrorMethod: {
        removeAbnormalOutput: 'خروجی غیرعادی را حذف کنید',
        operationTerminated: 'منحل شد',
        continueOnError: 'ادامه در صورت بروز خطا',
      },
      variableName: 'نام متغیر',
      error_other: '{{count}} خطا',
      loopNode: 'گره حلقه',
      exitConditionTip: 'یک گره حلقه به حداقل یک شرط خروج نیاز دارد.',
      inputMode: 'حالت ورودی',
      loop_other: '{{count}} حلقه',
      error_one: '{{count}} خطا',
      comma: ',',
      loopVariables: 'متغیرهای حلقه',
      setLoopVariables: 'متغیرها را در محدوده حلقه تنظیم کنید',
      loop_one: '{{count}} حلقه',
      deleteTitle: 'حذف گره حلقه؟',
      input: 'ورودی',
      finalLoopVariables: 'متغیرهای نهایی حلقه',
      output: 'متغیر خروجی',
      totalLoopCount: 'تعداد کل حلقه: {{count}}',
      currentLoop: 'حلقه جاری',
      initialLoopVariables: 'متغیرهای حلقه اولیه',
      errorResponseMethod: 'روش پاسخ خطا',
      loopMaxCountError: 'لطفاً یک تعداد حداکثر حلقه معتبر وارد کنید که در بازه‌ی ۱ تا {{maxCount}} باشد.',
      deleteDesc: 'حذف نود حلقه همه نودهای فرزند را حذف خواهد کرد',
      loopMaxCount: 'حداکثر تعداد حلقه',
      currentLoopCount: 'تعداد حلقه‌های فعلی: {{count}}',
      breakCondition: 'شرط خاتمه حلقه',
      breakConditionTip: 'فقط متغیرهای داخل حلقه‌ها با شرایط خاتمه و متغیرهای گفتگو می‌توانند مورد ارجاع قرار گیرند.',
    },
  },
  tracing: {
    stopBy: 'متوقف شده توسط {{user}}',
  },
  variableReference: {
    noAvailableVars: 'هیچ متغیری در دسترس نیست',
    conversationVars: 'متغیرهای مکالمه',
    noVarsForOperation: 'هیچ متغیری برای تخصیص با عملیات انتخاب شده در دسترس نیست.',
    assignedVarsDescription: 'متغیرهای اختصاص داده شده باید متغیرهای قابل نوشتن باشند، مانند',
    noAssignedVars: 'هیچ متغیر اختصاص داده شده در دسترس نیست',
  },
  versionHistory: {
    filter: {
      reset: 'بازنشانی فیلتر',
      onlyYours: 'فقط مال شماست',
      onlyShowNamedVersions: 'فقط نسخه‌های نام‌گذاری شده را نمایش بدهید',
      all: 'همه',
      empty: 'هیچ تاریخچه نسخه‌ای مطابق پیدا نشد',
    },
    editField: {
      title: 'عنوان',
      releaseNotes: 'یادداشت‌های نسخه',
      titleLengthLimit: 'عنوان نمی‌تواند از {{limit}} کاراکتر بیشتر شود',
      releaseNotesLengthLimit: 'یادداشت‌های انتشار نمی‌توانند از {{limit}} کاراکتر تجاوز کنند',
    },
    action: {
      updateSuccess: 'نسخه به‌روزرسانی شد',
      deleteSuccess: 'نسخه حذف شد',
      restoreSuccess: 'نسخه بازگردانی شده',
      deleteFailure: 'حذف نسخه موفق نبود',
      restoreFailure: 'بازگرداندن نسخه ناموفق بود',
      updateFailure: 'به‌روزرسانی نسخه ناموفق بود',
    },
    latest: 'آخرین',
    editVersionInfo: 'ویرایش اطلاعات نسخه',
    nameThisVersion: 'این نسخه را نامگذاری کنید',
    currentDraft: 'پیش نویس فعلی',
    defaultName: 'نسخه بدون عنوان',
    title: 'نسخه‌ها',
    releaseNotesPlaceholder: 'شرح دهید چه چیزی تغییر کرده است',
    restorationTip: 'پس از بازیابی نسخه، پیش‌نویس فعلی بازنویسی خواهد شد.',
    deletionTip: 'حذف غیرقابل برگشت است، لطفا تأیید کنید.',
  },
  debug: {
    noData: {
      runThisNode: 'این نود را اجرا کن',
      description: 'نتایج آخرین اجرا در اینجا نمایش داده خواهد شد',
    },
    variableInspect: {
      trigger: {
        clear: 'شفاف',
        stop: 'متوقف کن، برو',
        running: 'وضعیت اجرای کشینگ',
        normal: 'بازبینی متغیر',
        cached: 'مشاهده متغیرهای کش شده',
      },
      chatNode: 'گفتگو',
      edited: 'ویرایش شده',
      systemNode: 'سیستم',
      title: 'بازبینی متغیر',
      clearAll: 'همه را بازنشانی کن',
      emptyLink: 'بیشتر یاد بگیرید',
      reset: 'تنظیم به آخرین مقدار اجرا شده',
      view: 'مشاهده لاگ',
      envNode: 'محیط زیست',
      clearNode: 'کش متغیر کش شده را پاک کنید',
      emptyTip: 'پس از عبور از یک گره روی بوم یا اجرای گره به صورت مرحله‌ای، می‌توانید مقدار فعلی متغیر گره را در بازرسی متغیر مشاهده کنید.',
      resetConversationVar: 'متغیر گفتگو را به مقدار پیش‌فرض بازنشانی کنید',
    },
    settingsTab: 'تنظیمات',
    lastRunTab: 'آخرین اجرا',
  },
}

export default translation
