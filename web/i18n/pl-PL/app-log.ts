const translation = {
  title: '<PERSON>zienn<PERSON>',
  description:
    'Dzienniki rejestrują stan działania aplikacji, w tym dane wejściowe użytkowników i odpowiedzi AI.',
  dateTimeFormat: 'DD/MM/YYYY HH:mm',
  table: {
    header: {
      updatedTime: '<PERSON>zas aktualizacji',
      time: '<PERSON><PERSON> utworzenia',
      endUser: 'Użytkownik końcowy lub konto',
      input: 'Wejś<PERSON>',
      output: 'Wyjście',
      summary: 'Tytuł',
      messageCount: 'Liczba wiadomości',
      userRate: 'Ocena użytkownika',
      adminRate: 'Ocena operatora',
      startTime: 'CZAS STARTU',
      status: 'STATUS',
      runtime: 'CZAS DZIAŁANIA',
      tokens: 'TOKENY',
      user: 'UŻYTKOWNIK KOŃCOWY LUB KONTO',
      version: 'WERSJA',
    },
    pagination: {
      previous: '<PERSON><PERSON><PERSON><PERSON>',
      next: 'Następny',
    },
    empty: {
      noChat: '<PERSON>rak rozmowy',
      noOutput: '<PERSON><PERSON> wyników',
      element: {
        title: '<PERSON><PERSON> ktoś jest?',
        content:
          'Obserwuj i adnotuj interakcje między użytkownikami końcowymi a aplikacjami AI tutaj, aby ciągle poprawiać dokładność AI. Możesz spróbować <shareLink>udostępnić</shareLink> lub <testLink>przetestować</testLink> aplikację internetową samodzielnie, a następnie wrócić na tę stronę.',
      },
    },
  },
  detail: {
    time: 'Czas',
    conversationId: 'ID rozmowy',
    promptTemplate: 'Szablon monitu',
    promptTemplateBeforeChat:
      'Szablon monitu przed rozmową · Jako wiadomość systemowa',
    annotationTip: 'Usprawnienia oznaczone przez {{user}}',
    timeConsuming: '',
    second: 's',
    tokenCost: 'Wydatkowane tokeny',
    loading: 'ładowanie',
    operation: {
      like: 'lubię',
      dislike: 'nie lubię',
      addAnnotation: 'Dodaj usprawnienie',
      editAnnotation: 'Edytuj usprawnienie',
      annotationPlaceholder:
        'Wprowadź oczekiwaną odpowiedź, którą chcesz, aby AI odpowiedziało, co może być używane do dokładnego dostrojenia modelu i ciągłej poprawy jakości generacji tekstu w przyszłości.',
    },
    variables: 'Zmienne',
    uploadImages: 'Przesłane obrazy',
    modelParams: 'Parametry modelu',
  },
  filter: {
    period: {
      today: 'Dzisiaj',
      last7days: 'Ostatnie 7 dni',
      last4weeks: 'Ostatnie 4 tygodnie',
      last3months: 'Ostatnie 3 miesiące',
      last12months: 'Ostatnie 12 miesięcy',
      monthToDate: 'Od początku miesiąca',
      quarterToDate: 'Od początku kwartału',
      yearToDate: 'Od początku roku',
      allTime: 'Cały czas',
    },
    annotation: {
      all: 'Wszystkie',
      annotated: 'Zanotowane usprawnienia ({{count}} elementów)',
      not_annotated: 'Nie zanotowane',
    },
    sortBy: 'Sortuj według:',
    descending: 'malejąco',
    ascending: 'rosnąco',
  },
  workflowTitle: 'Dzienniki przepływu pracy',
  workflowSubtitle: 'Dziennik zarejestrował operację Automatyzacji.',
  runDetail: {
    title: 'Dziennik rozmowy',
    workflowTitle: 'Szczegół dziennika',
    fileListDetail: 'Detal',
    fileListLabel: 'Szczegóły pliku',
  },
  promptLog: 'Dziennik monitów',
  agentLog: 'Dziennik agenta',
  viewLog: 'Zobacz dziennik',
  agentLogDetail: {
    agentMode: 'Tryb agenta',
    toolUsed: 'Użyte narzędzia',
    iterations: 'Iteracje',
    iteration: 'Iteracja',
    finalProcessing: 'Końcowa obróbka',
  },
}

export default translation
