const translation = {
  list: {
    title: 'เอกสาร',
    desc: 'ไฟล์ทั้งหมดของความรู้จะแสดงที่นี่ และความรู้ทั้งหมดสามารถเชื่อมโยงกับการอ้างอิง Dify หรือจัดทําดัชนีผ่านปลั๊กอินแชท',
    addFile: 'เพิ่มไฟล์',
    addPages: 'เพิ่มหน้า',
    addUrl: 'เพิ่ม URL',
    table: {
      header: {
        fileName: 'ชื่อไฟล์',
        words: 'นิรุกติ',
        hitCount: 'จํานวนการดึงข้อมูล',
        uploadTime: 'เวลาอัปโหลด',
        status: 'สถานะ',
        action: 'การเคลื่อนไหว',
        chunkingMode: 'โหมดก้อน',
      },
      rename: 'ตั้งชื่อใหม่',
      name: 'ชื่อ',
    },
    action: {
      uploadFile: 'อัปโหลดไฟล์ใหม่',
      settings: 'การตั้งค่ากลุ่ม',
      addButton: 'เพิ่มก้อน',
      add: 'เพิ่มก้อน',
      batchAdd: 'เพิ่มแบทช์',
      archive: 'หอจดหมายเหตุ',
      unarchive: 'ยกเลิกการเก็บถาวร',
      delete: 'ลบ',
      enableWarning: 'ไม่สามารถเปิดใช้งานไฟล์ที่เก็บถาวรได้',
      sync: 'ซิงค์',
      pause: 'หยุด',
      resume: 'ดำเนิน',
    },
    index: {
      enable: 'เปิด',
      disable: 'เก',
      all: 'ทั้งหมด',
      enableTip: 'สามารถจัดทําดัชนีไฟล์ได้',
      disableTip: 'ไม่สามารถจัดทําดัชนีไฟล์',
    },
    status: {
      queuing: 'จัด คิว',
      indexing: 'ดัชนี',
      paused: 'หยุดชั่วคราว',
      error: 'ความผิดพลาด',
      available: 'มีอยู่',
      enabled: 'เปิด',
      disabled: 'พิการ',
      archived: 'เก็บ ถาวร',
    },
    empty: {
      title: 'ยังไม่มีเอกสาร',
      upload: {
        tip: 'คุณสามารถอัปโหลดไฟล์ ซิงค์จากเว็บไซต์ หรือจากแอป web เช่น Notion, GitHub เป็นต้น',
      },
      sync: {
        tip: 'Dify จะดาวน์โหลดไฟล์จาก Notion ของคุณเป็นระยะและดําเนินการให้เสร็จสมบูรณ์',
      },
    },
    delete: {
      title: 'คุณแน่ใจหรือเปล่า ลบ?',
      content: 'หากคุณต้องการดําเนินการต่อในภายหลัง คุณจะดําเนินการต่อจากจุดที่คุณค้างไว้',
    },
    batchModal: {
      title: 'แบทช์เพิ่มก้อน',
      csvUploadTitle: 'ลากและวางไฟล์ CSV ของคุณที่นี่ หรือ',
      browse: 'เล็ม',
      tip: 'ไฟล์ CSV ต้องสอดคล้องกับโครงสร้างต่อไปนี้:',
      question: 'ปัญหา',
      answer: 'ตอบ',
      contentTitle: 'เนื้อหาก้อน',
      content: 'เนื้อหา',
      template: 'ดาวน์โหลดเทมเพลตที่นี่',
      cancel: 'ยกเลิก',
      run: 'เรียกใช้แบทช์',
      runError: 'เรียกใช้ชุดงานล้มเหลว',
      processing: 'ในการประมวลผลแบบแบทช์',
      completed: 'นําเข้าเสร็จสมบูรณ์',
      error: 'ข้อผิดพลาดในการนําเข้า',
      ok: 'ตกลง, ได้',
    },
    learnMore: 'ศึกษาเพิ่มเติม',
  },
  metadata: {
    title: 'ข้อมูลเมตา',
    desc: 'การติดฉลากข้อมูลเมตาสําหรับเอกสารช่วยให้ AI สามารถเข้าถึงเอกสารได้ทันท่วงทีและเปิดเผยแหล่งที่มาของการอ้างอิงสําหรับผู้ใช้',
    dateTimeFormat: 'มมมม ด, ป hh:mm A',
    docTypeSelectTitle: 'โปรดเลือกประเภทเอกสาร',
    docTypeChangeTitle: 'เปลี่ยนชนิดเอกสาร',
    docTypeSelectWarning: 'หากชนิดเอกสารมีการเปลี่ยนแปลง ข้อมูลเมตาที่เติมในขณะนี้จะไม่ถูกเก็บรักษาไว้อีกต่อไป',
    firstMetaAction: 'ไปกันเถอะ',
    placeholder: {
      add: 'เพิ่ม',
      select: 'เลือก',
    },
    source: {
      upload_file: 'อัปโหลดไฟล์',
      notion: 'ซิงค์แบบฟอร์ม Notion',
      github: 'แบบฟอร์มซิงค์ Github',
    },
    type: {
      book: 'หนังสือ',
      webPage: 'เว็บเพจ',
      paper: 'กระดาษ',
      socialMediaPost: 'โพสต์บนโซเชียลมีเดีย',
      personalDocument: 'เอกสารส่วนตัว',
      businessDocument: 'เอกสารทางธุรกิจ',
      IMChat: 'แชท IM',
      wikipediaEntry: 'รายการวิกิพีเดีย',
      notion: 'ซิงค์แบบฟอร์ม Notion',
      github: 'แบบฟอร์มซิงค์ Github',
      technicalParameters: 'พารามิเตอร์ทางเทคนิค',
    },
    field: {
      processRule: {
        processDoc: 'เอกสารกระบวนการ',
        segmentRule: 'กฎก้อน',
        segmentLength: 'ความยาวของก้อน',
        processClean: 'กระบวนการส่งข้อความที่สะอาด',
      },
      book: {
        title: 'ชื่อเรื่อง',
        language: 'ภาษา',
        author: 'ผู้แต่ง',
        publisher: 'ผู้พิมพ์',
        publicationDate: 'วันที่ตีพิมพ์',
        ISBN: 'ไอเอส',
        category: 'ประเภท',
      },
      webPage: {
        title: 'ชื่อเรื่อง',
        url: 'URL',
        language: 'ภาษา',
        authorPublisher: 'ผู้เขียน/สํานักพิมพ์',
        publishDate: 'วันที่เผยแพร่',
        topicKeywords: 'หัวข้อ/คําสําคัญ',
        description: 'คำอธิบาย',
      },
      paper: {
        title: 'ชื่อเรื่อง',
        language: 'ภาษา',
        author: 'ผู้แต่ง',
        publishDate: 'วันที่เผยแพร่',
        journalConferenceName: 'ชื่อวารสาร/การประชุม',
        volumeIssuePage: 'ปริมาณ/ฉบับ/หน้า',
        DOI: 'ดอย',
        topicsKeywords: 'หัวข้อ/คําสําคัญ',
        abstract: 'นามธรรม',
      },
      socialMediaPost: {
        platform: 'แท่น',
        authorUsername: 'ผู้เขียน/ชื่อผู้ใช้',
        publishDate: 'วันที่เผยแพร่',
        postURL: 'URL โพสต์',
        topicsTags: 'หัวข้อ/แท็ก',
      },
      personalDocument: {
        title: 'ชื่อเรื่อง',
        author: 'ผู้แต่ง',
        creationDate: 'วันที่สร้าง',
        lastModifiedDate: 'วันที่แก้ไขล่าสุด',
        documentType: 'ประเภทเอกสาร',
        tagsCategory: 'แท็ก/หมวดหมู่',
      },
      businessDocument: {
        title: 'ชื่อเรื่อง',
        author: 'ผู้แต่ง',
        creationDate: 'วันที่สร้าง',
        lastModifiedDate: 'วันที่แก้ไขล่าสุด',
        documentType: 'ประเภทเอกสาร',
        departmentTeam: 'แผนก/ทีม',
      },
      IMChat: {
        chatPlatform: 'แพลตฟอร์มแชท',
        chatPartiesGroupName: 'ปาร์ตี้แชท/ชื่อกลุ่ม',
        participants: 'คน',
        startDate: 'วันที่เริ่มต้น',
        endDate: 'วันที่สิ้นสุด',
        topicsKeywords: 'หัวข้อ/คําสําคัญ',
        fileType: 'ประเภทไฟล์',
      },
      wikipediaEntry: {
        title: 'ชื่อเรื่อง',
        language: 'ภาษา',
        webpageURL: 'URL ของหน้าเว็บ',
        editorContributor: 'บรรณาธิการ/ผู้ร่วมให้ข้อมูล',
        lastEditDate: 'วันที่แก้ไขล่าสุด',
        summaryIntroduction: 'สรุป/บทนํา',
      },
      notion: {
        title: 'ชื่อเรื่อง',
        language: 'ภาษา',
        author: 'ผู้แต่ง',
        createdTime: 'เวลาที่สร้างขึ้น',
        lastModifiedTime: 'เวลาแก้ไขล่าสุด',
        url: 'URL',
        tag: 'ฉลาก',
        description: 'คำอธิบาย',
      },
      github: {
        repoName: 'ชื่อ Repo',
        repoDesc: 'คําอธิบาย Repo',
        repoOwner: 'เจ้าของ Repo',
        fileName: 'ชื่อไฟล์',
        filePath: 'เส้นทางไฟล์',
        programmingLang: 'ภาษาโปรแกรม',
        url: 'URL',
        license: 'ใบอนุญาต',
        lastCommitTime: 'เวลาคอมมิตล่าสุด',
        lastCommitAuthor: 'ผู้เขียนคอมมิตล่าสุด',
      },
      originInfo: {
        originalFilename: 'ชื่อไฟล์เดิม',
        originalFileSize: 'ขนาดไฟล์ต้นฉบับ',
        uploadDate: 'วันที่อัปโหลด',
        lastUpdateDate: 'วันที่อัปเดตล่าสุด',
        source: 'ที่มา',
      },
      technicalParameters: {
        segmentSpecification: 'ข้อมูลจําเพาะของก้อน',
        segmentLength: 'ความยาวของก้อน',
        avgParagraphLength: 'ความยาวย่อหน้าเฉลี่ย',
        paragraphs: 'ย่อหน้า',
        hitCount: 'จํานวนการดึงข้อมูล',
        embeddingTime: 'เวลาฝัง',
        embeddedSpend: 'การใช้จ่ายแบบฝังตัว',
      },
    },
    languageMap: {
      zh: 'จีน',
      en: 'อังกฤษ',
      es: 'สเปน',
      fr: 'ฝรั่งเศส',
      de: 'เยอรมัน',
      ja: 'ญี่ปุ่น',
      ko: 'เกาหลี',
      ru: 'รัสเซีย',
      ar: 'อาหรับ',
      pt: 'โปรตุเกส',
      it: 'อิตาลี',
      nl: 'ดัทช์',
      pl: 'โปแลนด์',
      sv: 'สวีเดน',
      tr: 'ตุรกี',
      he: 'เฮบรู',
      hi: 'ฮินดี',
      da: 'เดนมาร์ก',
      fi: 'ฟินแลนด์',
      no: 'นอร์เวย์',
      hu: 'ฮังการี',
      el: 'กรีก',
      cs: 'เชก',
      th: 'ไทย',
      id: 'อินโดนีเซีย',
    },
    categoryMap: {
      book: {
        fiction: 'นิยาย',
        biography: 'ชีวประวัติ',
        history: 'ประวัติศาสตร์',
        science: 'วิทยาศาสตร์',
        technology: 'เทคโนโลยี',
        education: 'การศึกษา',
        philosophy: 'ปรัชญา',
        religion: 'ศาสนา',
        socialSciences: 'สังคมศาสตร์',
        art: 'ศิลป์',
        travel: 'เดินทาง',
        health: 'สุขภาพ',
        selfHelp: 'ช่วยเหลือตนเอง',
        businessEconomics: 'ธุรกิจเศรษฐศาสตร์',
        cooking: 'การหุงต้ม',
        childrenYoungAdults: 'เด็กผู้ใหญ่',
        comicsGraphicNovels: 'การ์ตูนนิยายกราฟิก',
        poetry: 'กวีนิพนธ์',
        drama: 'ละคร',
        other: 'อื่นๆ',
      },
      personalDoc: {
        notes: 'หมาย เหตุ',
        blogDraft: 'ร่างบล็อก',
        diary: 'ไดอารี่',
        researchReport: 'รายงานการวิจัย',
        bookExcerpt: 'ข้อความที่ตัดตอนมาจากหนังสือ',
        schedule: 'ตาราง',
        list: 'รายการ',
        projectOverview: 'ภาพรวมโครงการ',
        photoCollection: 'คอลเลกชันภาพถ่าย',
        creativeWriting: 'การเขียนเชิงสร้างสรรค์',
        codeSnippet: 'ข้อมูลโค้ด',
        designDraft: 'ร่างการออกแบบ',
        personalResume: 'ประวัติส่วนตัว',
        other: 'อื่นๆ',
      },
      businessDoc: {
        meetingMinutes: 'รายงานการประชุม',
        researchReport: 'รายงานการวิจัย',
        proposal: 'ข้อเสนอ',
        employeeHandbook: 'คู่มือพนักงาน',
        trainingMaterials: 'สื่อการฝึกอบรม',
        requirementsDocument: 'เอกสารข้อกําหนด',
        designDocument: 'เอกสารการออกแบบ',
        productSpecification: 'คุณสมบัติของผลิตภัณฑ์',
        financialReport: 'รายงานทางการเงิน',
        marketAnalysis: 'การวิเคราะห์ตลาด',
        projectPlan: 'แผนโครงการ',
        teamStructure: 'โครงสร้างทีม',
        policiesProcedures: 'นโยบายและขั้นตอน',
        contractsAgreements: 'สัญญาและข้อตกลง',
        emailCorrespondence: 'การติดต่อทางอีเมล',
        other: 'อื่นๆ',
      },
    },
  },
  embedding: {
    processing: 'การประมวลผลการฝัง...',
    paused: 'การฝังหยุดชั่วคราว',
    completed: 'การฝังเสร็จสมบูรณ์',
    error: 'ข้อผิดพลาดในการฝัง',
    docName: 'เอกสารการประมวลผลล่วงหน้า',
    mode: 'กฎการแบ่งกลุ่ม',
    segmentLength: 'ความยาวของก้อน',
    textCleaning: 'การกําหนดข้อความล่วงหน้าและการทําความสะอาด',
    segments: 'ย่อหน้า',
    highQuality: 'โหมดคุณภาพสูง',
    economy: 'โหมดประหยัด',
    estimate: 'การบริโภคโดยประมาณ',
    stop: 'หยุดการประมวลผล',
    resume: 'ดําเนินการต่อ',
    automatic: 'อัตโนมัติ',
    custom: 'ธรรมเนียม',
    previewTip: 'การแสดงตัวอย่างย่อหน้าจะพร้อมใช้งานหลังจากการฝังเสร็จสิ้น',
    childMaxTokens: 'เด็ก',
    parentMaxTokens: 'พ่อแม่',
    pause: 'หยุด',
    hierarchical: 'พ่อแม่ลูก',
  },
  segment: {
    paragraphs: 'ย่อหน้า',
    keywords: 'คําสําคัญ',
    addKeyWord: 'เพิ่มคําสําคัญ',
    keywordError: 'ความยาวสูงสุดของคําหลักคือ 20',
    characters: 'อักขระ',
    hitCount: 'จํานวนการดึงข้อมูล',
    vectorHash: 'แฮชเวกเตอร์:',
    questionPlaceholder: 'เพิ่มคําถามที่นี่',
    questionEmpty: 'คําถามไม่สามารถว่างเปล่าได้',
    answerPlaceholder: 'เพิ่มคําตอบที่นี่',
    answerEmpty: 'คําตอบไม่สามารถว่างเปล่าได้',
    contentPlaceholder: 'เพิ่มเนื้อหาที่นี่',
    contentEmpty: 'เนื้อหาไม่สามารถว่างเปล่าได้',
    newTextSegment: 'เซ็กเมนต์ข้อความใหม่',
    newQaSegment: 'ส่วนถาม & คําตอบใหม่',
    delete: 'ลบส่วนนี้ ?',
    parentChunks_other: 'ก้อนผู้ปกครอง',
    childChunkAdded: 'เพิ่มก้อนลูก 1 ชิ้น',
    regeneratingMessage: 'อาจใช้เวลาสักครู่โปรดรอสักครู่...',
    regenerationSuccessTitle: 'การฟื้นฟูเสร็จสมบูรณ์',
    chunkDetail: 'รายละเอียดก้อน',
    childChunk: 'ก้อนเด็ก',
    chunk: 'ก้อน',
    edited: 'แก้ไข',
    addChunk: 'เพิ่มก้อน',
    editedAt: 'แก้ไขที่',
    dateTimeFormat: 'MM/DD/YYYY h:mm',
    childChunks_other: 'ก้อนเด็ก',
    editChildChunk: 'แก้ไข Child Chunk',
    parentChunk: 'ผู้ปกครอง-ก้อน',
    newChildChunk: 'ก้อนเด็กใหม่',
    regenerationConfirmTitle: 'คุณต้องการสร้างก้อนเด็กขึ้นมาใหม่หรือไม่?',
    chunks_other: 'ก้อน',
    regeneratingTitle: 'การสร้างก้อนลูกใหม่',
    regenerationConfirmMessage: 'การสร้างก้อนย่อยใหม่จะเขียนทับส่วนย่อยปัจจุบัน รวมถึงก้อนที่แก้ไขแล้วและก้อนที่เพิ่มเข้ามาใหม่ การฟื้นฟูไม่สามารถยกเลิกได้',
    chunkAdded: 'เพิ่ม 1 ก้อน',
    expandChunks: 'ขยายก้อน',
    searchResults_zero: 'ผล',
    characters_one: 'อักขระ',
    empty: 'ไม่พบก้อน',
    addChildChunk: 'เพิ่ม Child Chunk',
    chunks_one: 'ก้อน',
    clearFilter: 'ล้างตัวกรอง',
    searchResults_one: 'ผล',
    addAnother: 'เพิ่มอีก',
    editParentChunk: 'แก้ไขส่วนผู้ปกครอง',
    characters_other: 'อักขระ',
    parentChunks_one: 'ก้อนผู้ปกครอง',
    collapseChunks: 'ยุบก้อน',
    newChunk: 'ก้อนใหม่',
    editChunk: 'แก้ไขก้อน',
    searchResults_other: 'ผลลัพธ์',
    regenerationSuccessMessage: 'คุณสามารถปิดหน้าต่างนี้ได้',
    childChunks_one: 'ก้อนเด็ก',
    keywordDuplicate: 'คำสำคัญมีอยู่แล้ว',
    keywordEmpty: 'คีย์เวิร์ดไม่สามารถว่างเปล่าได้',
  },
}

export default translation
