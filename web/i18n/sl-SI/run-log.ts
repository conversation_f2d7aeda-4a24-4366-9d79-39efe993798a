const translation = {
  input: 'VNOS',
  result: 'REZULTAT',
  detail: 'PODROBNOSTI',
  tracing: 'SLEDENJE',
  resultPanel: {
    status: 'STATUS',
    time: 'PRETEČENI ČAS',
    tokens: 'SKUPNI ŽETONI',
  },
  meta: {
    title: 'METAPODATKI',
    status: 'Status',
    version: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    executor: '<PERSON><PERSON><PERSON><PERSON>le<PERSON>',
    startTime: 'Čas začetka',
    time: 'Pretečeni čas',
    tokens: 'Skupni žetoni',
    steps: 'Koraki izvajanja',
  },
  resultEmpty: {
    title: 'Ta zagon je izpisal samo format JSON,',
    tipLeft: 'prosimo, pojdite na ',
    link: 'panel podrobnosti',
    tipRight: ' za ogled.',
  },
  actionLogs: 'Dnevniki dejanj',
  circularInvocationTip: 'V trenutnem poteku dela obstaja krožno sklicevanje orodij / vozli<PERSON>.',
}

export default translation
