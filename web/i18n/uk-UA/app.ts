const translation = {
  createApp: 'Створити додаток',
  types: {
    all: 'Усе',
    chatbot: 'Ча<PERSON>б<PERSON><PERSON>',
    agent: 'Агент',
    workflow: 'Робочий процес',
    completion: 'Завершення',
    advanced: 'Чат',
    basic: 'Основні',
  },
  duplicate: 'Дублювати',
  duplicateTitle: 'Дублювати додаток',
  export: 'Експортувати DSL',
  exportFailed: 'Не вдалося експортувати DSL.',
  importDSL: 'Імпортувати файл DSL',
  createFromConfigFile: 'Створити з файлу DSL',
  deleteAppConfirmTitle: 'Видалити цей додаток?',
  deleteAppConfirmContent:
    'Видалення додатка незворотнє. Користувачі більше не зможуть отримати доступ до вашого додатка, і всі налаштування запитів та журнали будуть остаточно видалені.',
  appDeleted: 'Додаток видалено',
  appDeleteFailed: 'Не вдалося видалити додаток',
  join: 'Приєднуйтесь до спільноти',
  communityIntro:
    'Обговорюйте з членами команди, співавторами та розробниками на різних каналах.',
  roadmap: 'Переглянути наш план розвитку',
  newApp: {
    startFromBlank: 'Створити з нуля',
    startFromTemplate: 'Створити з шаблону',
    captionAppType: 'Який тип додатка ви хочете створити?',
    chatbotDescription: 'Побудуйте додаток на основі чату. Цей додаток використовує формат запитань та відповідей, що дозволяє проводити кілька раундів безперервного спілкування.',
    completionDescription: 'Побудуйте додаток, який генерує текст високої якості на основі підказок, таких як генерація статей, резюме, перекладів тощо.',
    completionWarning: 'Цей тип додатка більше не буде підтримуватися.',
    agentDescription: 'Побудуйте інтелектуального агента, який може автономно обирати інструменти для виконання завдань',
    workflowDescription: 'Побудуйте додаток, який генерує текст високої якості на основі робочого процесу з високим рівнем настроювання. Він підходить для досвідчених користувачів.',
    workflowWarning: 'Наразі в бета-версії',
    chatbotType: 'Метод оркестрації чатботу',
    basic: 'Базовий',
    basicTip: 'Для початківців, можна перейти до Chatflow пізніше',
    basicFor: 'ДЛЯ ПОЧАТКІВЦІВ',
    basicDescription: 'Базовий оркестр дозволяє оркеструвати додаток чатбота за допомогою простих налаштувань, без можливості змінювати вбудовані підказки. Він підходить для початківців.',
    advanced: 'Chatflow',
    advancedFor: 'Для досвідчених користувачів',
    advancedDescription: 'Оркестрування робочого процесу оркеструє чатботи у формі робочих процесів, пропонуючи високий рівень настроювання, включаючи можливість редагувати вбудовані підказки. Він підходить для досвідчених користувачів.',
    captionName: 'Іконка та назва додатка',
    appNamePlaceholder: 'Дайте назву вашому додатку',
    captionDescription: 'Опис',
    appDescriptionPlaceholder: 'Введіть опис додатка',
    useTemplate: 'Використовуйте цей шаблон',
    previewDemo: 'Попередній перегляд демонстрації',
    chatApp: 'Асистент',
    chatAppIntro:
      'Я хочу побудувати додаток на основі чату. Цей додаток використовує формат запитань та відповідей, що дозволяє проводити кілька раундів безперервного спілкування.',
    agentAssistant: 'Новий помічник агента',
    completeApp: 'Генератор тексту',
    completeAppIntro:
      'Я хочу створити додаток, який генерує текст високої якості на основі підказок, таких як генерація статей, резюме, перекладів тощо.',
    showTemplates: 'Я хочу вибрати з шаблону',
    hideTemplates: 'Повернутися до вибору режиму',
    Create: 'Створити',
    Cancel: 'Скасувати',
    nameNotEmpty: 'Назва не може бути порожньою',
    appTemplateNotSelected: 'Будь ласка, виберіть шаблон',
    appTypeRequired: 'Будь ласка, виберіть тип додатка',
    appCreated: 'Додаток створено',
    appCreateFailed: 'Не вдалося створити додаток',
    caution: 'Обережність',
    Confirm: 'Підтвердити',
    appCreateDSLErrorPart3: 'Поточна версія DSL програми:',
    appCreateDSLErrorPart4: 'Версія DSL з підтримкою системи:',
    appCreateDSLErrorPart2: 'Хочете продовжити?',
    appCreateDSLErrorTitle: 'Несумісність версій',
    appCreateDSLErrorPart1: 'Виявлено суттєву різницю у версіях DSL. Примусовий імпорт може призвести до неправильної роботи програми.',
    appCreateDSLWarning: 'Увага: різниця у версіях DSL може вплинути на певні функції',
    chooseAppType: 'Оберіть тип додатку',
    noIdeaTip: 'Немає ідей? Перегляньте наші шаблони',
    noTemplateFoundTip: 'Спробуйте шукати за різними ключовими словами.',
    foundResult: '{{count}} Результат',
    foundResults: '{{count}} Результатів',
    optional: 'Необов\'язково',
    completionShortDescription: 'AI-помічник для завдань генерації тексту',
    forAdvanced: 'ДЛЯ ДОСВІДЧЕНИХ КОРИСТУВАЧІВ',
    noTemplateFound: 'Не знайдено шаблонів',
    agentUserDescription: 'Інтелектуальний агент, здатний до ітеративного міркування і автономного використання інструменту для досягнення поставлених цілей.',
    advancedUserDescription: 'Робочий процес з функціями пам\'яті та інтерфейсом чат-бота.',
    agentShortDescription: 'Інтелектуальний агент з міркуваннями та автономним використанням інструментів',
    noAppsFound: 'Не знайдено додатків',
    forBeginners: 'Простіші типи додатків',
    workflowShortDescription: 'Агентський потік для інтелектуальних автоматизацій',
    learnMore: 'Дізнатися більше',
    chatbotUserDescription: 'Швидко створюйте чат-бота на базі LLM за допомогою простої конфігурації. Ви можете переключитися на Chatflow пізніше.',
    chatbotShortDescription: 'Чат-бот на базі LLM з простим налаштуванням',
    advancedShortDescription: 'Робочий процес, вдосконалений для багатоетапних чатів',
    completionUserDescription: 'Швидко створюйте помічника зі штучним інтелектом для завдань із генерації тексту за допомогою простої конфігурації.',
    workflowUserDescription: 'ізуально створюйте автономні ШІ-процеси з простотою перетягування.',
    dropDSLToCreateApp: 'Перетягніть файл DSL сюди, щоб створити додаток',
  },
  editApp: 'Редагувати інформацію',
  editAppTitle: 'Редагувати інформацію про додаток',
  editDone: 'Інформація про додаток оновлена',
  editFailed: 'Не вдалося оновити інформацію про додаток',
  iconPicker: {
    ok: 'OK',
    cancel: 'Скасувати',
    emoji: 'Емодзі',
    image: 'Зображення',
  },
  switch: 'Перейти до оркестрації робочого процесу',
  switchTipStart: 'Для вас буде створена нова копія додатка, і нова копія перейде до оркестрації робочого процесу. Нова копія не дозволить ',
  switchTip: 'повернутися',
  switchTipEnd: ' до базової оркестрації.',
  switchLabel: 'Копія додатка, яка буде створена',
  removeOriginal: 'Видалити початковий додаток',
  switchStart: 'Почати перемикання',
  typeSelector: {
    all: 'Усі типи',
    chatbot: 'Чатбот',
    agent: 'Агент',
    workflow: 'Робочий процес',
    completion: 'Завершення',
    advanced: 'Чат',
  },
  tracing: {
    title: 'Відстеження продуктивності додатку',
    description: 'Налаштування стороннього провайдера LLMOps та відстеження продуктивності додатку.',
    config: 'Налаштувати',
    collapse: 'Згорнути',
    expand: 'Розгорнути',
    tracing: 'Відстеження',
    disabled: 'Вимкнено',
    disabledTip: 'Спочатку налаштуйте провайдера',
    enabled: 'В роботі',
    tracingDescription: 'Захоплення повного контексту виконання додатку, включаючи виклики LLM, контекст, підказки, HTTP-запити та інше, на сторонню платформу відстеження.',
    configProviderTitle: {
      configured: 'Налаштовано',
      notConfigured: 'Налаштуйте провайдера для увімкнення відстеження',
      moreProvider: 'Більше провайдерів',
    },
    arize: {
      title: 'Arize',
      description: 'Спостережуваність LLM корпоративного рівня, онлайн та офлайн оцінювання, моніторинг та експерименти—на основі OpenTelemetry. Спеціально розроблено для застосунків на базі LLM та агентів.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Відкрита та заснована на OpenTelemetry платформа для спостережуваності, оцінювання, інженерії підказок та експериментів для ваших робочих процесів та агентів LLM.',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'Універсальна платформа розробника для кожного етапу життєвого циклу додатку на основі LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Трасування, оцінки, управління підказками та метрики для налагодження та покращення вашого LLM-додатку.',
    },
    inUse: 'Використовується',
    configProvider: {
      title: 'Налаштувати ',
      placeholder: 'Введіть ваш {{key}}',
      project: 'Проект',
      publicKey: 'Публічний ключ',
      secretKey: 'Секретний ключ',
      viewDocsLink: 'Переглянути документацію {{key}}',
      removeConfirmTitle: 'Видалити налаштування {{key}}?',
      removeConfirmContent: 'Поточне налаштування використовується, його видалення вимкне функцію Відстеження.',
    },
    view: 'Вид',
    opik: {
      title: 'Опік',
      description: 'Opik — це платформа з відкритим вихідним кодом для оцінки, тестування та моніторингу додатків LLM.',
    },
    weave: {
      title: 'Ткати',
      description: 'Weave є платформою з відкритим кодом для оцінки, тестування та моніторингу LLM додатків.',
    },
    aliyun: {
      title: 'Моніторинг Хмари',
      description: 'Повністю керовані та без обслуговування платформи спостереження, надані Alibaba Cloud, дозволяють миттєвий моніторинг, трасування та оцінку застосувань Dify.',
    },
  },
  answerIcon: {
    title: 'Використовуйте піктограму web app для заміни 🤖',
    description: 'Чи слід використовувати піктограму web app для заміни 🤖 у спільній програмі',
    descriptionInExplore: 'Чи використовувати піктограму веб-програми для заміни 🤖 в Огляді',
  },
  importFromDSLUrl: 'З URL',
  importFromDSL: 'Імпорт з DSL',
  importFromDSLUrlPlaceholder: 'Вставте посилання на DSL тут',
  importFromDSLFile: 'З DSL-файлу',
  mermaid: {
    handDrawn: 'Намальовані від руки',
    classic: 'Класичний',
  },
  openInExplore: 'Відкрити в Огляді',
  newAppFromTemplate: {
    sidebar: {
      Writing: 'Написання',
      Assistant: 'Асистент',
      Workflow: 'Робочий процес',
      Agent: 'Агент',
      Recommended: 'Рекомендується',
      HR: 'Управління персоналом',
      Programming: 'Програмування',
    },
    byCategories: 'ЗА КАТЕГОРІЯМИ',
    searchAllTemplate: 'Пошук по всіх шаблонах...',
  },
  showMyCreatedAppsOnly: 'Показати лише створені мною додатки',
  appSelector: {
    noParams: 'Параметри не потрібні',
    label: 'ДОДАТОК',
    params: 'ПАРАМЕТРИ ПРОГРАМИ',
    placeholder: 'Виберіть програму...',
  },
  structOutput: {
    LLMResponse: 'Відповідь ЛЛМ',
    configure: 'Налаштувати',
    required: 'Необхідно',
    moreFillTip: 'Показуючи максимум 10 рівнів вкладеності',
    structured: 'Структурований',
    modelNotSupported: 'Модель не підтримується',
    notConfiguredTip: 'Структурований вихід ще не було налаштовано',
    modelNotSupportedTip: 'Поточна модель не підтримує цю функцію та автоматично знижується до ін\'єкції запитів.',
    structuredTip: 'Структуровані виходи - це функція, яка забезпечує, що модель завжди генеруватиме відповіді, що відповідають наданій вами схемі JSON.',
  },
  accessItemsDescription: {
    anyone: 'Будь-хто може отримати доступ до веб-додатку',
    specific: 'Тільки окремі групи або члени можуть отримати доступ до веб-додатку.',
    organization: 'Будь-хто в організації може отримати доступ до веб-додатку.',
    external: 'Тільки перевірені зовнішні користувачі можуть отримати доступ до веб-застосунку.',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Кожен, у кого є посилання',
      specific: 'Конкретні групи або члени',
      organization: 'Тільки члени підприємства',
      external: 'Аутентифіковані зовнішні користувачі',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Шукати групи та учасників',
      allMembers: 'Всі члени',
      expand: 'розвивати',
      noResult: 'Немає результату',
    },
    title: 'Контроль доступу до веб-додатка',
    description: 'Встановіть дозволи доступу до веб-додатку',
    accessLabel: 'Хто має доступ',
    groups_one: '{{count}} ГРУПА',
    groups_other: '{{count}} ГРУП',
    members_one: '{{count}} ЧЛЕН',
    members_other: '{{count}} ЧЛЕНІ',
    noGroupsOrMembers: 'Не вибрано групи чи учасників',
    updateSuccess: 'Оновлення успішно',
    webAppSSONotEnabledTip: 'Будь ласка, зв\'яжіться з адміністратором підприємства для налаштування методу аутентифікації веб-додатку.',
  },
  publishApp: {
    title: 'Хто може отримати доступ до веб-додатку',
    notSet: 'Не встановлено',
    notSetDesc: 'На даний момент ніхто не може отримати доступ до веб-додатку. Будь ласка, налаштуйте дозволи.',
  },
  accessControl: 'Контроль доступу до веб-додатків',
  noAccessPermission: 'Немає дозволу на доступ до веб-додатку',
  maxActiveRequestsPlaceholder: 'Введіть 0 для необмеженого',
  maxActiveRequests: 'Максимальна кількість одночасних запитів',
}

export default translation
