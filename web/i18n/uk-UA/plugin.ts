const translation = {
  category: {
    tools: 'Інструмент',
    all: 'Увесь',
    bundles: 'Пакети',
    models: 'Моделі',
    extensions: 'Розширення',
    agents: 'Стратегії агентів',
  },
  categorySingle: {
    agent: 'Стратегія агента',
    bundle: 'Комплектація',
    tool: 'Інструмент',
    extension: 'Збільшення',
    model: 'Модель',
  },
  list: {
    source: {
      local: 'Інсталяція з локального файлу пакета',
      marketplace: 'Інсталяція з Marketplace',
      github: 'Встановлення з GitHub',
    },
    noInstalled: 'Плагіни не встановлено',
    notFound: 'Плаг<PERSON><PERSON>ів не знайдено',
  },
  source: {
    marketplace: 'Ринку',
    local: 'Файл локального пакета',
    github: 'Гітхаб',
  },
  detailPanel: {
    categoryTip: {
      github: 'Встановлено з Github',
      debugging: 'Плагін налагодження',
      local: 'Локальний плагін',
      marketplace: 'Інстальовано з Marketplace',
    },
    operation: {
      viewDetail: 'Переглянути деталі',
      detail: 'Деталі',
      remove: 'Видалити',
      install: 'Інсталювати',
      checkUpdate: 'Перевірити Оновлення',
      update: 'Оновлювати',
      info: 'Інформація про плагін',
    },
    toolSelector: {
      placeholder: 'Виберіть інструмент...',
      descriptionLabel: 'Опис засобу',
      paramsTip1: 'Контролює параметри логічного висновку LLM.',
      toolLabel: 'Інструмент',
      params: 'КОНФІГУРАЦІЯ МІРКУВАНЬ',
      settings: 'НАЛАШТУВАННЯ КОРИСТУВАЧА',
      uninstalledLink: 'Керування в плагінах',
      title: 'Додати інструмент',
      paramsTip2: 'Коли параметр "Автоматично" вимкнено, використовується значення за замовчуванням.',
      empty: 'Натисніть кнопку «+», щоб додати інструменти. Ви можете додати кілька інструментів.',
      uninstalledTitle: 'Інструмент не встановлено',
      descriptionPlaceholder: 'Короткий опис призначення інструменту, наприклад, отримання температури для конкретного місця.',
      unsupportedTitle: 'Непідтримувані дії',
      unsupportedContent2: 'Натисніть, щоб змінити версію.',
      auto: 'Автоматичний',
      uninstalledContent: 'Цей плагін встановлюється з локального/GitHub репозиторію. Будь ласка, використовуйте після встановлення.',
      unsupportedContent: 'Встановлена версія плагіна не передбачає цієї дії.',
      toolSetting: 'Налаштування інструментів',
      unsupportedMCPTool: 'Використовувана версія плагіна стратегії агента наразі не підтримує інструменти MCP.',
    },
    modelNum: '{{num}} МОДЕЛІ В КОМПЛЕКТІ',
    switchVersion: 'Версія перемикача',
    configureApp: 'Налаштуйте додаток',
    endpointDeleteTip: 'Видалити кінцеву точку',
    endpoints: 'Кінцеві точки',
    endpointsDocLink: 'Переглянути документ',
    configureModel: 'Налаштування моделі',
    endpointDisableTip: 'Вимкніть кінцеву точку',
    endpointsEmpty: 'Натисніть кнопку «+», щоб додати кінцеву точку',
    actionNum: '{{num}} {{дія}} ВКЛЮЧЕНІ',
    disabled: 'Вимкнуто',
    endpointModalTitle: 'Налаштування кінцевої точки',
    endpointDisableContent: 'Чи хотіли б ви вимкнути {{name}}?',
    endpointDeleteContent: 'Чи хотіли б ви видалити {{name}}?',
    endpointsTip: 'Цей плагін надає конкретні функції через кінцеві точки, і ви можете налаштувати кілька наборів кінцевих точок для поточного робочого простору.',
    strategyNum: '{{num}} {{стратегія}} ВКЛЮЧЕНІ',
    endpointModalDesc: 'Після налаштування можна використовувати функції, що надаються плагіном через кінцеві точки API.',
    configureTool: 'Інструмент налаштування',
    serviceOk: 'Сервіс працює',
    deprecation: {
      reason: {
        ownershipTransferred: 'право власності передано',
        businessAdjustments: 'бізнесові корективи',
        noMaintainer: 'немає супроводжувача',
      },
      noReason: 'Цей плагін було застаріло, і він більше не буде оновлюватися.',
      onlyReason: 'Цей плагін було знято з підтримки через {{deprecatedReason}} і більше не буде оновлюватися.',
      fullMessage: 'Цей плагін був застарілий через {{deprecatedReason}}, і більше не буде оновлюватися. Будь ласка, використовуйте <CustomLink href=\'https://example.com/\'>{{-alternativePluginId}}</CustomLink> замість цього.',
    },
  },
  debugInfo: {
    title: 'Налагодження',
    viewDocs: 'Переглянути документи',
  },
  privilege: {
    whoCanDebug: 'Хто може налагоджувати плагіни?',
    admins: 'Адміни',
    noone: 'Ніхто',
    whoCanInstall: 'Хто може встановлювати плагіни та керувати ними?',
    everyone: 'Кожен',
    title: 'Налаштування плагіна',
  },
  pluginInfoModal: {
    repository: 'Сховище',
    release: 'Реліз',
    title: 'Інформація про плагін',
    packageName: 'Пакунок',
  },
  action: {
    deleteContentLeft: 'Чи хотіли б ви видалити',
    usedInApps: 'Цей плагін використовується в додатках {{num}}.',
    deleteContentRight: 'плагін?',
    checkForUpdates: 'Перевірте наявність оновлень',
    delete: 'Видалити плагін',
    pluginInfo: 'Інформація про плагін',
  },
  installModal: {
    labels: {
      package: 'Пакунок',
      repository: 'Сховище',
      version: 'Версія',
    },
    uploadFailed: 'Не вдалося завантажити файл',
    close: 'Закрити',
    installedSuccessfullyDesc: 'Плагін успішно встановлено.',
    readyToInstallPackages: 'Про встановлення наступних плагінів {{num}}',
    install: 'Інсталювати',
    cancel: 'Скасувати',
    readyToInstall: 'Про встановлення наступного плагіна',
    pluginLoadErrorDesc: 'Цей плагін не буде встановлено',
    installComplete: 'Інсталяцію завершено',
    installing: 'Установки...',
    installPlugin: 'Встановити плагін',
    dropPluginToInstall: 'Перетягніть пакет плагіна сюди, щоб встановити',
    uploadingPackage: 'Завантаження {{packageName}}...',
    readyToInstallPackage: 'Про встановлення наступного плагіна',
    pluginLoadError: 'Помилка завантаження плагіна',
    fromTrustSource: 'Будь ласка, переконайтеся, що ви встановлюєте плагіни лише з <trustSource>надійного джерела</trustSource>.',
    back: 'Задній',
    installFailedDesc: 'Плагін був встановлений не вдалося.',
    installFailed: 'Не вдалося встановити',
    installedSuccessfully: 'Монтаж успішний',
    next: 'Наступний',
    installWarning: 'Цей плагін не можна установити.',
  },
  installFromGitHub: {
    selectVersionPlaceholder: 'Будь ласка, оберіть версію',
    uploadFailed: 'Не вдалося завантажити файл',
    selectVersion: 'Оберіть версію',
    installNote: 'Будь ласка, переконайтеся, що ви встановлюєте плагіни лише з надійного джерела.',
    gitHubRepo: 'Репозиторій GitHub',
    installFailed: 'Не вдалося встановити',
    installPlugin: 'Встановити плагін з GitHub',
    updatePlugin: 'Оновити плагін з GitHub',
    installedSuccessfully: 'Монтаж успішний',
    selectPackage: 'Оберіть пакет',
    selectPackagePlaceholder: 'Будь ласка, оберіть пакет',
  },
  upgrade: {
    description: 'Про встановлення наступного плагіна',
    close: 'Закрити',
    successfulTitle: 'Установка успішна',
    upgrade: 'Інсталювати',
    usedInApps: 'Використовується в додатках {{num}}',
    upgrading: 'Установки...',
    title: 'Встановити плагін',
  },
  error: {
    noReleasesFound: 'Релізів не знайдено. Будь ласка, перевірте репозиторій GitHub або URL-адресу введення.',
    fetchReleasesError: 'Не вдається отримати згоди. Повторіть спробу пізніше.',
    inValidGitHubUrl: 'Невірна URL-адреса GitHub. Будь ласка, введіть дійсну URL-адресу у форматі: https://github.com/owner/repo',
  },
  marketplace: {
    sortOption: {
      mostPopular: 'Найпопулярніших',
      newlyReleased: 'Новий реліз',
      recentlyUpdated: 'Нещодавно оновлено',
      firstReleased: 'Перший реліз',
    },
    and: 'і',
    discover: 'Виявити',
    moreFrom: 'Більше від Marketplace',
    sortBy: 'Чорне місто',
    pluginsResult: 'Результати {{num}}',
    empower: 'Розширюйте можливості розробки штучного інтелекту',
    difyMarketplace: 'Dify Marketplace',
    viewMore: 'Дивитись більше',
    noPluginFound: 'Плагін не знайдено',
    verifiedTip: 'Перевірено Dify',
    partnerTip: 'Перевірено партнером Dify',
  },
  task: {
    installingWithError: 'Не вдалося встановити плагіни {{installingLength}}, успіх {{successLength}}, {{errorLength}}',
    clearAll: 'Очистити все',
    installedError: 'Плагіни {{errorLength}} не вдалося встановити',
    installError: 'Плагіни {{errorLength}} не вдалося встановити, натисніть, щоб переглянути',
    installing: 'Встановлення плагінів {{installingLength}}, 0 виконано.',
    installingWithSuccess: 'Встановлення плагінів {{installingLength}}, успіх {{successLength}}.',
  },
  from: 'Від',
  searchInMarketplace: 'Пошук у Marketplace',
  endpointsEnabled: '{{num}} наборів кінцевих точок увімкнено',
  installAction: 'Інсталювати',
  findMoreInMarketplace: 'Дізнайтеся більше в Marketplace',
  installFrom: 'ВСТАНОВИТИ З',
  install: '{{num}} встановлює',
  fromMarketplace: 'Від Marketplace',
  searchCategories: 'Категорії пошуку',
  installPlugin: 'Встановити плагін',
  searchTools: 'Інструменти пошуку...',
  search: 'Шукати',
  searchPlugins: 'Плагіни пошуку',
  allCategories: 'Всі категорії',
  metadata: {
    title: 'Плагіни',
  },
  difyVersionNotCompatible: 'Поточна версія Dify не сумісна з цим плагіном, будь ласка, оновіть до мінімальної версії: {{minimalDifyVersion}}',
  requestAPlugin: 'Запросити плагін',
  publishPlugins: 'Публікація плагінів',
  auth: {
    custom: 'Користувацький',
    authorization: 'Авторизація',
    authRemoved: 'Автор видалено',
    addOAuth: 'Додати OAuth',
    setDefault: 'Встановити за замовчуванням',
    useOAuth: 'Використовуйте OAuth',
    useApi: 'Використовуйте ключ API',
    saveAndAuth: 'Зберегти та авторизувати',
    setupOAuth: 'Налаштування OAuth клієнта',
    saveOnly: 'Зберегти лише',
    authorizationName: 'Назва авторизації',
    workspaceDefault: 'За замовчуванням робочого простору',
    authorizations: 'Авторизації',
    addApi: 'Додайте ключ API',
    useOAuthAuth: 'Використовуйте авторизацію OAuth',
    useApiAuth: 'Конфігурація авторизації API ключа',
    oauthClientSettings: 'Налаштування клієнта OAuth',
    default: 'За замовчуванням',
    oauthClient: 'Клієнт OAuth',
    clientInfo: 'Оскільки не знайдено жодних секретів клієнта системи для цього постачальника інструментів, потрібно налаштувати його вручну; для redirect_uri, будь ласка, використовуйте',
    useApiAuthDesc: 'Після налаштування облікових даних усі учасники робочого простору можуть використовувати цей інструмент під час оркестрації додатків.',
  },
  deprecated: 'Застарілий',
  autoUpdate: {
    strategy: {
      disabled: {
        name: 'Вимкнено',
        description: 'Плагіни не будуть автоматично оновлюватися',
      },
      fixOnly: {
        name: 'Виправити тільки',
        selectedDescription: 'Автоматичне оновлення лише для версій патчів',
      },
      latest: {
        name: 'Останні',
        selectedDescription: 'Завжди оновлюйте до останньої версії',
        description: 'Завжди оновлюйте до останньої версії',
      },
    },
    upgradeMode: {
      all: 'Оновити все',
      partial: 'Тільки вибрані',
      exclude: 'Виключити вибране',
    },
    upgradeModePlaceholder: {
      exclude: 'Вибрані плагіни не будуть оновлюватися автоматично',
      partial: 'Тільки вибрані плагіни будуть автоматично оновлюватись. Наразі жоден з плагінів не вибрано, тому жоден плагін не буде автоматично оновлений.',
    },
    operation: {
      clearAll: 'Очистити все',
      select: 'Виберіть плагіни',
    },
    pluginDowngradeWarning: {
      downgrade: 'Все одно знизити версію',
      title: 'Пониження плагіна',
      exclude: 'Виключити з автоматичного оновлення',
      description: 'Автоматичне оновлення наразі увімкнене для цього плагіна. Пониження версії може призвести до того, що ваші зміни будуть перезаписані під час наступного автоматичного оновлення.',
    },
    noPluginPlaceholder: {
      noFound: 'Плагіни не були знайдені',
      noInstalled: 'Жодних плагінів не встановлено',
    },
    updateTime: 'Час оновлення',
    automaticUpdates: 'Автоматичні оновлення',
    updateTimeTitle: 'Час оновлення',
    nextUpdateTime: 'Наступне автоматичне оновлення: {{time}}',
    specifyPluginsToUpdate: 'Вкажіть плагіни для оновлення',
    excludeUpdate: 'Наступні {{num}} плагіни не будуть автоматично оновлюватися',
    updateSettings: 'Оновити налаштування',
    changeTimezone: 'Щоб змінити часовий пояс, перейдіть до <setTimezone>Налаштування</setTimezone>',
    partialUPdate: 'Тільки наступні {{num}} плагіни будуть автоматично оновлюватися',
  },
}

export default translation
