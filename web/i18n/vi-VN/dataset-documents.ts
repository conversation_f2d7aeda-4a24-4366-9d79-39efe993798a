const translation = {
  list: {
    title: 'Tài liệu',
    desc: 'Tất cả các tệp của Kiến thức được hiển thị ở đây. Toàn bộ Kiến thức có thể được liên kết với trích dẫn của Dify hoặc được lập chỉ mục thông qua plugin Chat.',
    addFile: 'Thêm tệp',
    addPages: 'Thêm trang',
    table: {
      header: {
        fileName: 'TÊN TỆP',
        words: 'SỐ TỪ',
        hitCount: 'SỐ LẦN TRUY VẤN',
        uploadTime: 'THỜI GIAN TẢI LÊN',
        status: 'TRẠNG THÁI',
        action: 'THAO TÁC',
        chunkingMode: 'CHẾ ĐỘ CHUNKING',
      },
      rename: '<PERSON><PERSON>',
      name: 'Tên',
    },
    action: {
      uploadFile: 'Tải lên tệp mới',
      settings: 'Cài đặt phân đoạn',
      addButton: 'Thêm đoạn',
      add: 'Thêm một đoạn',
      batchAdd: 'Thêm hàng loạt',
      archive: '<PERSON>ư<PERSON> trữ',
      unarchive: 'Khôi phục',
      delete: 'Xó<PERSON>',
      enableWarning: 'Tệp đã lưu trữ không thể được kích hoạt',
      sync: 'Đồng bộ',
      pause: 'Tạm dừng',
      resume: 'Tiếp tục',
    },
    index: {
      enable: 'Kích hoạt',
      disable: 'Vô hiệu hóa',
      all: 'Tất cả',
      enableTip: 'Tệp có thể được lập chỉ mục',
      disableTip: 'Tệp không thể được lập chỉ mục',
    },
    status: {
      queuing: 'Đang chờ',
      indexing: 'Đang lập chỉ mục',
      paused: 'Tạm dừng',
      error: 'Lỗi',
      available: 'Có sẵn',
      enabled: 'Đã kích hoạt',
      disabled: 'Đã vô hiệu hóa',
      archived: 'Đã lưu trữ',
    },
    empty: {
      title: 'Chưa có tài liệu',
      upload: {
        tip: 'Bạn có thể tải lên tệp, đồng bộ từ trang web, hoặc từ ứng dụng web như Notion, GitHub, v.v.',
      },
      sync: {
        tip: 'Dify sẽ định kỳ tải xuống tệp từ Notion của bạn và hoàn tất xử lý.',
      },
    },
    delete: {
      title: 'Bạn có chắc chắn muốn xóa?',
      content: 'Nếu bạn cần tiếp tục xử lý sau này, bạn sẽ tiếp tục từ vị trí bạn đã dừng lại',
    },
    batchModal: {
      title: 'Thêm đoạn hàng loạt',
      csvUploadTitle: 'Kéo và thả tệp CSV của bạn vào đây, hoặc ',
      browse: 'duyệt',
      tip: 'Tệp CSV phải tuân thủ cấu trúc sau:',
      question: 'câu hỏi',
      answer: 'trả lời',
      contentTitle: 'nội dung đoạn',
      content: 'nội dung',
      template: 'Tải mẫu ở đây',
      cancel: 'Hủy',
      run: 'Chạy hàng loạt',
      runError: 'Chạy hàng loạt thất bại',
      processing: 'Đang xử lý hàng loạt',
      completed: 'Nhập hoàn tất',
      error: 'Lỗi nhập',
      ok: 'OK',
    },
    addUrl: 'Thêm URL',
    learnMore: 'Tìm hiểu thêm',
  },
  metadata: {
    title: 'Siêu dữ liệu',
    desc: 'Gắn nhãn siêu dữ liệu cho các tài liệu cho phép AI truy cập chúng kịp thời và tiết lộ nguồn của các tài liệu tham chiếu cho người dùng.',
    dateTimeFormat: 'D MMMM, YYYY hh:mm A',
    docTypeSelectTitle: 'Vui lòng chọn loại tài liệu',
    docTypeChangeTitle: 'Thay đổi loại tài liệu',
    docTypeSelectWarning: 'Nếu thay đổi loại tài liệu, các siêu dữ liệu hiện tại sẽ không được bảo toàn',
    firstMetaAction: 'Bắt đầu',
    placeholder: {
      add: 'Thêm ',
      select: 'Chọn ',
    },
    source: {
      upload_file: 'Tải lên tệp',
      notion: 'Đồng bộ từ Notion',
      github: 'Đồng bộ từ Github',
    },
    type: {
      book: 'Sách',
      webPage: 'Trang web',
      paper: 'Bài báo',
      socialMediaPost: 'Bài đăng mạng xã hội',
      personalDocument: 'Tài liệu cá nhân',
      businessDocument: 'Tài liệu doanh nghiệp',
      IMChat: 'Trò chuyện tin nhắn',
      wikipediaEntry: 'Bài viết Wikipedia',
      notion: 'Đồng bộ từ Notion',
      github: 'Đồng bộ từ Github',
      technicalParameters: 'Tham số kỹ thuật',
    },
    field: {
      processRule: {
        processDoc: 'Xử lý tài liệu',
        segmentRule: 'Quy tắc phân đoạn',
        segmentLength: 'Độ dài đoạn',
        processClean: 'Quy tắc làm sạch văn bản',
      },
      book: {
        title: 'Tiêu đề',
        language: 'Ngôn ngữ',
        author: 'Tác giả',
        publisher: 'Nhà xuất bản',
        publicationDate: 'Ngày xuất bản',
        ISBN: 'ISBN',
        category: 'Thể loại',
      },
      webPage: {
        title: 'Tiêu đề',
        url: 'URL',
        language: 'Ngôn ngữ',
        authorPublisher: 'Tác giả/Nhà xuất bản',
        publishDate: 'Ngày xuất bản',
        topicKeywords: 'Chủ đề/Từ khóa',
        description: 'Mô tả',
      },
      paper: {
        title: 'Tiêu đề',
        language: 'Ngôn ngữ',
        author: 'Tác giả',
        publishDate: 'Ngày xuất bản',
        journalConferenceName: 'Tên tạp chí/Hội nghị',
        volumeIssuePage: 'Tập/Số/Trang',
        DOI: 'DOI',
        topicsKeywords: 'Chủ đề/Từ khóa',
        abstract: 'Tóm tắt',
      },
      socialMediaPost: {
        platform: 'Nền tảng',
        authorUsername: 'Tác giả/Tên người dùng',
        publishDate: 'Ngày đăng',
        postURL: 'URL bài đăng',
        topicsTags: 'Chủ đề/Thẻ',
      },
      personalDocument: {
        title: 'Tiêu đề',
        author: 'Tác giả',
        creationDate: 'Ngày tạo',
        lastModifiedDate: 'Ngày sửa đổi cuối',
        documentType: 'Loại tài liệu',
        tagsCategory: 'Thẻ/Danh mục',
      },
      businessDocument: {
        title: 'Tiêu đề',
        author: 'Tác giả',
        creationDate: 'Ngày tạo',
        lastModifiedDate: 'Ngày sửa đổi cuối',
        documentType: 'Loại tài liệu',
        departmentTeam: 'Phòng ban/Nhóm',
      },
      IMChat: {
        chatPlatform: 'Nền tảng trò chuyện',
        chatPartiesGroupName: 'Người tham gia/Tên nhóm',
        participants: 'Người tham gia',
        startDate: 'Ngày bắt đầu',
        endDate: 'Ngày kết thúc',
        topicsKeywords: 'Chủ đề/Từ khóa',
        fileType: 'Loại tệp',
      },
      wikipediaEntry: {
        title: 'Tiêu đề',
        language: 'Ngôn ngữ',
        webpageURL: 'URL trang web',
        editorContributor: 'Biên tập viên/Người đóng góp',
        lastEditDate: 'Ngày chỉnh sửa cuối',
        summaryIntroduction: 'Tóm tắt/Giới thiệu',
      },
      notion: {
        title: 'Tiêu đề',
        language: 'Ngôn ngữ',
        author: 'Tác giả',
        createdTime: 'Thời gian tạo',
        lastModifiedTime: 'Thời gian sửa đổi cuối',
        url: 'URL',
        tag: 'Thẻ',
        description: 'Mô tả',
      },
      github: {
        repoName: 'Tên kho lưu trữ',
        repoDesc: 'Mô tả kho lưu trữ',
        repoOwner: 'Chủ sở hữu kho lưu trữ',
        fileName: 'Tên tệp',
        filePath: 'Đường dẫn tệp',
        programmingLang: 'Ngôn ngữ lập trình',
        url: 'URL',
        license: 'Giấy phép',
        lastCommitTime: 'Thời gian commit cuối',
        lastCommitAuthor: 'Tác giả commit cuối',
      },
      originInfo: {
        originalFilename: 'Tên tệp gốc',
        originalFileSize: 'Kích thước tệp gốc',
        uploadDate: 'Ngày tải lên',
        lastUpdateDate: 'Ngày cập nhật cuối',
        source: 'Nguồn',
      },
      technicalParameters: {
        segmentSpecification: 'Đặc tả đoạn',
        segmentLength: 'Độ dài đoạn',
        avgParagraphLength: 'Độ dài trung bình đoạn văn',
        paragraphs: 'Số đoạn văn',
        hitCount: 'Số lần truy vấn',
        embeddingTime: 'Thời gian nhúng',
        embeddedSpend: 'Chi phí nhúng',
      },
    },
    languageMap: {
      zh: 'Tiếng Trung',
      en: 'Tiếng Anh',
      es: 'Tiếng Tây Ban Nha',
      fr: 'Tiếng Pháp',
      de: 'Tiếng Đức',
      ja: 'Tiếng Nhật',
      ko: 'Tiếng Hàn',
      ru: 'Tiếng Nga',
      ar: 'Tiếng Ả Rập',
      pt: 'Tiếng Bồ Đào Nha',
      it: 'Tiếng Ý',
      nl: 'Tiếng Hà Lan',
      pl: 'Tiếng Ba Lan',
      sv: 'Tiếng Thụy Điển',
      tr: 'Tiếng Thổ Nhĩ Kỳ',
      he: 'Tiếng Do Thái',
      hi: 'Tiếng Hindi',
      da: 'Tiếng Đan Mạch',
      fi: 'Tiếng Phần Lan',
      no: 'Tiếng Na Uy',
      hu: 'Tiếng Hungary',
      el: 'Tiếng Hy Lạp',
      cs: 'Tiếng Séc',
      th: 'Tiếng Thái',
      id: 'Tiếng Indonesia',
    },
    categoryMap: {
      book: {
        fiction: 'Tiểu thuyết',
        biography: 'Tiểu sử',
        history: 'Lịch sử',
        science: 'Khoa học',
        technology: 'Công nghệ',
        education: 'Giáo dục',
        philosophy: 'Triết học',
        religion: 'Tôn giáo',
        socialSciences: 'Khoa học xã hội',
        art: 'Nghệ thuật',
        travel: 'Du lịch',
        health: 'Sức khỏe',
        selfHelp: 'Tự lực',
        businessEconomics: 'Kinh doanh và kinh tế',
        cooking: 'Nấu ăn',
        childrenYoungAdults: 'Thiếu nhi và thanh thiếu niên',
        comicsGraphicNovels: 'Truyện tranh và tiểu thuyết đồ họa',
        poetry: 'Thơ ca',
        drama: 'Kịch',
        other: 'Khác',
      },
      personalDoc: {
        notes: 'Ghi chú',
        blogDraft: 'Bản nháp blog',
        diary: 'Nhật ký',
        researchReport: 'Báo cáo nghiên cứu',
        bookExcerpt: 'Trích đoạn sách',
        schedule: 'Lịch trình',
        list: 'Danh sách',
        projectOverview: 'Tổng quan dự án',
        photoCollection: 'Bộ sưu tập ảnh',
        creativeWriting: 'Viết sáng tạo',
        codeSnippet: 'Đoạn mã',
        designDraft: 'Bản phác thảo thiết kế',
        personalResume: 'Sơ yếu lý lịch cá nhân',
        other: 'Khác',
      },
      businessDoc: {
        meetingMinutes: 'Biên bản cuộc họp',
        researchReport: 'Báo cáo nghiên cứu',
        proposal: 'Đề xuất',
        employeeHandbook: 'Sổ tay nhân viên',
        trainingMaterials: 'Tài liệu đào tạo',
        requirementsDocument: 'Tài liệu yêu cầu',
        designDocument: 'Tài liệu thiết kế',
        productSpecification: 'Thông số kỹ thuật sản phẩm',
        financialReport: 'Báo cáo tài chính',
        marketAnalysis: 'Phân tích thị trường',
        projectPlan: 'Kế hoạch dự án',
        teamStructure: 'Cấu trúc nhóm',
        policiesProcedures: 'Chính sách và quy trình',
        contractsAgreements: 'Hợp đồng và thỏa thuận',
        emailCorrespondence: 'Thư từ trao đổi',
        other: 'Khác',
      },
    },
  },
  embedding: {
    processing: 'Đang nhúng...',
    paused: 'Đã tạm dừng nhúng',
    completed: 'Hoàn tất nhúng',
    error: 'Lỗi khi nhúng',
    docName: 'Đang xử lý văn bản',
    mode: 'Quy tắc phân đoạn',
    segmentLength: 'Độ dài đoạn',
    textCleaning: 'Định nghĩa và làm sạch văn bản',
    segments: 'Đoạn',
    highQuality: 'Chế độ chất lượng cao',
    economy: 'Chế độ tiết kiệm',
    estimate: 'Ước tính tiêu thụ',
    stop: 'Dừng xử lý',
    resume: 'Tiếp tục xử lý',
    automatic: 'Tự động',
    custom: 'Tùy chỉnh',
    previewTip: 'Xem trước đoạn sẽ có sẵn sau khi việc nhúng hoàn tất',
    parentMaxTokens: 'Cha mẹ',
    pause: 'Tạm dừng',
    childMaxTokens: 'Con',
    hierarchical: 'Cha mẹ-con cái',
  },
  segment: {
    paragraphs: 'Đoạn văn',
    keywords: 'Từ khóa',
    addKeyWord: 'Thêm từ khóa',
    keywordError: 'Độ dài tối đa của từ khóa là 20',
    characters: 'ký tự',
    hitCount: 'Số lần truy vấn',
    vectorHash: 'Mã băm vector: ',
    questionPlaceholder: 'thêm câu hỏi ở đây',
    questionEmpty: 'Câu hỏi không thể trống',
    answerPlaceholder: 'thêm câu trả lời ở đây',
    answerEmpty: 'Câu trả lời không thể trống',
    contentPlaceholder: 'thêm nội dung ở đây',
    contentEmpty: 'Nội dung không thể trống',
    newTextSegment: 'Đoạn văn bản mới',
    newQaSegment: 'Đoạn hỏi đáp mới',
    delete: 'Xóa đoạn này?',
    childChunks_one: 'KHỐI TRẺ',
    searchResults_zero: 'KẾT QUẢ',
    empty: 'Không tìm thấy Chunk',
    newChunk: 'Khối mới',
    childChunk: 'Khối trẻ em',
    regeneratingMessage: 'Quá trình này có thể mất một lúc, vui lòng đợi...',
    regenerationSuccessMessage: 'Bạn có thể đóng cửa sổ này.',
    regenerationSuccessTitle: 'Hoàn thành tái tạo',
    characters_other: 'Ký tự',
    chunks_one: 'KHÚC',
    chunkAdded: '1 miếng được thêm vào',
    editChildChunk: 'Chỉnh sửa phần con',
    characters_one: 'nhân vật',
    expandChunks: 'Mở rộng các đoạn',
    chunks_other: 'KHỐI',
    editedAt: 'Chỉnh sửa tại',
    dateTimeFormat: 'DD/MM/YYYY HH:mm',
    addAnother: 'Thêm một cái khác',
    regenerationConfirmTitle: 'Bạn có muốn tái tạo các chunk con không?',
    searchResults_one: 'KẾT QUẢ',
    regeneratingTitle: 'Tái tạo các chunk con',
    editParentChunk: 'Chỉnh sửa phần cha',
    collapseChunks: 'Thu gọn các đoạn',
    searchResults_other: 'KẾT QUẢ',
    parentChunks_one: 'PHẦN CHA MẸ',
    newChildChunk: 'Khối con mới',
    parentChunk: 'Phần cha mẹ',
    parentChunks_other: 'PHẦN CHA MẸ',
    regenerationConfirmMessage: 'Tạo lại các chunk con sẽ ghi đè lên các chunk con hiện tại, bao gồm các chunk đã chỉnh sửa và chunk mới được thêm vào. Sự tái sinh không thể hoàn tác.',
    childChunkAdded: '1 phần con được thêm vào',
    addChunk: 'Thêm Chunk',
    chunkDetail: 'Chi tiết khối',
    childChunks_other: 'KHỐI CON',
    editChunk: 'Chỉnh sửa Chunk',
    addChildChunk: 'Thêm phần con',
    clearFilter: 'Bộ lọc rõ ràng',
    chunk: 'Khúc',
    edited: 'EDITED',
    keywordDuplicate: 'Từ khóa đã tồn tại',
    keywordEmpty: 'Từ khóa không được để trống',
  },
}

export default translation
