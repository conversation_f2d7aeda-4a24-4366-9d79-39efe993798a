#!/bin/bash

# Dify 应用服务启动脚本

set -e

echo "=== Dify 应用服务启动脚本 ==="

# 检查必要文件
if [ ! -f "docker-compose-app.yaml" ]; then
    echo "错误: 找不到 docker-compose-app.yaml 文件"
    exit 1
fi

if [ ! -f ".env-app" ]; then
    echo "错误: 找不到 .env-app 文件"
    exit 1
fi

# 检查数据库连接配置
DB_HOST=$(grep "^DB_HOST=" .env-app | cut -d'=' -f2 | tr -d '"')
REDIS_HOST=$(grep "^REDIS_HOST=" .env-app | cut -d'=' -f2 | tr -d '"')
VECTOR_STORE=$(grep "^VECTOR_STORE=" .env-app | cut -d'=' -f2 | tr -d '"')
MILVUS_URI=$(grep "^MILVUS_URI=" .env-app | cut -d'=' -f2 | tr -d '"')

if [ "$DB_HOST" = "localhost" ] || [ "$DB_HOST" = "db" ]; then
    echo "警告: DB_HOST 仍然设置为 '$DB_HOST'"
    echo "请修改 .env-app 文件中的 DB_HOST 为实际的数据库服务器地址"
    read -p "是否继续启动? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

if [ "$REDIS_HOST" = "localhost" ] || [ "$REDIS_HOST" = "redis" ]; then
    echo "警告: REDIS_HOST 仍然设置为 '$REDIS_HOST'"
    echo "请修改 .env-app 文件中的 REDIS_HOST 为实际的 Redis 服务器地址"
    read -p "是否继续启动? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

if [ "$VECTOR_STORE" = "milvus" ] && [[ "$MILVUS_URI" == *"localhost"* ]]; then
    echo "警告: MILVUS_URI 仍然设置为 '$MILVUS_URI'"
    echo "请修改 .env-app 文件中的 MILVUS_URI 为实际的 Milvus 服务器地址"
    read -p "是否继续启动? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "数据库服务器: $DB_HOST"
echo "Redis 服务器: $REDIS_HOST"
echo "向量数据库: $VECTOR_STORE"
if [ "$VECTOR_STORE" = "milvus" ]; then
    echo "Milvus 地址: $MILVUS_URI"
fi

# 创建必要的目录
echo "创建应用目录..."
mkdir -p volumes/app/storage
mkdir -p volumes/sandbox/dependencies
mkdir -p volumes/sandbox/conf
mkdir -p volumes/plugin_daemon
mkdir -p volumes/certbot/conf
mkdir -p volumes/certbot/www
mkdir -p volumes/certbot/logs

# 测试数据库连接
echo "测试数据库连接..."
if command -v nc >/dev/null 2>&1; then
    if ! nc -z $DB_HOST 5432; then
        echo "错误: 无法连接到数据库服务器 $DB_HOST:5432"
        echo "请检查:"
        echo "1. 数据库服务器是否已启动"
        echo "2. 防火墙是否允许连接"
        echo "3. 网络连接是否正常"
        exit 1
    fi
    echo "数据库连接测试成功"
else
    echo "跳过数据库连接测试 (nc 命令不可用)"
fi

# 测试 Redis 连接
echo "测试 Redis 连接..."
if command -v nc >/dev/null 2>&1; then
    if ! nc -z $REDIS_HOST 6379; then
        echo "错误: 无法连接到 Redis 服务器 $REDIS_HOST:6379"
        echo "请检查:"
        echo "1. Redis 服务器是否已启动"
        echo "2. 防火墙是否允许连接"
        echo "3. 网络连接是否正常"
        exit 1
    fi
    echo "Redis 连接测试成功"
else
    echo "跳过 Redis 连接测试 (nc 命令不可用)"
fi

# 测试 Milvus 连接
if [ "$VECTOR_STORE" = "milvus" ]; then
    echo "测试 Milvus 连接..."
    MILVUS_HOST=$(echo $MILVUS_URI | sed 's|http://||' | sed 's|https://||' | cut -d':' -f1)
    MILVUS_PORT=$(echo $MILVUS_URI | sed 's|http://||' | sed 's|https://||' | cut -d':' -f2)
    if [ -z "$MILVUS_PORT" ]; then
        MILVUS_PORT=19530
    fi

    if command -v nc >/dev/null 2>&1; then
        if ! nc -z $MILVUS_HOST $MILVUS_PORT; then
            echo "错误: 无法连接到 Milvus 服务器 $MILVUS_HOST:$MILVUS_PORT"
            echo "请检查:"
            echo "1. Milvus 服务器是否已启动"
            echo "2. 防火墙是否允许连接"
            echo "3. 网络连接是否正常"
            exit 1
        fi
        echo "Milvus 连接测试成功"
    else
        echo "跳过 Milvus 连接测试 (nc 命令不可用)"
    fi
fi

# 启动基础服务
echo "启动 SSRF 代理..."
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d ssrf_proxy

echo "启动 Sandbox..."
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d sandbox

# 等待基础服务启动
sleep 5

echo "启动核心应用服务..."
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d api worker worker_beat

# 等待 API 服务启动
echo "等待 API 服务启动..."
sleep 15

echo "启动 Web 服务..."
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d web

echo "启动插件服务..."
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d plugin_daemon

echo "启动 Nginx..."
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d nginx

# 检查服务状态
echo "检查服务状态..."
docker-compose -f docker-compose-app.yaml ps

# 等待服务完全启动
echo "等待服务完全启动..."
sleep 10

# 检查 API 健康状态
echo "检查 API 服务健康状态..."
if command -v curl >/dev/null 2>&1; then
    if curl -f http://localhost/health >/dev/null 2>&1; then
        echo "API 服务健康检查通过"
    else
        echo "警告: API 服务健康检查失败，请查看日志"
        docker-compose -f docker-compose-app.yaml logs api | tail -20
    fi
else
    echo "跳过 API 健康检查 (curl 命令不可用)"
fi

echo "=== 应用服务启动完成 ==="
echo ""
echo "服务访问地址:"
echo "- Web 界面: http://localhost"
echo "- API 文档: http://localhost/docs"
echo ""
echo "如果遇到问题，请查看日志:"
echo "docker-compose -f docker-compose-app.yaml logs [service_name]"
echo ""
echo "常用命令:"
echo "- 查看所有服务状态: docker-compose -f docker-compose-app.yaml ps"
echo "- 查看 API 日志: docker-compose -f docker-compose-app.yaml logs api"
echo "- 查看 Worker 日志: docker-compose -f docker-compose-app.yaml logs worker"
echo "- 重启服务: docker-compose -f docker-compose-app.yaml restart [service_name]"
