# Dify 使用 Milvus 向量数据库配置说明

## Milvus 简介

Milvus 是一个开源的向量数据库，专为处理大规模向量数据而设计。它支持多种向量索引算法，提供高性能的向量相似性搜索功能。

## 架构说明

Milvus 在分离部署中包含以下组件：

1. **Milvus Server**: 主要的向量数据库服务
2. **etcd**: 元数据存储，用于服务发现和配置管理
3. **MinIO**: 对象存储，用于存储向量数据和索引文件

## 配置文件说明

### 数据服务配置 (.env-data)

```bash
# Milvus 相关配置
ETCD_AUTO_COMPACTION_MODE=revision
ETCD_AUTO_COMPACTION_RETENTION=1000
ETCD_QUOTA_BACKEND_BYTES=4294967296
ETCD_SNAPSHOT_COUNT=50000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
ETCD_ENDPOINTS=etcd:2379
MINIO_ADDRESS=minio:9000
MILVUS_AUTHORIZATION_ENABLED=true

# 端口配置
EXPOSE_MILVUS_PORT=19530        # Milvus 主服务端口
EXPOSE_MILVUS_WEB_PORT=9091     # Milvus Web 管理界面
EXPOSE_MINIO_PORT=9001          # MinIO 控制台
EXPOSE_MINIO_API_PORT=9000      # MinIO API 端口

# 设置使用 Milvus
COMPOSE_PROFILES=milvus
```

### 应用服务配置 (.env-app)

```bash
# 向量数据库配置
VECTOR_STORE=milvus
MILVUS_URI=http://数据库服务器IP:19530  # 修改为实际地址
MILVUS_DATABASE=                        # 可选，数据库名称
MILVUS_TOKEN=                          # 可选，认证令牌
MILVUS_USER=                           # 可选，用户名
MILVUS_PASSWORD=                       # 可选，密码
MILVUS_ENABLE_HYBRID_SEARCH=False      # 是否启用混合搜索
MILVUS_ANALYZER_PARAMS=                # 分析器参数
```

## 部署步骤

### 1. 数据服务器部署

```bash
# 1. 确保 .env-data 中设置了 COMPOSE_PROFILES=milvus

# 2. 创建必要的目录
mkdir -p volumes/etcd
mkdir -p volumes/minio
mkdir -p volumes/milvus

# 3. 启动 Milvus 相关服务
docker-compose -f docker-compose-data.yaml --env-file .env-data --profile milvus up -d

# 4. 检查服务状态
docker-compose -f docker-compose-data.yaml ps
```

### 2. 应用服务器配置

```bash
# 修改 .env-app 文件
VECTOR_STORE=milvus
MILVUS_URI=http://*************:19530  # 替换为实际的数据库服务器 IP
```

## 端口说明

| 服务 | 端口 | 说明 |
|------|------|------|
| Milvus | 19530 | 主服务端口，应用连接此端口 |
| Milvus Web | 9091 | Web 管理界面（可选） |
| MinIO API | 9000 | 对象存储 API |
| MinIO Console | 9001 | MinIO 管理控制台（可选） |
| etcd | 2379 | 元数据存储（内部使用） |

## 防火墙配置

在数据库服务器上开放以下端口：

```bash
# 必需端口
sudo ufw allow 19530  # Milvus 主服务
sudo ufw allow 9000   # MinIO API

# 可选端口（如果需要 Web 管理界面）
sudo ufw allow 9091   # Milvus Web 界面
sudo ufw allow 9001   # MinIO 控制台
```

## 验证部署

### 1. 检查服务状态

```bash
# 检查所有服务是否正常运行
docker-compose -f docker-compose-data.yaml ps

# 检查 Milvus 健康状态
curl -f http://localhost:9091/healthz

# 检查 MinIO 健康状态
curl -f http://localhost:9000/minio/health/live
```

### 2. 从应用服务器测试连接

```bash
# 测试 Milvus 连接
nc -z 数据库服务器IP 19530

# 测试 MinIO 连接
nc -z 数据库服务器IP 9000
```

## 性能优化建议

### 1. 硬件要求

- **CPU**: 建议 4 核以上
- **内存**: 建议 8GB 以上
- **存储**: 建议使用 SSD，至少 100GB 可用空间
- **网络**: 建议千兆网络

### 2. 配置优化

```bash
# 在 .env-data 中调整以下参数

# etcd 性能优化
ETCD_QUOTA_BACKEND_BYTES=**********  # 增加到 8GB
ETCD_SNAPSHOT_COUNT=100000           # 增加快照计数

# MinIO 性能优化
# 可以考虑使用外部高性能对象存储
```

### 3. 监控建议

- 监控 Milvus 服务状态：`http://数据库服务器IP:9091/healthz`
- 监控 MinIO 存储使用情况：`http://数据库服务器IP:9001`
- 监控系统资源使用情况（CPU、内存、磁盘）

## 常见问题

### 1. Milvus 启动失败

**问题**: Milvus 容器启动失败
**解决方案**:
```bash
# 检查依赖服务是否正常
docker-compose -f docker-compose-data.yaml logs etcd
docker-compose -f docker-compose-data.yaml logs minio

# 检查 Milvus 日志
docker-compose -f docker-compose-data.yaml logs milvus
```

### 2. 连接超时

**问题**: 应用无法连接到 Milvus
**解决方案**:
- 检查防火墙设置
- 确认 MILVUS_URI 配置正确
- 检查网络连通性

### 3. 存储空间不足

**问题**: MinIO 存储空间不足
**解决方案**:
- 清理不需要的数据
- 扩展存储空间
- 配置数据清理策略

## 数据备份

### 1. 备份 Milvus 数据

```bash
# 备份 MinIO 数据
docker exec minio_container mc mirror /minio_data /backup/minio_data

# 备份 etcd 数据
docker exec etcd_container etcdctl snapshot save /backup/etcd_snapshot.db
```

### 2. 恢复数据

```bash
# 恢复 MinIO 数据
docker exec minio_container mc mirror /backup/minio_data /minio_data

# 恢复 etcd 数据
docker exec etcd_container etcdctl snapshot restore /backup/etcd_snapshot.db
```

## 升级说明

升级 Milvus 时需要注意：

1. 备份现有数据
2. 检查版本兼容性
3. 按顺序停止服务：Milvus → MinIO → etcd
4. 更新镜像版本
5. 按顺序启动服务：etcd → MinIO → Milvus
6. 验证服务正常运行

## 更多资源

- [Milvus 官方文档](https://milvus.io/docs)
- [MinIO 官方文档](https://docs.min.io/)
- [etcd 官方文档](https://etcd.io/docs/)
