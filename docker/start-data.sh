#!/bin/bash

# Dify 数据服务启动脚本

set -e

echo "=== Dify 数据服务启动脚本 ==="

# 检查必要文件
if [ ! -f "docker-compose-data.yaml" ]; then
    echo "错误: 找不到 docker-compose-data.yaml 文件"
    exit 1
fi

if [ ! -f ".env-data" ]; then
    echo "错误: 找不到 .env-data 文件"
    exit 1
fi

# 读取向量数据库类型
VECTOR_STORE=$(grep "^COMPOSE_PROFILES=" .env-data | cut -d'=' -f2 | tr -d '"')
if [ -z "$VECTOR_STORE" ]; then
    VECTOR_STORE="milvus"
fi

echo "向量数据库类型: $VECTOR_STORE"

# 创建必要的目录
echo "创建数据目录..."
mkdir -p volumes/db/data
mkdir -p volumes/redis/data

case $VECTOR_STORE in
    "weaviate")
        mkdir -p volumes/weaviate
        ;;
    "qdrant")
        mkdir -p volumes/qdrant
        ;;
    "pgvector")
        mkdir -p volumes/pgvector/data
        ;;
    "milvus")
        mkdir -p volumes/etcd
        mkdir -p volumes/minio
        mkdir -p volumes/milvus
        ;;
esac

# 启动基础服务
echo "启动 PostgreSQL 和 Redis..."
docker-compose -f docker-compose-data.yaml --env-file .env-data up -d db redis

# 等待数据库启动
echo "等待数据库启动..."
sleep 10

# 启动向量数据库
if [ "$VECTOR_STORE" != "none" ]; then
    echo "启动向量数据库: $VECTOR_STORE"
    docker-compose -f docker-compose-data.yaml --env-file .env-data --profile $VECTOR_STORE up -d
fi

# 检查服务状态
echo "检查服务状态..."
docker-compose -f docker-compose-data.yaml ps

echo "=== 数据服务启动完成 ==="
echo "请确保防火墙已开放以下端口："
echo "- PostgreSQL: 5432"
echo "- Redis: 6379"

case $VECTOR_STORE in
    "weaviate")
        echo "- Weaviate: 8080"
        ;;
    "qdrant")
        echo "- Qdrant: 6333"
        ;;
    "pgvector")
        echo "- pgvector: 5433"
        ;;
    "milvus")
        echo "- Milvus: 19530"
        echo "- Milvus Web: 9091"
        echo "- MinIO: 9000"
        echo "- MinIO Console: 9001"
        ;;
esac

echo ""
echo "数据服务已启动，现在可以在应用服务器上启动应用服务。"
echo "请记得修改应用服务器上的 .env-app 文件中的数据库连接地址。"
