# ==================================================================
# Dify 应用服务环境变量配置
# 用于 API、Worker、Web 等应用相关服务
# ==================================================================

# ------------------------------
# 重要提示：请根据实际部署情况修改以下数据库连接地址
# ------------------------------

# ------------------------------
# 数据库连接配置
# 请修改为实际的数据库服务器地址
# ------------------------------
DB_USERNAME=postgres
DB_PASSWORD=difyai123456
DB_HOST=localhost  # 修改为数据库服务器的实际 IP 地址或域名
DB_PORT=5432
DB_DATABASE=dify

# 数据库连接池配置
SQLALCHEMY_POOL_SIZE=30
SQLALCHEMY_POOL_RECYCLE=3600
SQLALCHEMY_ECHO=false
SQLALCHEMY_POOL_PRE_PING=false
SQLALCHEMY_POOL_USE_LIFO=false

# ------------------------------
# Redis 连接配置
# 请修改为实际的 Redis 服务器地址
# ------------------------------
REDIS_HOST=localhost  # 修改为 Redis 服务器的实际 IP 地址或域名
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=difyai123456
REDIS_USE_SSL=false
REDIS_DB=0

# Redis 高级配置
REDIS_USE_SENTINEL=false
REDIS_SENTINELS=
REDIS_SENTINEL_SERVICE_NAME=
REDIS_SENTINEL_USERNAME=
REDIS_SENTINEL_PASSWORD=
REDIS_SENTINEL_SOCKET_TIMEOUT=0.1
REDIS_USE_CLUSTERS=false
REDIS_CLUSTERS=
REDIS_CLUSTERS_PASSWORD=

# ------------------------------
# Celery 配置
# 请修改 Redis 地址为实际地址
# ------------------------------
CELERY_BROKER_URL=redis://:difyai123456@localhost:6379/1  # 修改 localhost 为实际 Redis 地址
CELERY_BACKEND=redis
BROKER_USE_SSL=false
CELERY_USE_SENTINEL=false
CELERY_SENTINEL_MASTER_NAME=
CELERY_SENTINEL_PASSWORD=
CELERY_SENTINEL_SOCKET_TIMEOUT=0.1

# ------------------------------
# 向量数据库配置
# 请修改为实际的向量数据库服务器地址
# ------------------------------
VECTOR_STORE=milvus
VECTOR_INDEX_NAME_PREFIX=Vector_index

# Milvus 配置
MILVUS_URI=http://localhost:19530  # 修改为实际的 Milvus 服务器地址
MILVUS_DATABASE=
MILVUS_TOKEN=
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_ENABLE_HYBRID_SEARCH=False
MILVUS_ANALYZER_PARAMS=

# Weaviate 配置（备用）
WEAVIATE_ENDPOINT=http://localhost:8080
WEAVIATE_API_KEY=WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih

# Qdrant 配置（备用）
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=difyai123456
QDRANT_CLIENT_TIMEOUT=20
QDRANT_GRPC_ENABLED=false
QDRANT_GRPC_PORT=6334
QDRANT_REPLICATION_FACTOR=1

# pgvector 配置（备用）
PGVECTOR_HOST=localhost
PGVECTOR_PORT=5433
PGVECTOR_USER=postgres
PGVECTOR_PASSWORD=difyai123456
PGVECTOR_DATABASE=dify
PGVECTOR_MIN_CONNECTION=1
PGVECTOR_MAX_CONNECTION=5

# ------------------------------
# 应用基础配置
# ------------------------------
# 前端和 API 地址配置
CONSOLE_API_URL=
CONSOLE_WEB_URL=
SERVICE_API_URL=
APP_API_URL=
APP_WEB_URL=
FILES_URL=
INTERNAL_FILES_URL=

# 语言和编码
LANG=en_US.UTF-8
LC_ALL=en_US.UTF-8
PYTHONIOENCODING=utf-8

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/server.log
LOG_FILE_MAX_SIZE=20
LOG_FILE_BACKUP_COUNT=5
LOG_DATEFORMAT=%Y-%m-%d %H:%M:%S
LOG_TZ=UTC

# 调试配置
DEBUG=false
FLASK_DEBUG=false
ENABLE_REQUEST_LOGGING=False

# 安全配置
SECRET_KEY=************************************************
INIT_PASSWORD=

# 部署环境
DEPLOY_ENV=PRODUCTION
CHECK_UPDATE_URL=https://updates.dify.ai

# OpenAI 配置
OPENAI_API_BASE=https://api.openai.com/v1

# 迁移配置
MIGRATION_ENABLED=true

# 文件访问配置
FILES_ACCESS_TIMEOUT=300
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=30

# 应用限制配置
APP_MAX_ACTIVE_REQUESTS=0
APP_MAX_EXECUTION_TIME=1200

# ------------------------------
# 服务器配置
# ------------------------------
DIFY_BIND_ADDRESS=0.0.0.0
DIFY_PORT=5001
SERVER_WORKER_AMOUNT=1
SERVER_WORKER_CLASS=gevent
SERVER_WORKER_CONNECTIONS=10
CELERY_WORKER_CLASS=
GUNICORN_TIMEOUT=360
CELERY_WORKER_AMOUNT=
CELERY_AUTO_SCALE=false
CELERY_MAX_WORKERS=
CELERY_MIN_WORKERS=

# API 工具配置
API_TOOL_DEFAULT_CONNECT_TIMEOUT=10
API_TOOL_DEFAULT_READ_TIMEOUT=60

# 数据源配置
ENABLE_WEBSITE_JINAREADER=true
ENABLE_WEBSITE_FIRECRAWL=true
ENABLE_WEBSITE_WATERCRAWL=true

# ------------------------------
# CORS 配置
# ------------------------------
WEB_API_CORS_ALLOW_ORIGINS=*
CONSOLE_CORS_ALLOW_ORIGINS=*

# ------------------------------
# 存储配置
# ------------------------------
STORAGE_TYPE=opendal
OPENDAL_SCHEME=fs
OPENDAL_FS_ROOT=storage

# ------------------------------
# 代码执行配置
# ------------------------------
CODE_EXECUTION_ENDPOINT=http://sandbox:8194
CODE_EXECUTION_API_KEY=dify-sandbox

# ------------------------------
# SSRF 代理配置
# ------------------------------
SSRF_PROXY_HTTP_URL=http://ssrf_proxy:3128
SSRF_PROXY_HTTPS_URL=http://ssrf_proxy:3128

# ------------------------------
# 插件配置
# ------------------------------
DB_PLUGIN_DATABASE=dify_plugin
PLUGIN_DAEMON_PORT=5002
PLUGIN_DAEMON_KEY=lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi
PLUGIN_DAEMON_URL=http://plugin_daemon:5002
PLUGIN_MAX_PACKAGE_SIZE=52428800
PLUGIN_PPROF_ENABLED=false
PLUGIN_DEBUGGING_HOST=0.0.0.0
PLUGIN_DEBUGGING_PORT=5003
EXPOSE_PLUGIN_DEBUGGING_HOST=localhost
EXPOSE_PLUGIN_DEBUGGING_PORT=5003
PLUGIN_DIFY_INNER_API_KEY=QaHbTe77CtuXmsfyhR7+vRjI/+XbV1AaFy691iy+kGDv2Jvy0/eAh8Y1
PLUGIN_DIFY_INNER_API_URL=http://api:5001

# ------------------------------
# Sandbox 配置
# ------------------------------
SANDBOX_API_KEY=dify-sandbox
SANDBOX_GIN_MODE=release
SANDBOX_WORKER_TIMEOUT=15
SANDBOX_ENABLE_NETWORK=true
SANDBOX_HTTP_PROXY=http://ssrf_proxy:3128
SANDBOX_HTTPS_PROXY=http://ssrf_proxy:3128
SANDBOX_PORT=8194
PIP_MIRROR_URL=

# ------------------------------
# Nginx 配置
# ------------------------------
NGINX_SERVER_NAME=_
NGINX_HTTPS_ENABLED=false
NGINX_PORT=80
NGINX_SSL_PORT=443
NGINX_SSL_CERT_FILENAME=dify.crt
NGINX_SSL_CERT_KEY_FILENAME=dify.key
NGINX_SSL_PROTOCOLS=TLSv1.1 TLSv1.2 TLSv1.3
NGINX_WORKER_PROCESSES=auto
NGINX_CLIENT_MAX_BODY_SIZE=100M
NGINX_KEEPALIVE_TIMEOUT=65
NGINX_PROXY_READ_TIMEOUT=3600s
NGINX_PROXY_SEND_TIMEOUT=3600s
NGINX_ENABLE_CERTBOT_CHALLENGE=false

# ------------------------------
# SSRF 代理配置
# ------------------------------
SSRF_HTTP_PORT=3128
SSRF_COREDUMP_DIR=/var/spool/squid
SSRF_REVERSE_PROXY_PORT=8194
SSRF_SANDBOX_HOST=sandbox

# ------------------------------
# 端口暴露配置
# ------------------------------
EXPOSE_NGINX_PORT=8102
EXPOSE_NGINX_SSL_PORT=443

# ------------------------------
# 其他配置
# ------------------------------
TEXT_GENERATION_TIMEOUT_MS=60000
ALLOW_UNSAFE_DATA_SCHEME=false
ALLOW_EMBED=false
CSP_WHITELIST=
MARKETPLACE_API_URL=https://marketplace.dify.ai
MARKETPLACE_URL=https://marketplace.dify.ai
NEXT_TELEMETRY_DISABLED=0
