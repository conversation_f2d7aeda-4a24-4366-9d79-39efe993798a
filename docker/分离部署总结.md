# Dify 分离部署配置总结

## 已创建的文件

### 1. Docker Compose 配置文件
- **`docker-compose-data.yaml`** - 数据服务配置
  - PostgreSQL 数据库
  - Redis 缓存
  - Weaviate/Qdrant/pgvector 向量数据库（可选）
  - 暴露端口供外部访问

- **`docker-compose-app.yaml`** - 应用服务配置
  - API 服务
  - Worker 服务
  - Worker Beat 调度服务
  - Web 前端服务
  - Sandbox 代码执行服务
  - Plugin Daemon 插件服务
  - SSRF Proxy 安全代理
  - Nginx 反向代理

### 2. 环境变量配置文件
- **`.env-data`** - 数据服务环境变量
  - 数据库配置
  - Redis 配置
  - 向量数据库配置
  - 端口暴露设置

- **`.env-app`** - 应用服务环境变量
  - 数据库连接配置（需要修改为实际地址）
  - Redis 连接配置（需要修改为实际地址）
  - 向量数据库连接配置（需要修改为实际地址）
  - 应用相关配置

### 3. 启动脚本
- **`start-data.sh`** - 数据服务启动脚本
- **`start-app.sh`** - 应用服务启动脚本

### 4. 文档
- **`README-分离部署.md`** - 详细的部署指南

## 主要改动说明

### 1. 服务分离
- **数据服务**: 包含所有数据存储相关的服务
- **应用服务**: 包含所有应用逻辑相关的服务

### 2. 网络配置
- 数据服务暴露必要端口供应用服务连接
- 应用服务移除了对数据服务的 depends_on 依赖
- 修改了服务间的连接地址配置

### 3. 环境变量优化
- 分离了数据服务和应用服务的环境变量
- 简化了不必要的配置项
- 添加了清晰的配置说明和注释

### 4. 删除的不必要服务
从原始配置中移除了以下可选服务（可根据需要添加回来）：
- Certbot（SSL 证书服务）
- 大部分向量数据库服务（保留了常用的几个）
- 一些特定的数据库服务

## 快速部署步骤

### 数据服务器部署
```bash
# 1. 复制文件到数据服务器
cp docker-compose-data.yaml .env-data /path/to/data-server/

# 2. 启动数据服务
cd /path/to/data-server/
./start-data.sh
# 或手动启动
docker-compose -f docker-compose-data.yaml --env-file .env-data up -d
```

### 应用服务器部署
```bash
# 1. 复制文件到应用服务器
cp docker-compose-app.yaml .env-app nginx/ ssrf_proxy/ volumes/ /path/to/app-server/

# 2. 修改 .env-app 中的数据库连接地址
# 重要：修改以下配置项
# DB_HOST=数据库服务器IP
# REDIS_HOST=Redis服务器IP
# MILVUS_URI=http://向量数据库服务器IP:19530

# 3. 启动应用服务
cd /path/to/app-server/
./start-app.sh
# 或手动启动
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d
```

## 重要注意事项

### 1. 必须修改的配置
在 `.env-app` 文件中，必须修改以下配置为实际的服务器地址：
- `DB_HOST=localhost` → `DB_HOST=数据库服务器IP`
- `REDIS_HOST=localhost` → `REDIS_HOST=Redis服务器IP`
- `CELERY_BROKER_URL` 中的 Redis 地址
- 向量数据库的连接地址

### 2. 防火墙配置
确保数据库服务器开放以下端口：
- PostgreSQL: 5432
- Redis: 6379
- Milvus: 19530（默认使用）
- Milvus Web: 9091（如果需要 Web 管理界面）
- MinIO: 9000（Milvus 依赖的对象存储）
- MinIO Console: 9001（如果需要 MinIO 管理界面）
- Weaviate: 8080（如果使用）
- Qdrant: 6333（如果使用）

### 3. 安全建议
- 修改默认密码
- 配置防火墙规则
- 使用私有网络连接
- 定期备份数据

## 故障排查

### 常见问题
1. **连接失败**: 检查网络连通性和防火墙设置
2. **服务启动失败**: 查看 Docker 日志
3. **数据库连接错误**: 确认数据库服务已启动且配置正确

### 查看日志
```bash
# 数据服务日志
docker-compose -f docker-compose-data.yaml logs

# 应用服务日志
docker-compose -f docker-compose-app.yaml logs api
docker-compose -f docker-compose-app.yaml logs worker
```

## 扩展说明

这个分离配置支持：
- 独立扩展应用服务和数据服务
- 多个应用服务实例连接同一个数据服务
- 不同的向量数据库选择
- 灵活的部署架构

如需要其他特定配置或遇到问题，请参考详细的部署指南文档。
