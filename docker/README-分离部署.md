# Dify 分离部署指南

本指南将帮助您将 Dify 的数据服务和应用服务分离部署到不同的机器上。

## 文件说明

- `docker-compose-data.yaml`: 数据服务配置文件（数据库、缓存、向量数据库）
- `docker-compose-app.yaml`: 应用服务配置文件（API、Worker、Web、Nginx 等）
- `.env-data`: 数据服务环境变量配置
- `.env-app`: 应用服务环境变量配置

## 部署步骤

### 1. 数据服务部署（数据库服务器）

#### 1.1 准备文件
将以下文件复制到数据库服务器：
```bash
docker-compose-data.yaml
.env-data
volumes/  # 数据持久化目录
```

#### 1.2 配置环境变量
编辑 `.env-data` 文件，根据需要调整：
- 数据库密码
- Redis 密码
- 向量数据库配置
- 端口暴露设置

#### 1.3 启动数据服务
```bash
# 启动基础数据服务（PostgreSQL + Redis）
docker-compose -f docker-compose-data.yaml --env-file .env-data up -d db redis

# 如果使用 Weaviate 向量数据库
docker-compose -f docker-compose-data.yaml --env-file .env-data --profile weaviate up -d

# 如果使用 Qdrant 向量数据库
docker-compose -f docker-compose-data.yaml --env-file .env-data --profile qdrant up -d

# 如果使用 pgvector 向量数据库
docker-compose -f docker-compose-data.yaml --env-file .env-data --profile pgvector up -d

# 如果使用 Milvus 向量数据库
docker-compose -f docker-compose-data.yaml --env-file .env-data --profile milvus up -d
```

#### 1.4 验证数据服务
```bash
# 检查服务状态
docker-compose -f docker-compose-data.yaml ps

# 测试数据库连接
docker exec -it <postgres_container_name> psql -U postgres -d dify -c "SELECT 1;"

# 测试 Redis 连接
docker exec -it <redis_container_name> redis-cli ping
```

### 2. 应用服务部署（应用服务器）

#### 2.1 准备文件
将以下文件复制到应用服务器：
```bash
docker-compose-app.yaml
.env-app
nginx/          # Nginx 配置目录
ssrf_proxy/     # SSRF 代理配置目录
volumes/app/    # 应用数据目录
volumes/sandbox/    # Sandbox 配置目录
volumes/plugin_daemon/  # 插件目录
volumes/certbot/    # SSL 证书目录（如果需要）
```

#### 2.2 配置环境变量
**重要：** 编辑 `.env-app` 文件，修改以下关键配置：

```bash
# 数据库连接 - 修改为数据库服务器的实际地址
DB_HOST=*************  # 替换为数据库服务器 IP
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=difyai123456
DB_DATABASE=dify

# Redis 连接 - 修改为 Redis 服务器的实际地址
REDIS_HOST=*************  # 替换为 Redis 服务器 IP
REDIS_PORT=6379
REDIS_PASSWORD=difyai123456

# Celery Broker - 修改 Redis 地址
CELERY_BROKER_URL=redis://:difyai123456@*************:6379/1

# 向量数据库连接 - 根据使用的向量数据库类型修改
VECTOR_STORE=milvus
MILVUS_URI=http://*************:19530  # 替换为 Milvus 服务器 IP

# 或者如果使用 Weaviate
# VECTOR_STORE=weaviate
# WEAVIATE_ENDPOINT=http://*************:8080

# 或者如果使用 Qdrant
# VECTOR_STORE=qdrant
# QDRANT_URL=http://*************:6333

# 或者如果使用 pgvector
# VECTOR_STORE=pgvector
# PGVECTOR_HOST=*************
# PGVECTOR_PORT=5433
```

#### 2.3 启动应用服务
```bash
# 启动所有应用服务
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d

# 或者分步启动
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d ssrf_proxy
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d sandbox
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d api worker worker_beat
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d web
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d plugin_daemon
docker-compose -f docker-compose-app.yaml --env-file .env-app up -d nginx
```

#### 2.4 验证应用服务
```bash
# 检查服务状态
docker-compose -f docker-compose-app.yaml ps

# 查看日志
docker-compose -f docker-compose-app.yaml logs api
docker-compose -f docker-compose-app.yaml logs worker
```

### 3. 网络配置注意事项

#### 3.1 防火墙配置
确保数据库服务器的防火墙允许应用服务器访问以下端口：
- PostgreSQL: 5432
- Redis: 6379
- Milvus: 19530 (如果使用)
- Milvus Web: 9091 (如果使用)
- MinIO: 9000 (如果使用 Milvus)
- MinIO Console: 9001 (如果使用 Milvus)
- Weaviate: 8080 (如果使用)
- Qdrant: 6333 (如果使用)
- pgvector: 5433 (如果使用)

#### 3.2 安全建议
1. 使用强密码
2. 配置防火墙规则，只允许应用服务器访问数据库端口
3. 考虑使用 VPN 或私有网络连接
4. 定期备份数据库

### 4. 常见问题排查

#### 4.1 连接问题
```bash
# 从应用服务器测试数据库连接
telnet <数据库服务器IP> 5432

# 从应用服务器测试 Redis 连接
telnet <Redis服务器IP> 6379

# 查看应用服务日志
docker-compose -f docker-compose-app.yaml logs api | grep -i error
```

#### 4.2 数据库初始化
如果是首次部署，确保数据库已正确初始化：
```bash
# 查看 API 服务日志，确认数据库迁移成功
docker-compose -f docker-compose-app.yaml logs api | grep -i migration
```

### 5. 维护操作

#### 5.1 备份数据
```bash
# 备份 PostgreSQL 数据库
docker exec <postgres_container_name> pg_dump -U postgres dify > dify_backup.sql

# 备份 Redis 数据
docker exec <redis_container_name> redis-cli BGSAVE
```

#### 5.2 更新服务
```bash
# 更新应用服务
docker-compose -f docker-compose-app.yaml pull
docker-compose -f docker-compose-app.yaml up -d

# 更新数据服务（谨慎操作）
docker-compose -f docker-compose-data.yaml pull
docker-compose -f docker-compose-data.yaml up -d
```

### 6. 扩展配置

#### 6.1 负载均衡
如果需要部署多个应用服务实例，可以：
1. 在多台服务器上部署应用服务
2. 在前端配置负载均衡器（如 HAProxy、Nginx）
3. 确保所有应用服务器都能访问同一个数据库服务器

#### 6.2 高可用数据库
考虑配置 PostgreSQL 和 Redis 的高可用方案：
- PostgreSQL: 主从复制、流复制
- Redis: 哨兵模式、集群模式

## 总结

通过这种分离部署方式，您可以：
- 独立扩展应用服务和数据服务
- 提高系统的可维护性
- 更好地进行资源分配和管理
- 提高系统的可用性和性能

如有问题，请检查日志文件并根据错误信息进行排查。
