# ==================================================================
# Dify 数据服务环境变量配置
# 用于数据库、缓存和向量数据库等数据相关服务
# ==================================================================

# ------------------------------
# PostgreSQL 数据库配置
# ------------------------------
POSTGRES_USER=postgres
POSTGRES_PASSWORD=difyai123456
POSTGRES_DB=dify
PGDATA=/var/lib/postgresql/data/pgdata

# PostgreSQL 性能配置
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_SHARED_BUFFERS=128MB
POSTGRES_WORK_MEM=4MB
POSTGRES_MAINTENANCE_WORK_MEM=64MB
POSTGRES_EFFECTIVE_CACHE_SIZE=4096MB

# ------------------------------
# Redis 配置
# ------------------------------
REDIS_PASSWORD=difyai123456

# ------------------------------
# 向量数据库配置
# ------------------------------
# Milvus 配置
ETCD_AUTO_COMPACTION_MODE=revision
ETCD_AUTO_COMPACTION_RETENTION=1000
ETCD_QUOTA_BACKEND_BYTES=4294967296
ETCD_SNAPSHOT_COUNT=50000

MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
ETCD_ENDPOINTS=etcd:2379
MINIO_ADDRESS=minio:9000

MILVUS_AUTHORIZATION_ENABLED=true

# ------------------------------
# 端口暴露配置
# 这些端口将暴露给外部，供应用服务连接
# ------------------------------
EXPOSE_DB_PORT=5432
EXPOSE_REDIS_PORT=6379
EXPOSE_WEAVIATE_PORT=8080
EXPOSE_QDRANT_PORT=6333
EXPOSE_PGVECTOR_PORT=5433
# Milvus 相关端口
EXPOSE_MILVUS_PORT=19530
EXPOSE_MILVUS_WEB_PORT=9091
EXPOSE_MINIO_PORT=9001
EXPOSE_MINIO_API_PORT=9000

# ------------------------------
# Docker Compose 配置
# ------------------------------
# 根据使用的向量数据库类型设置 profiles
# 可选值: weaviate, qdrant, pgvector, milvus 等
COMPOSE_PROFILES=milvus
